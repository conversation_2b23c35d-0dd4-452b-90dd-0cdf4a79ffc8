/*  -*-c++-*-
 *  Copyright (C) 2008 <PERSON><PERSON> <<EMAIL>>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSGANIMATION_CUBIC_BEZIER
#define OSGANIMATION_CUBIC_BEZIER 1

#include <osg/Vec2>
#include <osg/Vec3>
#include <osg/Vec4>

namespace osgAnimation
{

    template <class T>
    class TemplateCubicBezier
    {
    public:
        TemplateCubicBezier() : _position(osg::default_value<T>()), _controlPointIn(osg::default_value<T>()), _controlPointOut(osg::default_value<T>()) {}

        TemplateCubicBezier(const T& p, const T& i, const T& o) : _position(p), _controlPointIn(i), _controlPointOut(o)
        {
        }

        // Constructor with value only
        TemplateCubicBezier(const T& p) : _position(p), _controlPointIn(p), _controlPointOut(p)
        {
        }

        const T& getPosition() const { return _position;}
        const T& getControlPointIn() const { return _controlPointIn;}
        const T& getControlPointOut() const { return _controlPointOut;}

        T& getPosition() { return _position;}
        T& getControlPointIn() { return _controlPointIn;}
        T& getControlPointOut() { return _controlPointOut;}

        void setPosition(const T& v) {_position = v;}
        void setControlPointIn(const T& v) {_controlPointIn = v;}
        void setControlPointOut(const T& v) {_controlPointOut = v;}

        bool operator==(const TemplateCubicBezier<T>& other) const
        { return _position == other._position && _controlPointIn == other._controlPointIn && _controlPointOut == other._controlPointOut; }

        // steaming operators.
        friend std::ostream& operator << (std::ostream& output, const TemplateCubicBezier<T>& tcb)
        {
            output << tcb._position << " "
                   << tcb._controlPointIn << " "
                   << tcb._controlPointOut;
            return output; // to enable cascading
        }

        friend std::istream& operator >> (std::istream& input, TemplateCubicBezier<T>& tcb)
        {
            input >> tcb._position >> tcb._controlPointIn >> tcb._controlPointOut;
            return input;
        }

    protected:
        T _position, _controlPointIn, _controlPointOut;
    };

    typedef TemplateCubicBezier<float> FloatCubicBezier;
    typedef TemplateCubicBezier<double> DoubleCubicBezier;
    typedef TemplateCubicBezier<osg::Vec2> Vec2CubicBezier;
    typedef TemplateCubicBezier<osg::Vec3> Vec3CubicBezier;
    typedef TemplateCubicBezier<osg::Vec4> Vec4CubicBezier;

}

#endif

/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
 *
 * ViewDependentShadow codes Copyright (C) 2008 W<PERSON><PERSON><PERSON><PERSON>
 * Thanks to to my company http://www.ai.com.pl for allowing me free this work.
*/


#ifndef OSGSHADOW_MINIMALDRAWBOUNDSSHADOWMAP
#define OSGSHADOW_MINIMALDRAWBOUNDSSHADOWMAP 1

#include <osgShadow/MinimalShadowMap>

namespace osgShadow {

class OSGSHADOW_EXPORT MinimalDrawBoundsShadowMap
    : public MinimalShadowMap
{
    public :
        /** Convenient typedef used in definition of ViewData struct and methods */
        typedef MinimalDrawBoundsShadowMap ThisClass;
        /** Convenient typedef used in definition of ViewData struct and methods */
        typedef MinimalShadowMap           BaseClass;

        /** Classic OSG constructor */
        MinimalDrawBoundsShadowMap();

        /** Classic OSG cloning constructor */
        MinimalDrawBoundsShadowMap(
            const MinimalDrawBoundsShadowMap& mdbsm,
            const osg::CopyOp& copyop=osg::CopyOp::SHALLOW_COPY);

        /** Declaration of standard OSG object methods */
        META_Object( osgShadow, MinimalDrawBoundsShadowMap );

    protected:
        /** Classic protected OSG destructor */
        virtual ~MinimalDrawBoundsShadowMap(void);

        struct OSGSHADOW_EXPORT ViewData: public BaseClass::ViewData
        {
            osg::ref_ptr< osg::RefMatrix >       _projection;
            osg::Vec2s                           _boundAnalysisSize;
            osg::ref_ptr< osg::Image >           _boundAnalysisImage;
            osg::ref_ptr< osg::Texture2D >       _boundAnalysisTexture;
            osg::ref_ptr< osg::Camera >          _boundAnalysisCamera;
            osg::observer_ptr< osg::Camera >     _mainCamera;

            void setShadowCameraProjectionMatrixPtr( osg::RefMatrix * projection )
            { _projection = projection; }

            osg::RefMatrix * getShadowCameraProjectionMatrixPtr( void )
            { return _projection.get(); }

            virtual void init( ThisClass * st, osgUtil::CullVisitor * cv );

            virtual void cullShadowReceivingScene( );

            virtual void createDebugHUD( );

            virtual void recordShadowMapParams( );

            virtual void cullBoundAnalysisScene( );

            static osg::BoundingBox scanImage( const osg::Image * image, osg::Matrix m );

            virtual void performBoundAnalysis( const osg::Camera& camera );

            ViewData( void ): _boundAnalysisSize( 64, 64 ) {}

            virtual void resizeGLObjectBuffers(unsigned int maxSize);
            virtual void releaseGLObjects(osg::State* = 0) const;
        };

        friend struct ViewData;

        META_ViewDependentShadowTechniqueData( ThisClass, ThisClass::ViewData );


        struct CameraPostDrawCallback : public osg::Camera::DrawCallback {

            CameraPostDrawCallback( ViewData * vd ): _vd( vd )
            {
            }

            virtual void operator ()( const osg::Camera& camera ) const
            {
                if( _vd.valid() )
                    _vd->performBoundAnalysis( camera );
            }

            osg::observer_ptr< ViewData > _vd;
        };

        struct CameraCullCallback: public osg::Callback {

            CameraCullCallback(ViewData * vd, osg::Callback * nc): _vd(vd), _nc(nc)
            {
            }

            virtual bool run(osg::Object* object, osg::Object* data)
            {
                osgUtil::CullVisitor *cv = dynamic_cast< osgUtil::CullVisitor *>( data );

                if( _nc.valid() )
                    _nc->run(object, data);
                else
                    traverse(object, data);

                if( cv )
                    _vd->recordShadowMapParams( );

                return true;
            }

        protected:
            osg::observer_ptr< ViewData >     _vd;
            osg::ref_ptr< osg::Callback >       _nc;
        };
};

} // namespace osgShadow

#endif

/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/
#ifndef OSGUTIL_REFLECTIONMAPGENERATOR_
#define OSGUTIL_REFLECTIONMAPGENERATOR_

#include <osgUtil/CubeMapGenerator>

namespace osgUtil
{

    /** This is the most simple cube map generator. It performs a direct association
        between reflection vector and RGBA color (C = R).
    */
    class ReflectionMapGenerator: public CubeMapGenerator {
    public:
        inline ReflectionMapGenerator(int texture_size = 64);
        inline ReflectionMapGenerator(const ReflectionMapGenerator &copy, const osg::CopyOp &copyop = osg::CopyOp::SHALLOW_COPY);

    protected:
        virtual ~ReflectionMapGenerator() {}
        ReflectionMapGenerator &operator=(const ReflectionMapGenerator &) { return *this; }

        inline virtual osg::Vec4 compute_color(const osg::Vec3 &R) const;
    };

    // INLINE METHODS

    inline ReflectionMapGenerator::ReflectionMapGenerator(int texture_size)
        : CubeMapGenerator(texture_size)
    {
    }

    inline ReflectionMapGenerator::ReflectionMapGenerator(const ReflectionMapGenerator &copy, const osg::CopyOp &copyop)
        : CubeMapGenerator(copy, copyop)
    {
    }

    inline osg::Vec4 ReflectionMapGenerator::compute_color(const osg::Vec3 &R) const
    {
        return vector_to_color(R / R.length());
    }

}

#endif

/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSG_OBJECT
#define OSG_OBJECT 1

#include <osg/Referenced>
#include <osg/CopyOp>
#include <osg/ref_ptr>
#include <osg/Notify>

#include <string>
#include <vector>

namespace osg {

// forward declare
class UserDataContainer;
class Node;
class NodeVisitor;
class StateAttribute;
class Uniform;
class Drawable;
class Camera;
class Callback;
class CallbackObject;
class ValueObject;

#define _ADDQUOTES(def) #def
#define ADDQUOTES(def) _ADDQUOTES(def)

/** META_Object macro define the standard clone, isSameKindAs and className methods.
  * Use when subclassing from Object to make it more convenient to define
  * the standard pure virtual clone, isSameKindAs and className methods
  * which are required for all Object subclasses.*/
#define META_Object(library,name) \
        virtual osg::Object* cloneType() const { return new name (); } \
        virtual osg::Object* clone(const osg::CopyOp& copyop) const { return new name (*this,copyop); } \
        virtual bool isSameKindAs(const osg::Object* obj) const { return dynamic_cast<const name *>(obj)!=NULL; } \
        virtual const char* libraryName() const { return #library; }\
        virtual const char* className() const { return #name; }

/** Helper macro that creates a static proxy object to call singleton function on it's construction, ensuring that the singleton gets initialized at startup.*/
#define OSG_INIT_SINGLETON_PROXY(ProxyName, Func) static struct ProxyName{ ProxyName() { Func; } } s_##ProxyName;

/** Base class/standard interface for objects which require IO support,
    cloning and reference counting.
    Based on GOF Composite, Prototype and Template Method patterns.
*/
class OSG_EXPORT Object : public Referenced
{
    public:


        /** Construct an object. Note Object is a pure virtual base class
            and therefore cannot be constructed on its own, only derived
            classes which override the clone and className methods are
            concrete classes and can be constructed.*/
        inline Object():Referenced(),_dataVariance(UNSPECIFIED), _userDataContainer(0) {}

        inline explicit Object(bool threadSafeRefUnref):Referenced(threadSafeRefUnref),_dataVariance(UNSPECIFIED),_userDataContainer(0) {}

        /** Copy constructor, optional CopyOp object can be used to control
          * shallow vs deep copying of dynamic data.*/
        Object(const Object&,const CopyOp& copyop=CopyOp::SHALLOW_COPY);

        /** Clone the type of an object, with Object* return type.
            Must be defined by derived classes.*/
        virtual Object* cloneType() const = 0;

        /** Clone an object, with Object* return type.
            Must be defined by derived classes.*/
        virtual Object* clone(const CopyOp&) const = 0;

        virtual bool isSameKindAs(const Object*) const { return true; }


        /** return the name of the object's library. Must be defined
            by derived classes. The OpenSceneGraph convention is that the
            namespace of a library is the same as the library name.*/
        virtual const char* libraryName() const = 0;

        /** return the name of the object's class type. Must be defined
            by derived classes.*/
        virtual const char* className() const = 0;

        /** return the compound class name that combines the library name and class name.*/
        std::string getCompoundClassName() const { return std::string(libraryName()) + std::string("::") + std::string(className()); }


        /** Convert 'this' into a Node pointer if Object is a Node, otherwise return 0.
          * Equivalent to dynamic_cast<Node*>(this).*/
        virtual Node* asNode() { return 0; }

        /** convert 'const this' into a const Node pointer if Object is a Node, otherwise return 0.
          * Equivalent to dynamic_cast<const Node*>(this).*/
        virtual const Node* asNode() const { return 0; }

        /** Convert 'this' into a NodeVisitor pointer if Object is a NodeVisitor, otherwise return 0.
          * Equivalent to dynamic_cast<NodeVisitor*>(this).*/
        virtual NodeVisitor* asNodeVisitor() { return 0; }

        /** convert 'const this' into a const NodeVisitor pointer if Object is a NodeVisitor, otherwise return 0.
          * Equivalent to dynamic_cast<const NodeVisitor*>(this).*/
        virtual const NodeVisitor* asNodeVisitor() const { return 0; }

        /** Convert 'this' into a StateSet pointer if Object is a StateSet, otherwise return 0.
          * Equivalent to dynamic_cast<StateSet*>(this).*/
        virtual StateSet* asStateSet() { return 0; }

        /** convert 'const this' into a const StateSet pointer if Object is a StateSet, otherwise return 0.
          * Equivalent to dynamic_cast<const StateSet*>(this).*/
        virtual const StateSet* asStateSet() const { return 0; }

        /** Convert 'this' into a StateAttribute pointer if Object is a StateAttribute, otherwise return 0.
          * Equivalent to dynamic_cast<StateAttribute*>(this).*/
        virtual StateAttribute* asStateAttribute() { return 0; }

        /** convert 'const this' into a const StateAttribute pointer if Object is a StateAttribute, otherwise return 0.
          * Equivalent to dynamic_cast<const StateAttribute*>(this).*/
        virtual const StateAttribute* asStateAttribute() const { return 0; }

        /** Convert 'this' into a Uniform pointer if Object is a Uniform, otherwise return 0.
          * Equivalent to dynamic_cast<Uniform*>(this).*/
        virtual Uniform* asUniform() { return 0; }

        /** convert 'const this' into a const Uniform pointer if Object is a Uniform, otherwise return 0.
          * Equivalent to dynamic_cast<const UniformBase*>(this).*/
        virtual const UniformBase* asUniformBase() const { return 0; }

        /** Convert 'this' into a Uniform pointer if Object is a Uniform, otherwise return 0.
          * Equivalent to dynamic_cast<UniformBase*>(this).*/
        virtual UniformBase* asUniformBase() { return 0; }

        /** convert 'const this' into a const Uniform pointer if Object is a Uniform, otherwise return 0.
          * Equivalent to dynamic_cast<const Uniform*>(this).*/
        virtual const Uniform* asUniform() const { return 0; }

        /** Convert 'this' into a Camera pointer if Node is a Camera, otherwise return 0.
          * Equivalent to dynamic_cast<Camera*>(this).*/
        virtual Camera* asCamera() { return 0; }

        /** convert 'const this' into a const Camera pointer if Node is a Camera, otherwise return 0.
          * Equivalent to dynamic_cast<const Camera*>(this).*/
        virtual const Camera* asCamera() const { return 0; }

        /** Convert 'this' into a Drawable pointer if Object is a Drawable, otherwise return 0.
          * Equivalent to dynamic_cast<Drawable*>(this).*/
        virtual Drawable* asDrawable() { return 0; }

        /** convert 'const this' into a const Drawable pointer if Object is a Drawable, otherwise return 0.
          * Equivalent to dynamic_cast<const Drawable*>(this).*/
        virtual const Drawable* asDrawable() const { return 0; }

        /** Convert 'this' into a Callback pointer if Object is a Callback, otherwise return 0.
          * Equivalent to dynamic_cast<Callback*>(this).*/
        virtual Callback* asCallback() { return 0; }

        /** convert 'const this' into a const Callback pointer if Object is a Callback, otherwise return 0.
          * Equivalent to dynamic_cast<const Callback*>(this).*/
        virtual const Callback* asCallback() const { return 0; }

        /** Convert 'this' into a CallbackObject pointer if Object is a CallbackObject, otherwise return 0.
          * Equivalent to dynamic_cast<CallbackObject*>(this).*/
        virtual CallbackObject* asCallbackObject() { return 0; }

        /** convert 'const this' into a const CallbackObject pointer if Object is a CallbackObject, otherwise return 0.
          * Equivalent to dynamic_cast<const CallbackObject*>(this).*/
        virtual const CallbackObject* asCallbackObject() const { return 0; }

        /** Convert 'this' into a UserDataContainer pointer if Object is a UserDataContainer, otherwise return 0.
          * Equivalent to dynamic_cast<UserDataContainer*>(this).*/
        virtual UserDataContainer* asUserDataContainer() { return 0; }

        /** convert 'const this' into a const UserDataContainer pointer if Object is a UserDataContainer, otherwise return 0.
          * Equivalent to dynamic_cast<const UserDataContainer*>(this).*/
        virtual const UserDataContainer* asUserDataContainer() const { return 0; }

        /** Convert 'this' into a ValueObject pointer if Object is a ValueObject, otherwise return 0.
          * Equivalent to dynamic_cast<ValueObject*>(this).*/
        virtual ValueObject* asValueObject() { return 0; }

        /** Convert 'this' into a ValueObject pointer if Object is a ValueObject, otherwise return 0.
          * Equivalent to dynamic_cast<ValueObject*>(this).*/
        virtual const ValueObject* asValueObject() const { return 0; }

        /** Convert 'this' into a Image pointer if Object is a Image, otherwise return 0.
          * Equivalent to dynamic_cast<Image*>(this).*/
        virtual Image* asImage() { return 0; }

        /** Convert 'this' into a Image pointer if Object is a Image, otherwise return 0.
          * Equivalent to dynamic_cast<Image*>(this).*/
        virtual const Image* asImage() const { return 0; }



        /** Set whether to use a mutex to ensure ref() and unref() are thread safe.*/
        virtual void setThreadSafeRefUnref(bool threadSafe);

        /** Set the name of object using C++ style string.*/
        virtual void setName( const std::string& name ) { _name = name; }

        /** Set the name of object using a C style string.*/
        inline void setName( const char* name )
        {
            if (name) setName(std::string(name));
            else setName(std::string());
        }

        /** Get the name of object.*/
        inline const std::string& getName() const { return _name; }


        enum DataVariance
        {
            DYNAMIC,
            STATIC,
            UNSPECIFIED
        };

        /** Set the data variance of this object.
           * Can be set to either STATIC for values that do not change over the lifetime of the object,
           * or DYNAMIC for values that vary over the lifetime of the object. The DataVariance value
           * can be used by routines such as optimization codes that wish to share static data.
           * UNSPECIFIED is used to specify that the DataVariance hasn't been set yet. */
        inline void setDataVariance(DataVariance dv) { _dataVariance = dv; }

        /** Get the data variance of this object.*/
        inline DataVariance getDataVariance() const { return _dataVariance; }

        /** Compute the DataVariance based on an assessment of callback etc.*/
        virtual void computeDataVariance() {}


        /** set the UserDataContainer object.*/
        void setUserDataContainer(osg::UserDataContainer* udc);

        template<class T> void setUserDataContainer(const ref_ptr<T>& udc) { setUserDataContainer(udc.get()); }

        /** get the UserDataContainer attached to this object.*/
        osg::UserDataContainer* getUserDataContainer() { return _userDataContainer; }

        /** get the const UserDataContainer attached to this object.*/
        const osg::UserDataContainer* getUserDataContainer() const { return _userDataContainer; }

        /** Convenience method that returns the UserDataContainer, and if one doesn't already exist creates and assigns
         * a DefaultUserDataContainer to the Object and then return this new UserDataContainer.*/
        osg::UserDataContainer* getOrCreateUserDataContainer();


        /**
         * Set user data, data must be subclassed from Referenced to allow
         * automatic memory handling.  If your own data isn't directly
         * subclassed from Referenced then create an adapter object
         * which points to your own object and handles the memory addressing.
         */
        virtual void setUserData(Referenced* obj);

        template<class T> void setUserData(const ref_ptr<T>& ud) { setUserData(ud.get()); }

        /** Get user data.*/
        virtual Referenced* getUserData();

        /** Get const user data.*/
        virtual const Referenced* getUserData() const;



        /** Convenience method that casts the named UserObject to osg::TemplateValueObject<T> and gets the value.
          * To use this template method you need to include the osg/ValueObject header.*/
        template<typename T>
        bool getUserValue(const std::string& name, T& value) const;

        /** Convenience method that creates the osg::TemplateValueObject<T> to store the
          * specified value and adds it as a named UserObject.
          * To use this template method you need to include the osg/ValueObject header. */
        template<typename T>
        void setUserValue(const std::string& name, const T& value);


        /** Resize any per context GLObject buffers to specified size. */
        virtual void resizeGLObjectBuffers(unsigned int /*maxSize*/) {}

        /** If State is non-zero, this function releases any associated OpenGL objects for
           * the specified graphics context. Otherwise, releases OpenGL objects
           * for all graphics contexts. */
        virtual void releaseGLObjects(osg::State* = 0) const {}


    protected:

        /** Object destructor. Note, is protected so that Objects cannot
            be deleted other than by being dereferenced and the reference
            count being zero (see osg::Referenced), preventing the deletion
            of nodes which are still in use. This also means that
            Nodes cannot be created on stack i.e Node node will not compile,
            forcing all nodes to be created on the heap i.e Node* node
            = new Node().*/
        virtual ~Object();

        std::string _name;
        DataVariance _dataVariance;

        osg::UserDataContainer* _userDataContainer;

    private:

        /** disallow any copy operator.*/
        Object& operator = (const Object&) { return *this; }
};

template<typename T>
T* clone(const T* t, const osg::CopyOp& copyop=osg::CopyOp::SHALLOW_COPY)
{
    if (t)
    {
        osg::ref_ptr<osg::Object> obj = t->clone(copyop);

        T* ptr = dynamic_cast<T*>(obj.get());
        if (ptr)
        {
            obj.release();
            return ptr;
        }
        else
        {
            OSG_WARN<<"Warning: osg::clone(const T*, osg::CopyOp&) cloned object not of type T, returning NULL."<<std::endl;
            return 0;
        }
    }
    else
    {
        OSG_WARN<<"Warning: osg::clone(const T*, osg::CopyOp&) passed null object to clone, returning NULL."<<std::endl;
        return 0;
    }
}

template<typename T>
T* clone(const T* t, const std::string& name, const osg::CopyOp& copyop=osg::CopyOp::SHALLOW_COPY)
{
    T* newObject = osg::clone(t, copyop);
    if (newObject)
    {
        newObject->setName(name);
        return newObject;
    }
    else
    {
        OSG_WARN<<"Warning: osg::clone(const T*, const std::string&, const osg::CopyOp) passed null object to clone, returning NULL."<<std::endl;
        return 0;
    }
}

template<typename T>
T* cloneType(const T* t)
{
    if (t)
    {
        osg::ref_ptr<osg::Object> obj = t->cloneType();

        T* ptr = dynamic_cast<T*>(obj.get());
        if (ptr)
        {
            obj.release();
            return ptr;
        }
        else
        {
            OSG_WARN<<"Warning: osg::cloneType(const T*) cloned object not of type T, returning NULL."<<std::endl;
            return 0;
        }
    }
    else
    {
        OSG_WARN<<"Warning: osg::cloneType(const T*) passed null object to clone, returning NULL."<<std::endl;
        return 0;
    }
}

/** DummyObject that can be used as placeholder but otherwise has no other functionality.*/
class DummyObject : public osg::Object
{
public:
    DummyObject() {}
    DummyObject(const DummyObject& org, const CopyOp& copyop) :
        Object(org, copyop) {}

    META_Object(osg, DummyObject)
protected:
    virtual ~DummyObject() {}
};


inline void resizeGLObjectBuffers(osg::Object* object, unsigned int maxSize) { if (object) object->resizeGLObjectBuffers(maxSize); }

template<class T> void resizeGLObjectBuffers(const osg::ref_ptr<T>& object, unsigned int maxSize) { if (object.valid()) object->resizeGLObjectBuffers(maxSize); }

inline void releaseGLObjects(osg::Object* object, osg::State* state = 0) { if (object) object->releaseGLObjects(state); }

template<class T> void releaseGLObjects(const osg::ref_ptr<T>& object, osg::State* state = 0) { if (object.valid()) object->releaseGLObjects(state); }

}

#endif

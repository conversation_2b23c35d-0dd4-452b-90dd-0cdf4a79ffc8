/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/
//osgManipulator - Copyright (C) 2007 Fugro-Jason B.V.

#ifndef OSGMANIPULATOR_TRANSLATEPLANEDRAGGER
#define OSGMANIPULATOR_TRANSLATEPLANEDRAGGER 1

#include <osgManipulator/Translate2DDragger>
#include <osgManipulator/Translate1DDragger>

namespace osgManipulator {

/**
 * Translate plane dragger consists of a wireframe box representing a plane that can be dragged to translate along the plane.
 */
class OSGMANIPULATOR_EXPORT TranslatePlaneDragger : public CompositeDragger
{
    public:

        TranslatePlaneDragger();

        META_OSGMANIPULATOR_Object(osgManipulator,TranslatePlaneDragger)

        virtual bool handle(const PointerInfo& pi, const osgGA::GUIEventAdapter& ea, osgGA::GUIActionAdapter& us);

        /** Setup default geometry for dragger. */
        void setupDefaultGeometry();

        inline void setColor(const osg::Vec4& color) { if (_translate2DDragger.valid()) _translate2DDragger->setColor(color); }

        Translate1DDragger* getTranslate1DDragger() { return _translate1DDragger.get(); }
        Translate2DDragger* getTranslate2DDragger() { return _translate2DDragger.get(); }

    protected:

        virtual ~TranslatePlaneDragger();

        osg::ref_ptr< Translate2DDragger >      _translate2DDragger;
        osg::ref_ptr< Translate1DDragger >      _translate1DDragger;
        bool                                    _usingTranslate1DDragger;
};


}

#endif

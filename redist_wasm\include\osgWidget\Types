/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2008 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

// Code by: <PERSON> (cubicool) 2007-2008

#ifndef OSGWIDGET_TYPES
#define OSGWIDGET_TYPES

#include <numeric>
#include <osg/Geometry>

namespace osgWidget {

typedef osg::Vec2Array TexCoordArray;
typedef osg::Vec3Array PointArray;
typedef osg::Vec4Array ColorArray;

typedef TexCoordArray::value_type TexCoord;
typedef PointArray::value_type    Point;
typedef ColorArray::value_type    Color;

typedef TexCoord::value_type texcoord_type;
typedef Point::value_type    point_type;
typedef Color::value_type    color_type;

typedef osg::Vec2 XYCoord;
typedef osg::Vec4 Quad;

typedef osg::Matrix::value_type matrix_type;

// This is multiplied by a normalized Z value [0.0f, -1.0f] to create a RenderBin number
// to set the state of the Window/Widget with. Perhaps at some later time this should
// be configurable.
const int OSGWIDGET_RENDERBIN_MOD = 5000;

}

#endif

/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2008 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

// Code by: <PERSON> (cubicool) 2007-2008

#ifndef OSGWIDGET_SCRIPT_ENGINE
#define OSGWIDGET_SCRIPT_ENGINE

#include <osg/Referenced>

namespace osgWidget {

// An interface for our scripting API so that we can have a unified way to talk to both
// Lua and Python; perhaps even more! Furthermore, this will allow us to put the
// entire implementation into a source file...
class ScriptEngine: public osg::Referenced
{
    public:
        virtual bool initialize () { return false; }
        virtual bool close      () { return false; }
        virtual bool eval       (const std::string&) { return false; }
        virtual bool runFile    (const std::string&) { return false; }

        virtual const std::string& getLastErrorText() const { return _err; }
    protected:
        std::string _err;

};

}

#endif

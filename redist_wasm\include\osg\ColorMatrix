/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSG_COLORMATRIX
#define OSG_COLORMATRIX 1

#include <osg/Matrix>
#include <osg/StateAttribute>

namespace osg {

/** Encapsulates OpenGL color matrix functionality. */
class OSG_EXPORT ColorMatrix : public StateAttribute
{
    public :

        ColorMatrix();

        /** Copy constructor using CopyOp to manage deep vs shallow copy. */
        ColorMatrix(const ColorMatrix& cm,const CopyOp& copyop=CopyOp::SHALLOW_COPY):
            StateAttribute(cm,copyop),
            _matrix(cm._matrix) {}

        META_StateAttribute(osg, ColorMatrix, COLORMATRIX);

        /** Return -1 if *this < *rhs, 0 if *this==*rhs, 1 if *this>*rhs. */
        virtual int compare(const StateAttribute& sa) const
        {
            // Check for equal types, then create the rhs variable
            // used by the COMPARE_StateAttribute_Parameter macros below.
            COMPARE_StateAttribute_Types(ColorMatrix,sa)

            // Compare each parameter in turn against the rhs.
            COMPARE_StateAttribute_Parameter(_matrix)

            return 0; // Passed all the above comparison macros, so must be equal.
        }

        /** Sets the color matrix. */
        inline void setMatrix(const Matrix& matrix) { _matrix = matrix; }

        /** Gets the color matrix. */
        inline Matrix& getMatrix() { return _matrix; }

        /** Gets the const color matrix. */
        inline const Matrix& getMatrix() const { return _matrix; }

        /** Applies as OpenGL texture matrix. */
        virtual void apply(State& state) const;

    protected:

        virtual ~ColorMatrix( void );

        Matrix _matrix;

};

}


#endif

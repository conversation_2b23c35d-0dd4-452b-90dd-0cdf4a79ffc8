/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/
#ifndef OSGUTIL_HALFWAYMAPGENERATOR_
#define OSGUTIL_HALFWAYMAPGENERATOR_

#include <osgUtil/Export>

#include <osgUtil/CubeMapGenerator>

namespace osgUtil
{

    /** This cube map generator produces an Half-way vector map, useful for
      * hardware-based specular lighting effects.
      * It computes: C = normalize(R - L), where C is the resulting color,
      * R is the reflection vector and L is the light direction.
      */
    class OSGUTIL_EXPORT HalfWayMapGenerator: public CubeMapGenerator {
    public:
        HalfWayMapGenerator(const osg::Vec3 &light_direction, int texture_size = 64);
        HalfWayMapGenerator(const HalfWayMapGenerator &copy, const osg::CopyOp &copyop);

    protected:
        virtual ~HalfWayMapGenerator() {}
        HalfWayMapGenerator &operator=(const HalfWayMapGenerator &) { return *this; }

        inline virtual osg::Vec4 compute_color(const osg::Vec3 &R) const;

    private:
        osg::Vec3 ldir_;
    };

    // INLINE METHODS

    inline osg::Vec4 HalfWayMapGenerator::compute_color(const osg::Vec3 &R) const
    {
        const osg::Vec3 V = (R / R.length()) - ldir_;
        return vector_to_color(V / V.length());
    }

}

#endif

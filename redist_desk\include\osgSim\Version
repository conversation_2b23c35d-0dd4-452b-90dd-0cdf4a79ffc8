/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSGSIM_VERSION
#define OSGSIM_VERSION 1

#include <osgSim/Export>

extern "C" {

/**
 * osgSimGetVersion() returns the library version number.
 * Numbering convention : OpenSceneGraph-1.0 will return 1.0 from osgSimGetVersion.
 *
 * This C function can be also used to check for the existence of the OpenSceneGraph
 * library using autoconf and its m4 macro AC_CHECK_LIB.
 *
 * Here is the code to add to your configure.in:
 \verbatim
 #
 # Check for the OpenSceneGraph (OSG) Sim library
 #
 AC_CHECK_LIB(osg, osgSimGetVersion, ,
    [AC_MSG_ERROR(OpenSceneGraph Sim library not found. See http://www.openscenegraph.org)],)
 \endverbatim
*/
extern OSGSIM_EXPORT const char* osgSimGetVersion();

/**
 * osgSimGetLibraryName() returns the library name in human friendly form.
*/
extern OSGSIM_EXPORT const char* osgSimGetLibraryName();

}

#endif

/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSGUTIL_SCENEVIEW
#define OSGUTIL_SCENEVIEW 1

#include <osg/Node>
#include <osg/StateSet>
#include <osg/Light>
#include <osg/FrameStamp>
#include <osg/DisplaySettings>
#include <osg/CollectOccludersVisitor>
#include <osg/CullSettings>
#include <osg/Camera>

#include <osgUtil/CullVisitor>

namespace osgUtil {

/**
 * SceneView is deprecated, and is now just kept for backwards compatibility.
 * It is recommend that you use osgViewer::Viewer/Composite in combination
 * with osgViewer::GraphicsWindowEmbedded for embedded rendering support as
 * this provides a greater range of functionality and consistency of API.
*/
class OSGUTIL_EXPORT SceneView : public osg::Object, public osg::CullSettings
{
    public:

        /** Construct a default scene view.*/
        SceneView(osg::DisplaySettings* ds=NULL);

        SceneView(const SceneView& sceneview, const osg::CopyOp& copyop = osg::CopyOp());

        META_Object(osgUtil, SceneView);

        enum Options
        {
            NO_SCENEVIEW_LIGHT = 0x0,
            HEADLIGHT = 0x1,
            SKY_LIGHT = 0x2,
            COMPILE_GLOBJECTS_AT_INIT = 0x4,
            APPLY_GLOBAL_DEFAULTS = 0x8,
            CLEAR_GLOBAL_STATESET = 0x10,
            STANDARD_SETTINGS = HEADLIGHT |
                                COMPILE_GLOBJECTS_AT_INIT |
                                APPLY_GLOBAL_DEFAULTS |
                                CLEAR_GLOBAL_STATESET

        };

        /* Set defaults. */
        virtual void setDefaults() { setDefaults(STANDARD_SETTINGS); }

        /** Set scene view to use default global state, light, camera
         *  and render visitor.
         */
        virtual void setDefaults(unsigned int options);

        /** Set the camera used to represent the camera view of this SceneView.*/
        void setCamera(osg::Camera* camera, bool assumeOwnershipOfCamera = true);

        /** Get the camera used to represent the camera view of this SceneView.*/
        osg::Camera* getCamera() { return _camera.get(); }

        /** Get the const camera used to represent the camera view of this SceneView.*/
        const osg::Camera* getCamera() const { return _camera.get(); }

        /** Set the data to view. The data will typically be
         *  an osg::Scene but can be any osg::Node type.
         */
        void setSceneData(osg::Node* node);

        /** Get the scene data to view. The data will typically be
         *  an osg::Scene but can be any osg::Node type.
         */
        osg::Node* getSceneData(unsigned int childNo=0) { return (_camera->getNumChildren()>childNo) ? _camera->getChild(childNo) : 0; }

        /** Get the const scene data which to view. The data will typically be
         *  an osg::Scene but can be any osg::Node type.
         */
        const osg::Node* getSceneData(unsigned int childNo=0) const { return (_camera->getNumChildren()>childNo) ? _camera->getChild(childNo) : 0; }

        /** Get the number of scene data subgraphs added to the SceneView's camera.*/
        unsigned int getNumSceneData() const { return _camera->getNumChildren(); }

        /** Set the viewport of the scene view to use specified osg::Viewport. */
        void setViewport(osg::Viewport* viewport) { _camera->setViewport(viewport); }

        /** Set the viewport of the scene view to specified dimensions. */
        void setViewport(int x,int y,int width,int height) { _camera->setViewport(x,y,width,height); }


        /** Get the viewport. */
        osg::Viewport* getViewport() { return (_camera->getViewport()!=0) ? _camera->getViewport() : 0; }

        /** Get the const viewport. */
        const osg::Viewport* getViewport() const { return (_camera->getViewport()!=0) ? _camera->getViewport() : 0; }

        /** Set the DisplaySettings. */
        inline void setDisplaySettings(osg::DisplaySettings* vs) { _displaySettings = vs; }

        /** Get the const DisplaySettings */
        inline const osg::DisplaySettings* getDisplaySettings() const { return _displaySettings.get(); }

        /** Get the DisplaySettings */
        inline osg::DisplaySettings* getDisplaySettings() { return _displaySettings.get(); }


        /** Set the color used in glClearColor().
            Defaults to an off blue color.*/
        void setClearColor(const osg::Vec4& color) { _camera->setClearColor(color); }

        /** Get the color used in glClearColor.*/
        const osg::Vec4& getClearColor() const { return _camera->getClearColor(); }


        /** Manually set the redraw interlaced stereo stencil mask request flag to control whether to redraw the stencil buffer on the next frame.*/
        void setRedrawInterlacedStereoStencilMask(bool flag) { _redrawInterlacedStereoStencilMask = flag; }

        /** Get the redraw interlaced stereo stencil mask request flag.*/
        bool getRedrawInterlacedStereoStencilMask() const { return _redrawInterlacedStereoStencilMask; }


        void setGlobalStateSet(osg::StateSet* state) { _globalStateSet = state; }
        osg::StateSet* getGlobalStateSet() { return _globalStateSet.get(); }
        const osg::StateSet* getGlobalStateSet() const { return _globalStateSet.get(); }

        void setSecondaryStateSet(osg::StateSet* state) { _secondaryStateSet = state; }
        osg::StateSet* getSecondaryStateSet() { return _secondaryStateSet.get(); }
        const osg::StateSet* getSecondaryStateSet() const { return _secondaryStateSet.get(); }

        void setLocalStateSet(osg::StateSet* state) { _localStateSet = state; }
        osg::StateSet* getLocalStateSet() { return _localStateSet.get(); }
        const osg::StateSet* getLocalStateSet() const { return _localStateSet.get(); }

        enum ActiveUniforms
        {
            FRAME_NUMBER_UNIFORM            = 1,
            FRAME_TIME_UNIFORM              = 2,
            DELTA_FRAME_TIME_UNIFORM        = 4,
            SIMULATION_TIME_UNIFORM         = 8,
            DELTA_SIMULATION_TIME_UNIFORM   = 16,
            VIEW_MATRIX_UNIFORM             = 32,
            VIEW_MATRIX_INVERSE_UNIFORM     = 64,
            DEFAULT_UNIFORMS                = FRAME_NUMBER_UNIFORM |
                                              FRAME_TIME_UNIFORM |
                                              DELTA_FRAME_TIME_UNIFORM |
                                              SIMULATION_TIME_UNIFORM |
                                              DELTA_SIMULATION_TIME_UNIFORM |
                                              VIEW_MATRIX_UNIFORM |
                                              VIEW_MATRIX_INVERSE_UNIFORM,
            ALL_UNIFORMS                    = 0x7FFFFFFF
        };

        /** Set the uniforms that SceneView should set set up on each frame.*/
        void setActiveUniforms(int activeUniforms) { _activeUniforms = activeUniforms; }

        /** Get the uniforms that SceneView should set set up on each frame.*/
        int getActiveUniforms() const { return _activeUniforms; }

        void updateUniforms();


        typedef Options LightingMode;

        void setLightingMode(LightingMode mode);
        LightingMode getLightingMode() const { return _lightingMode; }

        void setLight(osg::Light* light) { _light = light; }
        osg::Light* getLight() { return _light.get(); }
        const osg::Light* getLight() const { return _light.get(); }

        void setState(osg::State* state) { _renderInfo.setState(state); }
        osg::State* getState() { return _renderInfo.getState(); }
        const osg::State* getState() const { return _renderInfo.getState(); }

        void setView(osg::View* view) { _camera->setView(view); }
        osg::View* getView() { return _camera->getView(); }
        const osg::View* getView() const { return _camera->getView(); }

        void setRenderInfo(osg::RenderInfo& renderInfo) { _renderInfo = renderInfo; }
        osg::RenderInfo& getRenderInfo() { return _renderInfo; }
        const osg::RenderInfo& getRenderInfo() const { return _renderInfo; }



        /** Set the projection matrix. Can be thought of as setting the lens of a camera. */
        inline void setProjectionMatrix(const osg::Matrixf& matrix) { _camera->setProjectionMatrix(matrix); }

        /** Set the projection matrix. Can be thought of as setting the lens of a camera. */
        inline void setProjectionMatrix(const osg::Matrixd& matrix) { _camera->setProjectionMatrix(matrix); }

        /** Set to an orthographic projection. See OpenGL glOrtho documentation for further details.*/
        void setProjectionMatrixAsOrtho(double left, double right,
                                        double bottom, double top,
                                        double zNear, double zFar);

        /** Set to a 2D orthographic projection. See OpenGL glOrtho2D documentation for further details.*/
        void setProjectionMatrixAsOrtho2D(double left, double right,
                                          double bottom, double top);

        /** Set to a perspective projection. See OpenGL glFrustum documentation for further details.*/
        void setProjectionMatrixAsFrustum(double left, double right,
                                          double bottom, double top,
                                          double zNear, double zFar);

        /** Create a symmetrical perspective projection, See OpenGL gluPerspective documentation for further details.
          * Aspect ratio is defined as width/height.*/
        void setProjectionMatrixAsPerspective(double fovy,double aspectRatio,
                                              double zNear, double zFar);

        /** Get the projection matrix.*/
        osg::Matrixd& getProjectionMatrix() { return _camera->getProjectionMatrix(); }

        /** Get the const projection matrix.*/
        const osg::Matrixd& getProjectionMatrix() const { return _camera->getProjectionMatrix(); }

        /** Get the orthographic settings of the orthographic projection matrix.
          * Returns false if matrix is not an orthographic matrix, where parameter values are undefined.*/
        bool getProjectionMatrixAsOrtho(double& left, double& right,
                                        double& bottom, double& top,
                                        double& zNear, double& zFar) const;

        /** Get the frustum setting of a perspective projection matrix.
          * Returns false if matrix is not a perspective matrix, where parameter values are undefined.*/
        bool getProjectionMatrixAsFrustum(double& left, double& right,
                                          double& bottom, double& top,
                                          double& zNear, double& zFar) const;

        /** Get the frustum setting of a symmetric perspective projection matrix.
          * Returns false if matrix is not a perspective matrix, where parameter values are undefined.
          * Note, if matrix is not a symmetric perspective matrix then the shear will be lost.
          * Asymmetric matrices occur when stereo, power walls, caves and reality center display are used.
          * In these configurations one should use the 'getProjectionMatrixAsFrustum' method instead.*/
        bool getProjectionMatrixAsPerspective(double& fovy,double& aspectRatio,
                                              double& zNear, double& zFar) const;


        /** Set the view matrix. Can be thought of as setting the position of the world relative to the camera in camera coordinates. */
        inline void setViewMatrix(const osg::Matrixf& matrix) { _camera->setViewMatrix(matrix); }

        /** Set the view matrix. Can be thought of as setting the position of the world relative to the camera in camera coordinates. */
        inline void setViewMatrix(const osg::Matrixd& matrix) { _camera->setViewMatrix(matrix); }

        /** Set the position and orientation components of the view matrix, using the same convention as gluLookAt. */
        void setViewMatrixAsLookAt(const osg::Vec3& eye,const osg::Vec3& center,const osg::Vec3& up);

        /** Get the view matrix. */
        osg::Matrixd& getViewMatrix() { return _camera->getViewMatrix(); }

        /** Get the const view matrix. */
        const osg::Matrixd& getViewMatrix() const { return _camera->getViewMatrix(); }

        /** Get the position and orientation components of a modelview matrix, using the same convention as gluLookAt. */
        void getViewMatrixAsLookAt(osg::Vec3& eye,osg::Vec3& center,osg::Vec3& up,float lookDistance=1.0f) const;




        void setInitVisitor(osg::NodeVisitor* av) { _initVisitor = av; }
        osg::NodeVisitor* getInitVisitor() { return _initVisitor.get(); }
        const osg::NodeVisitor* getInitVisitor() const { return _initVisitor.get(); }


        void setUpdateVisitor(osg::NodeVisitor* av) { _updateVisitor = av; }
        osg::NodeVisitor* getUpdateVisitor() { return _updateVisitor.get(); }
        const osg::NodeVisitor* getUpdateVisitor() const { return _updateVisitor.get(); }


        void setCullVisitor(osgUtil::CullVisitor* cv) { _cullVisitor = cv; }
        osgUtil::CullVisitor* getCullVisitor() { return _cullVisitor.get(); }
        const osgUtil::CullVisitor* getCullVisitor() const { return _cullVisitor.get(); }

        void setCullVisitorLeft(osgUtil::CullVisitor* cv) { _cullVisitorLeft = cv; }
        osgUtil::CullVisitor* getCullVisitorLeft() { return _cullVisitorLeft.get(); }
        const osgUtil::CullVisitor* getCullVisitorLeft() const { return _cullVisitorLeft.get(); }

        void setCullVisitorRight(osgUtil::CullVisitor* cv) { _cullVisitorRight = cv; }
        osgUtil::CullVisitor* getCullVisitorRight() { return _cullVisitorRight.get(); }
        const osgUtil::CullVisitor* getCullVisitorRight() const { return _cullVisitorRight.get(); }

        void setCollectOccludersVisitor(osg::CollectOccludersVisitor* cov) { _collectOccludersVisitor = cov; }
        osg::CollectOccludersVisitor* getCollectOccludersVisitor() { return _collectOccludersVisitor.get(); }
        const osg::CollectOccludersVisitor* getCollectOccludersVisitor() const { return _collectOccludersVisitor.get(); }


        void setStateGraph(osgUtil::StateGraph* rg) { _stateGraph = rg; }
        osgUtil::StateGraph* getStateGraph() { return _stateGraph.get(); }
        const osgUtil::StateGraph* getStateGraph() const { return _stateGraph.get(); }

        void setStateGraphLeft(osgUtil::StateGraph* rg) { _stateGraphLeft = rg; }
        osgUtil::StateGraph* getStateGraphLeft() { return _stateGraphLeft.get(); }
        const osgUtil::StateGraph* getStateGraphLeft() const { return _stateGraphLeft.get(); }

        void setStateGraphRight(osgUtil::StateGraph* rg) { _stateGraphRight = rg; }
        osgUtil::StateGraph* getStateGraphRight() { return _stateGraphRight.get(); }
        const osgUtil::StateGraph* getStateGraphRight() const { return _stateGraphRight.get(); }


        void setRenderStage(osgUtil::RenderStage* rs) { _renderStage = rs; }
        osgUtil::RenderStage* getRenderStage() { return _renderStage.get(); }
        const osgUtil::RenderStage* getRenderStage() const { return _renderStage.get(); }

        void setRenderStageLeft(osgUtil::RenderStage* rs) { _renderStageLeft = rs; }
        osgUtil::RenderStage* getRenderStageLeft() { return _renderStageLeft.get(); }
        const osgUtil::RenderStage* getRenderStageLeft() const { return _renderStageLeft.get(); }

        void setRenderStageRight(osgUtil::RenderStage* rs) { _renderStageRight = rs; }
        osgUtil::RenderStage* getRenderStageRight() { return _renderStageRight.get(); }
        const osgUtil::RenderStage* getRenderStageRight() const { return _renderStageRight.get(); }

        /** search through any pre and post RenderStages that reference a Camera, and take a reference to each of these cameras to prevent them being deleted while they are still be used by the drawing thread.*/
        void collateReferencesToDependentCameras();

        /** clear the reference to any any dependent cameras.*/
        void clearReferencesToDependentCameras();


        /** Set the draw buffer value used at the start of each frame draw.  Note, overridden in quad buffer stereo mode */
        void setDrawBufferValue( GLenum drawBufferValue ) { _camera->setDrawBuffer(drawBufferValue); }

        /** Get the draw buffer value used at the start of each frame draw. */
        GLenum getDrawBufferValue() const { return _camera->getDrawBuffer(); }


        /** FusionDistanceMode is used only when working in stereo.*/
        enum FusionDistanceMode
        {
            /** Use fusion distance from the value set on the SceneView.*/
            USE_FUSION_DISTANCE_VALUE,
            /** Compute the fusion distance by multiplying the screen distance by the fusion distance value.*/
            PROPORTIONAL_TO_SCREEN_DISTANCE
        };

        /** Set the FusionDistanceMode and Value. Note, is used only when working in stereo.*/
        void setFusionDistance(FusionDistanceMode mode,float value=1.0f)
        {
            _fusionDistanceMode = mode;
            _fusionDistanceValue = value;
        }

        /** Get the FusionDistanceMode.*/
        FusionDistanceMode getFusionDistanceMode() const { return _fusionDistanceMode; }

        /** Get the FusionDistanceValue. Note, only used for USE_FUSION_DISTANCE_VALUE & PROPORTIONAL_TO_SCREEN_DISTANCE modes.*/
        float getFusionDistanceValue() const { return _fusionDistanceValue; }


        /** Set whether the draw method should call renderer->prioritizeTexture.*/
        void setPrioritizeTextures(bool pt) { _prioritizeTextures = pt; }

        /** Get whether the draw method should call renderer->prioritizeTexture.*/
        bool getPrioritizeTextures() const { return _prioritizeTextures; }

        /** Callback for overidding the default method for compute the offset projection and view matrices.*/
        struct ComputeStereoMatricesCallback : public osg::Referenced
        {
            virtual osg::Matrixd computeLeftEyeProjection(const osg::Matrixd& projection) const = 0;
            virtual osg::Matrixd computeLeftEyeView(const osg::Matrixd& view) const = 0;

            virtual osg::Matrixd computeRightEyeProjection(const osg::Matrixd& projection) const = 0;
            virtual osg::Matrixd computeRightEyeView(const osg::Matrixd& view) const = 0;
        };

        void setComputeStereoMatricesCallback(ComputeStereoMatricesCallback* callback) { _computeStereoMatricesCallback=callback; }
        ComputeStereoMatricesCallback* getComputeStereoMatricesCallback() { return _computeStereoMatricesCallback.get(); }
        const ComputeStereoMatricesCallback* getComputeStereoMatricesCallback() const { return _computeStereoMatricesCallback.get(); }

        /** Calculate the object coordinates of a point in window coordinates.
            Note, current implementation requires that SceneView::draw() has been previously called
            for projectWindowIntoObject to produce valid values. Consistent with OpenGL,
            window coordinates are calculated relative to the bottom left of the window.
            Returns true on successful projection.
        */
        bool projectWindowIntoObject(const osg::Vec3& window,osg::Vec3& object) const;

        /** Calculate the object coordinates of a window x,y when projected onto the near and far planes.
            Note, current implementation requires that SceneView::draw() has been previously called
            for projectWindowIntoObject to produce valid values. Consistent with OpenGL,
            window coordinates are calculated relative to the bottom left of the window.
            Returns true on successful projection.
        */
        bool projectWindowXYIntoObject(int x,int y,osg::Vec3& near_point,osg::Vec3& far_point) const;

        /** Calculate the window coordinates of a point in object coordinates.
            Note, current implementation requires that SceneView::draw() has been previously called
            for projectWindowIntoObject to produce valid values. Consistent with OpenGL,
            window coordinates are calculated relative to the bottom left of the window,
            whereas window API's normally have the top left as the origin,
            so you may need to pass in (mouseX,window_height-mouseY,...).
            Returns true on successful projection.
        */
        bool projectObjectIntoWindow(const osg::Vec3& object,osg::Vec3& window) const;


        /** Set the frame stamp for the current frame.*/
        inline void setFrameStamp(osg::FrameStamp* fs) { _frameStamp = fs; }

        /** Get the frame stamp for the current frame.*/
        inline osg::FrameStamp* getFrameStamp() { return _frameStamp.get(); }

        /** Get the const frame stamp for the current frame.*/
        inline const osg::FrameStamp* getFrameStamp() const { return _frameStamp.get(); }


        inline osg::Matrixd computeLeftEyeProjection(const osg::Matrixd& projection)  const
        {
            if (_computeStereoMatricesCallback.valid()) return _computeStereoMatricesCallback->computeLeftEyeProjection(projection);
            else return computeLeftEyeProjectionImplementation(projection);
        }

        inline osg::Matrixd computeLeftEyeView(const osg::Matrixd& view) const
        {
            if (_computeStereoMatricesCallback.valid()) return _computeStereoMatricesCallback->computeLeftEyeView(view);
            else return computeLeftEyeViewImplementation(view);
        }

        inline osg::Matrixd computeRightEyeProjection(const osg::Matrixd& projection)  const
        {
            if (_computeStereoMatricesCallback.valid()) return _computeStereoMatricesCallback->computeRightEyeProjection(projection);
            else return computeRightEyeProjectionImplementation(projection);
        }

        inline osg::Matrixd computeRightEyeView(const osg::Matrixd& view) const
        {
            if (_computeStereoMatricesCallback.valid()) return _computeStereoMatricesCallback->computeRightEyeView(view);
            else return computeRightEyeViewImplementation(view);
        }

        /** helper function for computing the left eye projection matrix.*/
        virtual osg::Matrixd computeLeftEyeProjectionImplementation(const osg::Matrixd& projection) const;

        /** helper function for computing the left eye view matrix.*/
        virtual osg::Matrixd computeLeftEyeViewImplementation(const osg::Matrixd& view) const;

        /** helper function for computing the right eye view matrix.*/
        virtual osg::Matrixd computeRightEyeProjectionImplementation(const osg::Matrixd& projection) const;

        /** helper function for computing the right eye view matrix.*/
        virtual osg::Matrixd computeRightEyeViewImplementation(const osg::Matrixd& view) const;


        /** Inherit the local cull settings variable from a specified CullSettings object, according to the inheritance mask.*/
        virtual void inheritCullSettings(const osg::CullSettings& settings) { inheritCullSettings(settings, _inheritanceMask); }

        /** Inherit the local cull settings variable from a specified CullSettings object, according to the inheritance mask.*/
        virtual void inheritCullSettings(const osg::CullSettings& settings, unsigned int inheritanceMask);


        /** Do init traversal of the attached scene graph using Init NodeVisitor.
          * The init traversal is called once for each SceneView, and should
          * be used to compile display lists, texture objects and initialize data
          * not otherwise initialized during scene graph loading. Note, is
          * called automatically by update & cull if it hasn't already been called
          * elsewhere. Also init() should only ever be called within a valid
          * graphics context.*/
        virtual void init();

        /** Do app traversal of the attached scene graph using App NodeVisitor.*/
        virtual void update();

        /** Do cull traversal of the attached scene graph using Cull NodeVisitor.*/
        virtual void cull();

        /** Do draw traversal of draw bins generated by cull traversal.*/
        virtual void draw();

        /** Compute the number of dynamic objects that will be held in the rendering backend */
        unsigned int getDynamicObjectCount() const { return _dynamicObjectCount; }

        /** Release all OpenGL objects from the scene graph, such as texture objects, display lists, etc.
          * These released scene graphs are placed in the respective delete GLObjects cache, and
          * then need to be deleted in OpenGL by SceneView::flushAllDeleteGLObjects(). */
        virtual void releaseAllGLObjects();

        virtual void resizeGLObjectBuffers(unsigned int maxSize);
        virtual void releaseGLObjects(osg::State* = 0) const;

        /** Flush all deleted OpenGL objects, such as texture objects, display lists, etc.*/
        virtual void flushAllDeletedGLObjects();

        /** Flush deleted OpenGL objects, such as texture objects, display lists, etc., within the specified available time.*/
        virtual void flushDeletedGLObjects(double& availableTime);

        /** Extract stats for current draw list. */
        bool getStats(Statistics& primStats);

        /** Set whether the SceneView should automatically call flushDeletedObjects() on each new frame.*/
        void setAutomaticFlush(bool automaticFlush) { _automaticFlush = automaticFlush; }
        bool getAutomaticFlush() const { return _automaticFlush; }

        void setResetColorMaskToAllOn(bool enable) { _resetColorMaskToAllEnabled = enable; }
        bool getResetColorMaskToAllOn() const { return _resetColorMaskToAllEnabled; }

    protected:

        virtual ~SceneView();

        /** Do cull traversal of attached scene graph using Cull NodeVisitor. Return true if computeNearFar has been done during the cull traversal.*/
        virtual bool cullStage(const osg::Matrixd& projection,const osg::Matrixd& modelview,osgUtil::CullVisitor* cullVisitor, osgUtil::StateGraph* rendergraph, osgUtil::RenderStage* renderStage, osg::Viewport *viewport);

        void computeLeftEyeViewport(const osg::Viewport *viewport);
        void computeRightEyeViewport(const osg::Viewport *viewport);

        const osg::Matrix computeMVPW() const;

        void clearArea(int x,int y,int width,int height,const osg::Vec4& color);

        osg::ref_ptr<osg::StateSet>                 _localStateSet;
        osg::RenderInfo                             _renderInfo;

        bool                                        _initCalled;
        osg::ref_ptr<osg::NodeVisitor>              _initVisitor;
        osg::ref_ptr<osg::NodeVisitor>              _updateVisitor;
        osg::ref_ptr<osgUtil::CullVisitor>          _cullVisitor;
        osg::ref_ptr<osgUtil::StateGraph>           _stateGraph;
        osg::ref_ptr<osgUtil::RenderStage>          _renderStage;

        osg::ref_ptr<ComputeStereoMatricesCallback> _computeStereoMatricesCallback;

        osg::ref_ptr<osgUtil::CullVisitor>          _cullVisitorLeft;
        osg::ref_ptr<osgUtil::StateGraph>           _stateGraphLeft;
        osg::ref_ptr<osgUtil::RenderStage>          _renderStageLeft;
        osg::ref_ptr<osg::Viewport>                 _viewportLeft;

        osg::ref_ptr<osgUtil::CullVisitor>          _cullVisitorRight;
        osg::ref_ptr<osgUtil::StateGraph>           _stateGraphRight;
        osg::ref_ptr<osgUtil::RenderStage>          _renderStageRight;
        osg::ref_ptr<osg::Viewport>                 _viewportRight;

        osg::ref_ptr<osg::CollectOccludersVisitor>  _collectOccludersVisitor;

        osg::ref_ptr<osg::FrameStamp>               _frameStamp;

        osg::observer_ptr<osg::Camera>              _camera;
        osg::ref_ptr<osg::Camera>                   _cameraWithOwnership;

        osg::ref_ptr<osg::StateSet>                 _globalStateSet;
        osg::ref_ptr<osg::Light>                    _light;
        osg::ref_ptr<osg::DisplaySettings>          _displaySettings;

        osg::ref_ptr<osg::StateSet>                 _secondaryStateSet;

        FusionDistanceMode                          _fusionDistanceMode;
        float                                       _fusionDistanceValue;

        LightingMode                                _lightingMode;

        bool                                        _prioritizeTextures;

        bool                                        _automaticFlush;
        bool                                        _requiresFlush;

        int                                         _activeUniforms;
        double                                      _previousFrameTime;
        double                                      _previousSimulationTime;

        bool                                        _redrawInterlacedStereoStencilMask;
        int                                         _interlacedStereoStencilWidth;
        int                                         _interlacedStereoStencilHeight;

        unsigned int                                _dynamicObjectCount;

        bool                                        _resetColorMaskToAllEnabled;
};

}

#endif


/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/
//osgParticle - Copyright (C) 2002 <PERSON>

#ifndef OSGPARTICLE_VARIABLERATE_COUNTER
#define OSGPARTICLE_VARIABLERATE_COUNTER 1

#include <osgParticle/Counter>
#include <osgParticle/range>

#include <osg/CopyOp>
#include <osg/Object>

namespace osgParticle
{

    class VariableRateCounter: public Counter {
    public:
        inline VariableRateCounter();
        inline VariableRateCounter(const VariableRateCounter& copy, const osg::CopyOp& copyop = osg::CopyOp::SHALLOW_COPY);

        virtual const char* libraryName() const { return "osgParticle"; }
        virtual const char* className() const { return "VariableRateCounter"; }
        virtual bool isSameKindAs(const osg::Object* obj) const { return dynamic_cast<const VariableRateCounter *>(obj) != 0; }

        inline const rangef& getRateRange() const;
        inline void setRateRange(const rangef& r);
        inline void setRateRange(float minrange, float maxrange);

        virtual int getEstimatedMaxNumOfParticles(double lifeTime) const { return static_cast<int>(_rate_range.maximum * lifeTime); }

    protected:
        virtual ~VariableRateCounter() {}

    private:
        rangef _rate_range;
    };

    // INLINE FUNCTIONS

    inline VariableRateCounter::VariableRateCounter()
    : Counter(), _rate_range(1, 1)
    {
    }

    inline VariableRateCounter::VariableRateCounter(const VariableRateCounter& copy, const osg::CopyOp& copyop)
    : Counter(copy, copyop), _rate_range(copy._rate_range)
    {
    }

    inline const rangef &VariableRateCounter::getRateRange() const
    {
        return _rate_range;
    }

    inline void VariableRateCounter::setRateRange(const rangef& r)
    {
        _rate_range = r;
    }

    inline void VariableRateCounter::setRateRange(float minrange, float maxrange)
    {
        _rate_range.set(minrange, maxrange);
    }

}


#endif

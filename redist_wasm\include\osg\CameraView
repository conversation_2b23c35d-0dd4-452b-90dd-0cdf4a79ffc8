/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSG_CAMERAVIEW
#define OSG_CAMERAVIEW 1

#include <osg/Group>
#include <osg/Transform>
#include <osg/AnimationPath>
#include <osg/Vec3d>
#include <osg/Quat>

namespace osg {

/** CameraView - is a Transform that is used to specify camera views from within the scene graph.
  * The application must attach a camera to a CameraView via the NodePath from the top of the scene graph
  * to the CameraView node itself, and accumulate the view matrix from this NodePath.
*/
class OSG_EXPORT CameraView : public Transform
{
    public :
        CameraView();

        CameraView(const CameraView& pat,const CopyOp& copyop=CopyOp::SHALLOW_COPY):
            Transform(pat,copyop),
            _position(pat._position),
            _attitude(pat._attitude),
            _fieldOfView(pat._fieldOfView),
            _fieldOfViewMode(pat._fieldOfViewMode),
            _focalLength(pat._focalLength) {}


        META_Node(osg, CameraView);

        /** Set the position of the camera view.*/
        inline void setPosition(const Vec3d& pos) { _position = pos; dirtyBound(); }

        /** Get the position of the camera view.*/
        inline const Vec3d& getPosition() const { return _position; }

        /** Set the attitude of the camera view.*/
        inline void setAttitude(const Quat& quat) { _attitude = quat; dirtyBound(); }

        /** Get the attitude of the camera view.*/
        inline const Quat& getAttitude() const { return _attitude; }

        /** Set the field of view.
          * The camera's field of view can be constrained to either the horizontal or vertical axis of the camera, or unconstrained
          * in which case the camera/application are left to choose an appropriate field of view.
          * The default value if 60 degrees. */
        inline void setFieldOfView(double fieldOfView) { _fieldOfView = fieldOfView; }

        /** Get the field of view.*/
        inline double getFieldOfView() const { return _fieldOfView; }

        enum FieldOfViewMode
        {
            UNCONSTRAINED,
            HORIZONTAL,
            VERTICAL
        };

        /** Set the field of view mode - controlling how the field of view of the camera is constrained by the CameraView settings.*/
        inline void setFieldOfViewMode(FieldOfViewMode mode) { _fieldOfViewMode = mode; }

        /** Get the field of view mode.*/
        inline FieldOfViewMode getFieldOfViewMode() const { return _fieldOfViewMode; }

        /** Set the focal length of the camera.
          * A focal length of 0.0 indicates that the camera/application should determine the focal length.
          * The default value is 0.0. */
        inline void setFocalLength(double focalLength) { _focalLength = focalLength; }

        /** Get the focal length of the camera.*/
        inline double getFocalLength() const { return _focalLength; }


        virtual bool computeLocalToWorldMatrix(Matrix& matrix,NodeVisitor* nv) const;
        virtual bool computeWorldToLocalMatrix(Matrix& matrix,NodeVisitor* nv) const;


    protected :

        virtual ~CameraView() {}

        Vec3d           _position;
        Quat            _attitude;
        double          _fieldOfView;
        FieldOfViewMode _fieldOfViewMode;
        double          _focalLength;
};

}

#endif

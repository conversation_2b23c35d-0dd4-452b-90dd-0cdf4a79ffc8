/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSG_SHADEMODEL
#define OSG_SHADEMODEL 1

#include <osg/GL>
#include <osg/StateAttribute>

namespace osg {

#ifndef OSG_GL_FIXED_FUNCTION_AVAILABLE
    #define GL_FLAT     0x1D00
    #define GL_SMOOTH   0x1D01
#endif

/** Class which encapsulates glShadeModel(..).
*/
class OSG_EXPORT ShadeModel : public StateAttribute
{
    public :

        enum Mode {
            FLAT = GL_FLAT,
            SMOOTH = GL_SMOOTH
        };

        ShadeModel(Mode mode=SMOOTH);

        /** Copy constructor using CopyOp to manage deep vs shallow copy.*/
        ShadeModel(const ShadeModel& sm,const CopyOp& copyop=CopyOp::SHALLOW_COPY):
            StateAttribute(sm,copyop),
            _mode(sm._mode) {}


        META_StateAttribute(osg, ShadeModel, SHADEMODEL);

        /** return -1 if *this < *rhs, 0 if *this==*rhs, 1 if *this>*rhs.*/
        virtual int compare(const StateAttribute& sa) const
        {
            // check the types are equal and then create the rhs variable
            // used by the COMPARE_StateAttribute_Parameter macros below.
            COMPARE_StateAttribute_Types(ShadeModel,sa)

            // compare each parameter in turn against the rhs.
            COMPARE_StateAttribute_Parameter(_mode)

            return 0; // passed all the above comparison macros, must be equal.
        }

        inline void setMode(Mode mode) { _mode = mode; }

        inline Mode getMode() const { return _mode; }

        virtual void apply(State& state) const;

    protected:

        virtual ~ShadeModel();

        Mode _mode;

};

}

#endif

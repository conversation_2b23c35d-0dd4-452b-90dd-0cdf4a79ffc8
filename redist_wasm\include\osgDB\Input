/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSGDB_INPUT
#define OSGDB_INPUT 1

#include <osg/Image>
#include <osg/Shader>
#include <osg/Node>
#include <osg/Drawable>
#include <osg/StateAttribute>
#include <osg/ArgumentParser>

#include <osgDB/ReaderWriter>
#include <osgDB/Options>

#include <map>
#include <string>

namespace osgDB {

/** basic structure for custom runtime inheritance checking */
struct basic_type_wrapper {
    virtual ~basic_type_wrapper() {}
    virtual bool matches(const osg::Object *proto) const = 0;
};

/** a class template that checks inheritance between a given
    Object's class and a class defined at compile time through
        the template parameter T.
        This is used in conjunction with readObjectOfType() to
        specify an abstract class as reference type.
**/
template<class T>
struct type_wrapper: basic_type_wrapper {
    bool matches(const osg::Object *proto) const
    {
        return dynamic_cast<const T*>(proto) != 0;
    }
};

/** deprecated. */
class OSGDB_EXPORT Field
{
    public:

        enum {
            MIN_CACHE_SIZE = 256
        };

        Field();
        Field(const Field& field);
        virtual ~Field();

        virtual Field& operator = (const Field& ic);

        void reset();
        void addChar(char c);
        int getNoCharacters() const { return _fieldCacheSize; }

        void setWithinQuotes(bool withinQuotes=true);
        bool getWithinQuotes();

        void setNoNestedBrackets(int no);
        int getNoNestedBrackets();

        enum FieldType
        {
            OPEN_BRACKET,
            CLOSE_BRACKET,
            STRING,
            WORD,
            REAL,
            INTEGER,
            BLANK,
            UNINITIALISED
        };

        FieldType getFieldType() const;

        bool isValid() const;

        bool isOpenBracket() const;
        bool isCloseBracket() const;

        bool isWord() const;
        bool matchWord(const char* str) const;
        bool matchWord(const char* str,int noCharacters) const;

        bool isString() const;
        bool matchString(const char* str) const;
        bool matchString(const char* str,int noCharacters) const;
        bool isQuotedString() const;

        const char* getStr() const;
        char* takeStr();

        bool isInt() const;
        bool matchInt(int i) const;
        bool getInt(int& i) const;

        bool isUInt() const;
        bool matchUInt(unsigned int i) const;
        bool getUInt(unsigned int& i) const;

        bool isFloat() const;
        bool matchFloat(float f) const;
        bool getFloat(float& f) const;
        bool getFloat(double& f) const;

        static FieldType calculateFieldType(const char* str,bool withinQuotes=false);

    protected:

        void _init();
        void _free();
        void _copy(const Field& ic);

        int _fieldCacheCapacity;
        int _fieldCacheSize;
        char* _fieldCache;

        mutable FieldType _fieldType;

        bool _withinQuotes;

        int _noNestedBrackets;

};

/** deprecated. */
class OSGDB_EXPORT FieldReader
{
    public:

        FieldReader();
        FieldReader(const FieldReader& ic);
        virtual ~FieldReader();

        virtual FieldReader& operator = (const FieldReader& ic);

        void attach(std::istream* input);
        void detach();

        virtual bool eof() const;

        bool readField(Field& fieldPtr);
        void ignoreField();

        /** no of unmatched `{' encountered so far in file*/
        int getNoNestedBrackets() const;

    private:

        bool _readField(Field* fieldPtr);

        void _init();
        void _free();
        void _copy(const FieldReader& ic);

        std::istream* _fin;
        bool _eof;

        bool findStartOfNextField();

        int _noNestedBrackets;

        bool _delimiterEatLookUp[256];
        bool _delimiterKeepLookUp[256];

};

/** deprecated. */
class OSGDB_EXPORT FieldReaderIterator
{
    public:

        enum {
            MINIMUM_FIELD_READER_QUEUE_SIZE = 10
        };

        FieldReaderIterator();
        FieldReaderIterator(const FieldReaderIterator& ic);
        virtual ~FieldReaderIterator();

        FieldReaderIterator& operator = (const FieldReaderIterator& ic);

        void attach(std::istream* input);
        void detach();

        virtual bool eof() const;

        FieldReader& getFieldReader() { return _reader; }

        void insert(int pos,Field* field);
        void insert(int pos,const char* str);

        Field& operator [] (int pos);
        Field& field (int pos);

        FieldReaderIterator& operator ++ ();
        FieldReaderIterator& operator += (int no);

        /** increments the iterator of the next simple field or
          * whole block if the current field[0] is an open bracket */
        void advanceOverCurrentFieldOrBlock();
        void advanceToEndOfCurrentBlock();
        void advanceToEndOfBlock(int noNestBrackets);

        bool matchSequence(const char* str);

        bool readSequence(const char* keyword,std::string& value);
        bool readSequence(const char* keyword,unsigned int& value);
        bool readSequence(const char* keyword,int& value);
        bool readSequence(const char* keyword,float& value);
        bool readSequence(const char* keyword,osg::Vec2f& value);
        bool readSequence(const char* keyword,osg::Vec3f& value);
        bool readSequence(const char* keyword,osg::Vec4f& value);
        bool readSequence(const char* keyword,osg::Vec2d& value);
        bool readSequence(const char* keyword,osg::Vec3d& value);
        bool readSequence(const char* keyword,osg::Vec4d& value);

        bool readSequence(std::string& value);
        bool readSequence(unsigned int& value);
        bool readSequence(int& value);
        bool readSequence(float& value);
        bool readSequence(osg::Vec2f& value);
        bool readSequence(osg::Vec3f& value);
        bool readSequence(osg::Vec4f& value);
        bool readSequence(osg::Vec2d& value);
        bool readSequence(osg::Vec3d& value);
        bool readSequence(osg::Vec4d& value);

    private:

        void _init();
        void _free();
        void _copy(const FieldReaderIterator& ic);

        FieldReader _reader;

        Field _blank;

        Field* _previousField;

        Field** _fieldQueue;
        int _fieldQueueSize;
        int _fieldQueueCapacity;

};

/** deprecated. */
class OSGDB_EXPORT Input : public FieldReaderIterator
{
    public:

        Input();
        virtual ~Input();

        void setOptions(const Options* options) { _options = options; }
        const Options* getOptions() const { return _options.get(); }

        virtual osg::Object*         readObjectOfType(const osg::Object& compObj);
        virtual osg::Object*         readObjectOfType(const basic_type_wrapper &btw);


        template<typename T>
        inline T* readObjectOfType()
        {
            return dynamic_cast<T*>(readObjectOfType(osgDB::type_wrapper<T>()));
        }

        virtual osg::Object*         readObject();
        virtual osg::Image*          readImage();
        virtual osg::Drawable*       readDrawable();
        virtual osg::StateAttribute* readStateAttribute();
        virtual osg::Uniform*        readUniform();
        virtual osg::Node*           readNode();
        virtual osg::Shader*         readShader();

        virtual osg::ref_ptr<osg::Object>       readObject(const std::string& fileName);
        virtual osg::ref_ptr<osg::Image>        readImage(const std::string& fileName);
        virtual osg::ref_ptr<osg::Node>         readNode(const std::string& fileName);
        virtual osg::ref_ptr<osg::Shader>       readShader(const std::string& fileName);

        virtual osg::Object*         getObjectForUniqueID(const std::string& uniqueID);
        virtual void                 registerUniqueIDForObject(const std::string& uniqueID,osg::Object* obj);

        typedef osg::ArgumentParser::Parameter Parameter;

        bool read(Parameter value1);
        bool read(Parameter value1, Parameter value2);
        bool read(Parameter value1, Parameter value2, Parameter value3);
        bool read(Parameter value1, Parameter value2, Parameter value3, Parameter value4);
        bool read(Parameter value1, Parameter value2, Parameter value3, Parameter value4, Parameter value5);
        bool read(Parameter value1, Parameter value2, Parameter value3, Parameter value4, Parameter value5, Parameter value6);
        bool read(Parameter value1, Parameter value2, Parameter value3, Parameter value4, Parameter value5, Parameter value6, Parameter value7);
        bool read(Parameter value1, Parameter value2, Parameter value3, Parameter value4, Parameter value5, Parameter value6, Parameter value7, Parameter value8);

        bool read(const char* str);
        bool read(const char* str, Parameter value1);
        bool read(const char* str, Parameter value1, Parameter value2);
        bool read(const char* str, Parameter value1, Parameter value2, Parameter value3);
        bool read(const char* str, Parameter value1, Parameter value2, Parameter value3, Parameter value4);
        bool read(const char* str, Parameter value1, Parameter value2, Parameter value3, Parameter value4, Parameter value5);
        bool read(const char* str, Parameter value1, Parameter value2, Parameter value3, Parameter value4, Parameter value5, Parameter value6);
        bool read(const char* str, Parameter value1, Parameter value2, Parameter value3, Parameter value4, Parameter value5, Parameter value6, Parameter value7);
        bool read(const char* str, Parameter value1, Parameter value2, Parameter value3, Parameter value4, Parameter value5, Parameter value6, Parameter value7, Parameter value8);

    private:

        typedef std::map< std::string, osg::ref_ptr<osg::Object> > UniqueIDToObjectMapping;
        UniqueIDToObjectMapping _uniqueIDToObjectMap;

        osg::ref_ptr<const Options> _options;

};

}

#endif                                            // __SG_INPUT_H

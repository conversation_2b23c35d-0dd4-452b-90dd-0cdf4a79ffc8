/*  -*-c++-*-
 *  Copyright (C) 2009 <PERSON><PERSON> <<EMAIL>>
 *  Copyright (C) 2017 <PERSON> <<EMAIL>>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
 */

#ifndef OSGANIMATION_RIG_TRANSFORM_HARDWARE
#define OSGANIMATION_RIG_TRANSFORM_HARDWARE 1

#include <osgAnimation/Export>
#include <osgAnimation/RigTransform>
#include <osgAnimation/VertexInfluence>
#include <osgAnimation/Bone>
#include <osg/Matrix>
#include <osg/Array>

#define RIGTRANSHW_DEFAULT_FIRST_VERTATTRIB_TARGETTED 11

namespace osgAnimation
{
    class RigGeometry;

    /// This class manage format for hardware skinning
    class OSGANIMATION_EXPORT RigTransformHardware : public RigTransform
    {
    public:

        RigTransformHardware();

        RigTransformHardware(const RigTransformHardware& rth, const osg::CopyOp& copyop);

        META_Object(osgAnimation,RigTransformHardware);

        typedef std::vector<osg::ref_ptr<osg::Vec4Array> > BoneWeightAttribList;
        typedef std::vector<osg::ref_ptr<Bone> > BonePalette;
        typedef std::map<std::string, unsigned int> BoneNamePaletteIndex;
        typedef std::vector<osg::Matrix> MatrixPalette;

        ///set the first Vertex Attribute Array index of the rig generated by this technic (default:11)
        void setFirstVertexAttributeTarget(unsigned int i) { _minAttribIndex=i; }
        unsigned int getFirstVertexAttributeTarget()const { return _minAttribIndex; }

        void setShader(osg::Shader* shader) { _shader = shader; }
        const osg::Shader* getShader() const { return _shader.get(); }
        osg::Shader* getShader() { return _shader.get(); }

        osg::Vec4Array* getVertexAttrib(unsigned int index);
        unsigned int getNumVertexAttrib() const { return _boneWeightAttribArrays.size(); }

        const unsigned int &getNumBonesPerVertex() const { return _bonesPerVertex; }
        const unsigned int &getNumVertexes() const { return _nbVertices; }

        const BoneNamePaletteIndex& getBoneNameToPalette() { return _boneNameToPalette; }
        const BonePalette& getBonePalette() { return _bonePalette; }
        osg::Uniform* getMatrixPaletteUniform() { return _uniformMatrixPalette.get(); }

        void computeMatrixPaletteUniform(const osg::Matrix& transformFromSkeletonToGeometry, const osg::Matrix& invTransformFromSkeletonToGeometry);

        // update rig if needed
        virtual void operator()(RigGeometry&);

        // init/reset animations data
        virtual bool prepareData(RigGeometry& );

    protected:

        unsigned int _bonesPerVertex;
        unsigned int _nbVertices;

        BonePalette _bonePalette;
        BoneNamePaletteIndex _boneNameToPalette;
        BoneWeightAttribList _boneWeightAttribArrays;
        osg::ref_ptr<osg::Uniform> _uniformMatrixPalette;
        osg::ref_ptr<osg::Shader> _shader;

        bool _needInit;
        unsigned int _minAttribIndex;
        bool buildPalette(const BoneMap& boneMap,const RigGeometry& rig);

        //on first update
        virtual bool init(RigGeometry& );

        std::vector<IndexWeightList>  _perVertexInfluences;
    };
}

#endif

/*  -*-c++-*-
 *  Copyright (C) 2008 <PERSON><PERSON> <<EMAIL>>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
 *
 * Authors: <AUTHORS>
 *         <PERSON> <<EMAIL>>
 */

#ifndef OSGANIMATION_BONE
#define OSGANIMATION_BONE 1

#include <osg/MatrixTransform>
#include <osgAnimation/Export>

namespace osgAnimation
{

    // A bone can't have more than one parent Bone, so sharing a part of <PERSON>'s hierarchy
    // makes no sense. You can share the entire hierarchy but not only a part of it.
    class OSGANIMATION_EXPORT Bone : public osg::MatrixTransform
    {
    public:
        typedef osg::Matrix MatrixType;

        META_Node(osgAnimation, Bone);
        Bone(const Bone& b, const osg::CopyOp& copyop= osg::CopyOp::SHALLOW_COPY);
        Bone(const std::string& name = "");

        void setDefaultUpdateCallback(const std::string& name = "");

        Bone* getBoneParent();
        const Bone* getBoneParent() const;

        const osg::Matrix& getMatrixInBoneSpace() const { return getMatrix();}
        const osg::Matrix& getMatrixInSkeletonSpace() const { return _boneInSkeletonSpace; }
        const osg::Matrix& getInvBindMatrixInSkeletonSpace() const { return _invBindInSkeletonSpace;}
        void setMatrixInSkeletonSpace(const osg::Matrix& matrix) { _boneInSkeletonSpace = matrix; }
        void setInvBindMatrixInSkeletonSpace(const osg::Matrix& matrix) { _invBindInSkeletonSpace = matrix; }

    protected:

        // bind data
        osg::Matrix _invBindInSkeletonSpace;

        // bone updated
        osg::Matrix _boneInSkeletonSpace;
    };

    typedef std::map<std::string, osg::ref_ptr<Bone> > BoneMap;
}
#endif

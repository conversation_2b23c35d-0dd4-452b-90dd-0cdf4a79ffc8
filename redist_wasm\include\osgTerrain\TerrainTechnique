/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSGTERRAIN_TERRAINTECHNIQUE
#define OSGTERRAIN_TERRAINTECHNIQUE 1

#include <osg/Object>

#include <osgUtil/UpdateVisitor>
#include <osgUtil/CullVisitor>

#include <osgTerrain/Export>

namespace osgTerrain {

class TerrainTile;

class OSGTERRAIN_EXPORT TerrainNeighbours
{
    public:

        TerrainNeighbours();
        ~TerrainNeighbours();

        void clear();
        void addNeighbour(TerrainTile* tile);
        void removeNeighbour(TerrainTile* tile);
        bool containsNeighbour(TerrainTile* tile) const;

    protected:

        TerrainNeighbours(const TerrainNeighbours& /*tn*/) {}
        TerrainNeighbours& operator = (const TerrainNeighbours& /*rhs*/) { return *this; }

        typedef std::set<TerrainTile*> Neighbours;

        mutable OpenThreads::Mutex      _neighboursMutex;
        Neighbours                      _neighbours;
};


class OSGTERRAIN_EXPORT TerrainTechnique : public osg::Object, public osg::Observer
{
    public:

        TerrainTechnique();

        /** Copy constructor using CopyOp to manage deep vs shallow copy.*/
        TerrainTechnique(const TerrainTechnique&,const osg::CopyOp& copyop=osg::CopyOp::SHALLOW_COPY);

        META_Object(osgTerrain, TerrainTechnique);

        virtual void setTerrainTile(TerrainTile* tile);
        TerrainTile* getTerrainTile() { return _terrainTile; }
        const TerrainTile* getTerrainTile() const { return _terrainTile; }

        virtual void init(int dirtyMask, bool assumeMultiThreaded);

        virtual void update(osgUtil::UpdateVisitor* nv);

        virtual void cull(osgUtil::CullVisitor* nv);

        /** Clean scene graph from any terrain technique specific nodes.*/
        virtual void cleanSceneGraph();

        /** Traverse the terrain subgraph.*/
        virtual void traverse(osg::NodeVisitor& nv);

        /** If State is non-zero, this function releases any associated OpenGL objects for
        * the specified graphics context. Otherwise, releases OpenGL objects
        * for all graphics contexts. */
        virtual void releaseGLObjects(osg::State* = 0) const {}

        virtual void addNeighbour(TerrainTile* tile) { _neighbours.addNeighbour(tile); }
        virtual void removeNeighbour(TerrainTile* tile) {  _neighbours.removeNeighbour(tile); }
        virtual bool containsNeighbour(TerrainTile* tile) { return _neighbours.containsNeighbour(tile); }


    protected:

        virtual ~TerrainTechnique();

        friend class osgTerrain::TerrainTile;

        TerrainTile*            _terrainTile;


        TerrainNeighbours       _neighbours;

};

}

#endif

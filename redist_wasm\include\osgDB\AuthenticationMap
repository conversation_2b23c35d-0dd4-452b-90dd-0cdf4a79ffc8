/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSGDB_AUTHENTICATIONMAP
#define OSGDB_AUTHENTICATIONMAP 1

#include <osg/Referenced>
#include <osg/ref_ptr>

#include <osgDB/Export>

#include <string>
#include <map>

namespace osgDB {

class Archive;

class AuthenticationDetails : public osg::Referenced
{
public:

    /** Http authentication techniques, see libcurl docs for details on names and associated functionality.*/
    enum HttpAuthentication
    {
        BASIC           = 1<<0,
        DIGEST          = 1<<1,
        NEGOTIATE       = 1<<2,
        GSSNegotiate    = NEGOTIATE,
        NTLM            = 1<<3,
        DIGEST_IE       = 1<<4,
        NTLM_WB         = 1<<5,
        ONLY            = 1<<31,
        ANY             = ~(DIGEST_IE),
        ANYSAFE         = ~(BASIC|DIGEST_IE)
    };

    AuthenticationDetails(const std::string& u, const std::string& p, HttpAuthentication auth=BASIC):
        username(u),
        password(p),
        httpAuthentication(auth) {}

    std::string         username;
    std::string         password;
    HttpAuthentication  httpAuthentication;

protected:
    virtual ~AuthenticationDetails() {}
};

class OSGDB_EXPORT AuthenticationMap : public osg::Referenced
{
    public:

        AuthenticationMap() {}


        virtual void addAuthenticationDetails(const std::string& path, AuthenticationDetails* details);

        virtual const AuthenticationDetails* getAuthenticationDetails(const std::string& path) const;

    protected:

        virtual ~AuthenticationMap() {}

        typedef std::map<std::string, osg::ref_ptr<AuthenticationDetails> > AuthenticationDetailsMap;
        AuthenticationDetailsMap _authenticationMap;

};

}

#endif // OSGDB_AUTHENTICATIONMAP

/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSGSIM_INSERTIMPOSTORSVISITOR
#define OSGSIM_INSERTIMPOSTORSVISITOR

#include <osg/NodeVisitor>
#include <osgSim/Impostor>

namespace osgSim {

/** Insert impostor nodes into scene graph.
  * For example of usage see examples/osgimpostor.
  */
class OSGSIM_EXPORT InsertImpostorsVisitor : public osg::NodeVisitor
{
    public:

        /** Default to traversing all children. */
        InsertImpostorsVisitor();

        META_NodeVisitor(osgSim, InsertImpostorsVisitor)

        void setImpostorThresholdRatio(float ratio) { _impostorThresholdRatio = ratio; }
        float getImpostorThresholdRatio() const { return _impostorThresholdRatio; }

        void setMaximumNumberOfNestedImpostors(unsigned int num) { _maximumNumNestedImpostors = num; }
        unsigned int getMaximumNumberOfNestedImpostors() const { return _maximumNumNestedImpostors; }

        /** Empty visitor, make it ready for next traversal. */
        void reset();

        virtual void apply(osg::Node& node);

        virtual void apply(osg::Group& node);

        virtual void apply(osg::LOD& node);

        /* Insert the required impostors into the scene graph. */
        void insertImpostors();

    protected:

        typedef std::vector< osg::Group* >  GroupList;
        typedef std::vector< osg::LOD* >    LODList;

        GroupList       _groupList;
        LODList         _lodList;

        float           _impostorThresholdRatio;
        unsigned int    _maximumNumNestedImpostors;
        unsigned int    _numNestedImpostors;

};

}

#endif

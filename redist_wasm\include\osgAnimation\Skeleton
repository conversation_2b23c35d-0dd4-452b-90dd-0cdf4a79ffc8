/*  -*-c++-*-
 *  Copyright (C) 2008 <PERSON><PERSON> <<EMAIL>>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSGANIMATION_SKELETON
#define OSGANIMATION_SKELETON 1

#include <osgAnimation/Export>
#include <osg/MatrixTransform>
#include <osg/Callback>

namespace osgAnimation
{

    class OSGANIMATION_EXPORT Skeleton : public osg::MatrixTransform
    {
    public:
        META_Node(osgAnimation, Skeleton);

        class OSGANIMATION_EXPORT UpdateSkeleton : public osg::NodeCallback
        {
        public:
            META_Object(osgAnimation, UpdateSkeleton);
            UpdateSkeleton();
            UpdateSkeleton(const UpdateSkeleton&, const osg::CopyOp& copyop = osg::CopyOp::SHALLOW_COPY);
            virtual void operator()(osg::Node* node, osg::NodeVisitor* nv);
            bool needToValidate() const;
        protected:
            bool _needValidate;
        };

        Skeleton();
        Skeleton(const Skeleton&, const osg::CopyOp&);
        void setDefaultUpdateCallback();

    };

}

#endif

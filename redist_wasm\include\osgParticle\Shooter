/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/
//osgParticle - Copyright (C) 2002 <PERSON>

#ifndef OSGPARTICLE_SHOOTER
#define OSGPARTICLE_SHOOTER 1

#include <osg/CopyOp>
#include <osg/Object>

namespace osgParticle
{

    class Particle;

    /**     An abstract base class used by ModularEmitter to "shoot" the particles after they have been placed.
        Descendants of this class must override the <CODE>shoot()</CODE> method.
    */
    class Shooter: public osg::Object
    {
    public:
        inline Shooter();
        inline Shooter(const Shooter& copy, const osg::CopyOp& copyop = osg::CopyOp::SHALLOW_COPY);

        virtual const char* libraryName() const { return "osgParticle"; }
        virtual const char* className() const { return "Shooter"; }
        virtual bool isSameKindAs(const osg::Object* obj) const { return dynamic_cast<const Shooter *>(obj) != 0; }

        /**     Shoot a particle. Must be overridden by descendants.
            This method should only set the velocity vector of particle <CODE>P</CODE>, leaving other
            attributes unchanged.
        */
        virtual void shoot(Particle* P) const = 0;

    protected:
        virtual ~Shooter() {}
        Shooter &operator=(const Shooter &) { return *this; }
    };

    // INLINE FUNCTIONS

    inline Shooter::Shooter()
    : osg::Object()
    {
    }

    inline Shooter::Shooter(const Shooter& copy, const osg::CopyOp& copyop)
    : osg::Object(copy, copyop)
    {
    }

}

#endif

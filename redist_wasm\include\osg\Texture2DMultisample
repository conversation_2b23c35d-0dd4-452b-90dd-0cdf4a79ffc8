/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2010 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
 *
 * Texture2DMultisample codes Copyright (C) 2010 <PERSON><PERSON>
 * Thanks to to my company http://www.ai.com.pl for allowing me free this work.
*/

#ifndef OSG_TEXTURE2DMS
#define OSG_TEXTURE2DMS 1

#include <osg/Texture>

namespace osg {

 /** Texture2DMultisample state class which encapsulates OpenGL 2D multisampled texture functionality.
 * Multisampled texture were introduced with OpenGL 3.1 and extension GL_ARB_texture_multisample.
 * See http://www.opengl.org/registry/specs/ARB/texture_multisample.txt for more info.
 */

class OSG_EXPORT Texture2DMultisample : public Texture
{
    public :

        Texture2DMultisample();

        Texture2DMultisample(GLsizei numSamples, GLboolean fixedsamplelocations);

        /** Copy constructor using CopyOp to manage deep vs shallow copy. */
        Texture2DMultisample(const Texture2DMultisample& text,const CopyOp& copyop=CopyOp::SHALLOW_COPY);

        META_StateAttribute(osg, Texture2DMultisample,TEXTURE);

        /** Return -1 if *this < *rhs, 0 if *this==*rhs, 1 if *this>*rhs. */
        virtual int compare(const StateAttribute& rhs) const;

        virtual GLenum getTextureTarget() const
        {
          return GL_TEXTURE_2D_MULTISAMPLE;
        }

        /** Texture2DMultisample is related to non fixed pipeline usage only so isn't appropriate to enable/disable.*/
        virtual bool getModeUsage(StateAttribute::ModeUsage&) const { return false; }

        /** Sets the texture width and height. If width or height are zero,
          * calculate the respective value from the source image size. */
        inline void setTextureSize(int width, int height) const
        {
            _textureWidth = width;
            _textureHeight = height;
        }

        inline void setNumSamples( int samples ) { _numSamples = samples; }
        GLsizei getNumSamples() const { return _numSamples; }

        inline void setFixedSampleLocations( GLboolean fixedSampleLocations ) { _fixedsamplelocations = fixedSampleLocations; }
        inline GLboolean getFixedSampleLocations() const { return _fixedsamplelocations; }
 
        // unnecessary for Texture2DMultisample
        virtual void setImage(unsigned int /*face*/, Image* /*image*/) {}

        virtual Image* getImage(unsigned int /*face*/) { return NULL; }
        virtual const Image* getImage(unsigned int /*face*/) const { return NULL; }
        virtual unsigned int getNumImages() const {return 0; }
        virtual void allocateMipmap(State& /*state*/) const {}

        void setTextureWidth(int width) { _textureWidth=width; }
        void setTextureHeight(int height) { _textureHeight=height; }

        virtual int getTextureWidth() const { return _textureWidth; }
        virtual int getTextureHeight() const { return _textureHeight; }
        virtual int getTextureDepth() const { return 1; }

        /** Bind the texture object. If the texture object hasn't already been
          * compiled, create the texture mipmap levels. */
        virtual void apply(State& state) const;

    protected :

        virtual ~Texture2DMultisample();

        virtual void computeInternalFormat() const;

        /** Subloaded images can have different texture and image sizes. */
        mutable GLsizei _textureWidth, _textureHeight;

        mutable GLsizei _numSamples;

        mutable GLboolean _fixedsamplelocations;

};

}

#endif

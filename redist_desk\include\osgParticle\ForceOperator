/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/
//osgParticle - Copyright (C) 2002 <PERSON>

#ifndef OSGPARTICLE_FORCEOPERATOR
#define OSGPARTICLE_FORCEOPERATOR 1

#include <osgParticle/ModularProgram>
#include <osgParticle/Operator>
#include <osgParticle/Particle>

#include <osg/CopyOp>
#include <osg/Object>
#include <osg/Vec3>

namespace osgParticle
{

    /** An operator that applies a constant force to the particles.
      * Remember that if the mass of particles is expressed in kg and the lengths are
      * expressed in meters, then the force should be expressed in Newtons.
    */
    class ForceOperator: public Operator {
    public:
        inline ForceOperator();
        inline ForceOperator(const ForceOperator& copy, const osg::CopyOp& copyop = osg::CopyOp::SHALLOW_COPY);

        META_Object(osgParticle, ForceOperator);

        /// Get the force vector.
        inline const osg::Vec3& getForce() const;

        /// Set the force vector.
        inline void setForce(const osg::Vec3& f);

        /// Apply the force to a particle. Do not call this method manually.
        inline void operate(Particle* P, double dt);

        /// Perform some initialization. Do not call this method manually.
        inline void beginOperate(Program *prg);

    protected:
        virtual ~ForceOperator() {};
        ForceOperator& operator=(const ForceOperator&) { return *this; }

    private:
        osg::Vec3 _force;
        osg::Vec3 _xf_force;
    };

    // INLINE FUNCTIONS

    inline ForceOperator::ForceOperator()
    : Operator(), _force(0, 0, 0)
    {
    }

    inline ForceOperator::ForceOperator(const ForceOperator& copy, const osg::CopyOp& copyop)
    : Operator(copy, copyop), _force(copy._force)
    {
    }

    inline const osg::Vec3& ForceOperator::getForce() const
    {
        return _force;
    }

    inline void ForceOperator::setForce(const osg::Vec3& v)
    {
        _force = v;
    }

    inline void ForceOperator::operate(Particle* P, double dt)
    {
        P->addVelocity(_xf_force * (P->getMassInv() * dt));
    }

    inline void ForceOperator::beginOperate(Program *prg)
    {
        if (prg->getReferenceFrame() == ModularProgram::RELATIVE_RF) {
            _xf_force = prg->rotateLocalToWorld(_force);
        } else {
            _xf_force = _force;
        }
    }
}

#endif

/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSG_PROJECTION
#define OSG_PROJECTION 1

#include <osg/Group>
#include <osg/Matrix>

namespace osg {

/** Projection nodes set up the frustum/orthographic projection used when rendering the scene.
*/
class OSG_EXPORT Projection : public Group
{
    public :


        Projection();

        /** Copy constructor using CopyOp to manage deep vs shallow copy.*/
        Projection(const Projection&,const CopyOp& copyop=CopyOp::SHALLOW_COPY);

        Projection(const Matrix& matix);

        META_Node(osg, Projection);

        /** Set the transform's matrix.*/
        void setMatrix(const Matrix& mat) { _matrix = mat; }

        /** Get the transform's matrix. */
        inline const Matrix& getMatrix() const { return _matrix; }

        /** preMult transform.*/
        void preMult(const Matrix& mat) { _matrix.preMult(mat); }

        /** postMult transform.*/
        void postMult(const Matrix& mat)  { _matrix.postMult(mat); }


    protected :

        virtual ~Projection();

        Matrix  _matrix;

};

}

#endif

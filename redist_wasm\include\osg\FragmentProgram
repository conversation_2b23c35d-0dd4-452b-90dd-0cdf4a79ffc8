/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSG_FRAGMENTPROGRAM
#define OSG_FRAGMENTPROGRAM 1

#include <osg/StateAttribute>
#include <osg/Vec4>
#include <osg/Matrix>
#include <osg/buffered_value>

#include <map>
#include <string>

// if not defined by gl.h use the definition found in:
// http://oss.sgi.com/projects/ogl-sample/registry/ARB/fragment_program.txt
#ifndef GL_ARB_fragment_program
#define GL_FRAGMENT_PROGRAM_ARB                            0x8804
#define GL_PROGRAM_FORMAT_ASCII_ARB                        0x8875
#define GL_PROGRAM_LENGTH_ARB                              0x8627
#define GL_PROGRAM_FORMAT_ARB                              0x8876
#define GL_PROGRAM_BINDING_ARB                             0x8677
#define GL_PROGRAM_INSTRUCTIONS_ARB                        0x88A0
#define GL_MAX_PROGRAM_INSTRUCTIONS_ARB                    0x88A1
#define GL_PROGRAM_NATIVE_INSTRUCTIONS_ARB                 0x88A2
#define GL_MAX_PROGRAM_NATIVE_INSTRUCTIONS_ARB             0x88A3
#define GL_PROGRAM_TEMPORARIES_ARB                         0x88A4
#define GL_MAX_PROGRAM_TEMPORARIES_ARB                     0x88A5
#define GL_PROGRAM_NATIVE_TEMPORARIES_ARB                  0x88A6
#define GL_MAX_PROGRAM_NATIVE_TEMPORARIES_ARB              0x88A7
#define GL_PROGRAM_PARAMETERS_ARB                          0x88A8
#define GL_MAX_PROGRAM_PARAMETERS_ARB                      0x88A9
#define GL_PROGRAM_NATIVE_PARAMETERS_ARB                   0x88AA
#define GL_MAX_PROGRAM_NATIVE_PARAMETERS_ARB               0x88AB
#define GL_PROGRAM_ATTRIBS_ARB                             0x88AC
#define GL_MAX_PROGRAM_ATTRIBS_ARB                         0x88AD
#define GL_PROGRAM_NATIVE_ATTRIBS_ARB                      0x88AE
#define GL_MAX_PROGRAM_NATIVE_ATTRIBS_ARB                  0x88AF
#define GL_MAX_PROGRAM_LOCAL_PARAMETERS_ARB                0x88B4
#define GL_MAX_PROGRAM_ENV_PARAMETERS_ARB                  0x88B5
#define GL_PROGRAM_UNDER_NATIVE_LIMITS_ARB                 0x88B6
#define GL_PROGRAM_ALU_INSTRUCTIONS_ARB                    0x8805
#define GL_PROGRAM_TEX_INSTRUCTIONS_ARB                    0x8806
#define GL_PROGRAM_TEX_INDIRECTIONS_ARB                    0x8807
#define GL_PROGRAM_NATIVE_ALU_INSTRUCTIONS_ARB             0x8808
#define GL_PROGRAM_NATIVE_TEX_INSTRUCTIONS_ARB             0x8809
#define GL_PROGRAM_NATIVE_TEX_INDIRECTIONS_ARB             0x880A
#define GL_MAX_PROGRAM_ALU_INSTRUCTIONS_ARB                0x880B
#define GL_MAX_PROGRAM_TEX_INSTRUCTIONS_ARB                0x880C
#define GL_MAX_PROGRAM_TEX_INDIRECTIONS_ARB                0x880D
#define GL_MAX_PROGRAM_NATIVE_ALU_INSTRUCTIONS_ARB         0x880E
#define GL_MAX_PROGRAM_NATIVE_TEX_INSTRUCTIONS_ARB         0x880F
#define GL_MAX_PROGRAM_NATIVE_TEX_INDIRECTIONS_ARB         0x8810
#define GL_PROGRAM_STRING_ARB                              0x8628
#define GL_PROGRAM_ERROR_POSITION_ARB                      0x864B
#define GL_CURRENT_MATRIX_ARB                              0x8641
#define GL_TRANSPOSE_CURRENT_MATRIX_ARB                    0x88B7
#define GL_CURRENT_MATRIX_STACK_DEPTH_ARB                  0x8640
#define GL_MAX_PROGRAM_MATRICES_ARB                        0x862F
#define GL_MAX_PROGRAM_MATRIX_STACK_DEPTH_ARB              0x862E
#define GL_MAX_TEXTURE_COORDS_ARB                          0x8871
#define GL_MAX_TEXTURE_IMAGE_UNITS_ARB                     0x8872
#define GL_PROGRAM_ERROR_STRING_ARB                        0x8874
#define GL_MATRIX0_ARB                                     0x88C0
#define GL_MATRIX1_ARB                                     0x88C1
#define GL_MATRIX2_ARB                                     0x88C2
#define GL_MATRIX3_ARB                                     0x88C3
#define GL_MATRIX4_ARB                                     0x88C4
#define GL_MATRIX5_ARB                                     0x88C5
#define GL_MATRIX6_ARB                                     0x88C6
#define GL_MATRIX7_ARB                                     0x88C7
#define GL_MATRIX8_ARB                                     0x88C8
#define GL_MATRIX9_ARB                                     0x88C9
#define GL_MATRIX10_ARB                                    0x88CA
#define GL_MATRIX11_ARB                                    0x88CB
#define GL_MATRIX12_ARB                                    0x88CC
#define GL_MATRIX13_ARB                                    0x88CD
#define GL_MATRIX14_ARB                                    0x88CE
#define GL_MATRIX15_ARB                                    0x88CF
#define GL_MATRIX16_ARB                                    0x88D0
#define GL_MATRIX17_ARB                                    0x88D1
#define GL_MATRIX18_ARB                                    0x88D2
#define GL_MATRIX19_ARB                                    0x88D3
#define GL_MATRIX20_ARB                                    0x88D4
#define GL_MATRIX21_ARB                                    0x88D5
#define GL_MATRIX22_ARB                                    0x88D6
#define GL_MATRIX23_ARB                                    0x88D7
#define GL_MATRIX24_ARB                                    0x88D8
#define GL_MATRIX25_ARB                                    0x88D9
#define GL_MATRIX26_ARB                                    0x88DA
#define GL_MATRIX27_ARB                                    0x88DB
#define GL_MATRIX28_ARB                                    0x88DC
#define GL_MATRIX29_ARB                                    0x88DD
#define GL_MATRIX30_ARB                                    0x88DE
#define GL_MATRIX31_ARB                                    0x88DF

#endif


namespace osg {



/** FragmentProgram - encapsulates the OpenGL ARB fragment program state.*/
class OSG_EXPORT FragmentProgram : public StateAttribute
{
    public:

        FragmentProgram();

        /** Copy constructor using CopyOp to manage deep vs shallow copy.*/
        FragmentProgram(const FragmentProgram& vp,const CopyOp& copyop=CopyOp::SHALLOW_COPY);

        META_StateAttribute(osg, FragmentProgram, FRAGMENTPROGRAM);

        /** return -1 if *this < *rhs, 0 if *this==*rhs, 1 if *this>*rhs.*/
        virtual int compare(const osg::StateAttribute& sa) const
        {
            // check the types are equal and then create the rhs variable
            // used by the COMPARE_StateAttribute_Parameter macros below.
            COMPARE_StateAttribute_Types(FragmentProgram,sa)

            // compare each parameter in turn against the rhs.
            COMPARE_StateAttribute_Parameter(_fragmentProgram)

            return 0; // passed all the above comparison macros, must be equal.
        }

        virtual bool getModeUsage(StateAttribute::ModeUsage& usage) const
        {
            usage.usesMode(GL_FRAGMENT_PROGRAM_ARB);
            return true;
        }

        // data access methods.

        /** Get the handle to the fragment program id for the current context.*/
        inline GLuint& getFragmentProgramID(unsigned int contextID) const
        {
            return _fragmentProgramIDList[contextID];
        }


        /** Set the fragment program using a C style string.*/
        inline void setFragmentProgram( const char* program )
        {
            _fragmentProgram = program;
            dirtyFragmentProgramObject();
        }

        /** Set the fragment program using C++ style string.*/
        inline void setFragmentProgram( const std::string& program )
        {
            _fragmentProgram = program;
            dirtyFragmentProgramObject();
        }

        /** Get the fragment program.*/
        inline const std::string& getFragmentProgram() const { return _fragmentProgram; }

        /** Set Program Parameters */
        inline void setProgramLocalParameter(const GLuint index, const Vec4& p)
        {
            _programLocalParameters[index] = p;
        }

        typedef std::map<GLuint,Vec4> LocalParamList;

        /** Set list of Program Parameters */
        inline void setLocalParameters(const LocalParamList& lpl) { _programLocalParameters = lpl; }

        /** Get list of Program Parameters */
        inline LocalParamList& getLocalParameters() { return _programLocalParameters; }

        /** Get const list of Program Parameters */
        inline const LocalParamList& getLocalParameters() const { return _programLocalParameters; }

        /** Matrix */
        inline void setMatrix(const GLenum mode, const Matrix& matrix)
        {
            _matrixList[mode] = matrix;
        }

        typedef std::map<GLenum,Matrix> MatrixList;

        /** Set list of Matrices */
        inline void setMatrices(const MatrixList& matrices) { _matrixList = matrices; }

        /** Get list of Matrices */
        inline MatrixList& getMatrices() { return _matrixList; }

        /** Get list of Matrices */
        inline const MatrixList& getMatrices() const { return _matrixList; }

        /** Force a recompile on next apply() of associated OpenGL vertex program objects.*/
        void dirtyFragmentProgramObject();

        virtual void apply(State& state) const;

        virtual void compileGLObjects(State& state) const { apply(state); }

        /** Resize any per context GLObject buffers to specified size. */
        virtual void resizeGLObjectBuffers(unsigned int maxSize);

        /** release an OpenGL objects in specified graphics context if State
            object is passed, otherwise release OpenGL objects for all graphics context if
            State object pointer == NULL.*/
        virtual void releaseGLObjects(State* state=0) const;

    protected:


        virtual ~FragmentProgram();

        typedef buffered_value<GLuint> FragmentProgramIDList;
        mutable FragmentProgramIDList _fragmentProgramIDList;

        std::string     _fragmentProgram;

        LocalParamList  _programLocalParameters;
        MatrixList      _matrixList;
};

}

#endif


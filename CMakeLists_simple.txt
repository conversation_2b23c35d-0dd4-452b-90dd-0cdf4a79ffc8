cmake_minimum_required(VERSION 3.16)
project(SimpleOSGTest)

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置OSG路径
set(OSG_ROOT ${CMAKE_CURRENT_SOURCE_DIR}/redist_desk)

# 包含目录
include_directories(${OSG_ROOT}/include)

# 创建可执行文件
add_executable(test_simple_osg test_simple_osg.cpp)

# 链接库
target_link_libraries(test_simple_osg
    ${OSG_ROOT}/lib/OpenThreads.lib
    ${OSG_ROOT}/lib/osg202-osg.lib
    ${OSG_ROOT}/lib/osg202-osgDB.lib
    ${OSG_ROOT}/lib/osg202-osgViewer.lib
    ${OSG_ROOT}/lib/osg202-osgGA.lib
    ${OSG_ROOT}/lib/osg202-osgUtil.lib
)

# 复制DLL文件到输出目录
add_custom_command(TARGET test_simple_osg POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy_if_different
    "${OSG_ROOT}/bin/ot21-OpenThreads.dll"
    $<TARGET_FILE_DIR:test_simple_osg>
)

/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSGSIM_COLORRANGE
#define OSGSIM_COLORRANGE 1

#include <osgSim/Export>

#include <osgSim/ScalarsToColors>
#include <vector>

namespace osgSim
{
/**
ColorRange is a ScalarsToColors object to define a color spectrum
for a scalar range. An optional vector of colors may be passed in at
construction time. The range of colors will be mapped to the scalar range,
and interpolation between the colors will be performed as necessary.
By default, the color range will run Red-Yellow-Green-Cyan-Blue.
*/
class OSGSIM_EXPORT ColorRange: public ScalarsToColors
{
public:

    /** Constructor for a ColorRange with a default list of colors set to Red-Yellow-Green-Blue-Cyan
    @param min      minimum scalar value
    @param max      maximum scalar value
    */
    ColorRange(float min, float max);

    /** Constructor for a ColorRange
    @param min      minimum scalar value
    @param max      maximum scalar value
    @param colors   optional range of colors,
    */
    ColorRange(float min, float max, const std::vector<osg::Vec4>& colors);

    /** Set the range of colors. */
    void setColors(const std::vector<osg::Vec4>& colors);

    /** Get the range of colors */
    const std::vector<osg::Vec4>& getColors() const { return _colors; }

    /** Get the color for a given scalar value. */
    osg::Vec4 getColor(float scalar) const;

private:

    // Default assignment and copy construction are OK.

    std::vector<osg::Vec4> _colors;
};

}

#endif

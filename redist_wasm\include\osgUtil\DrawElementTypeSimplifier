/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/
#ifndef OSGUTIL_DRAWELEMENTTYPESIMPLIFIER
#define OSGUTIL_DRAWELEMENTTYPESIMPLIFIER


#include <osg/Geometry>
#include <osg/NodeVisitor>

#include <osgUtil/Export>

namespace osgUtil
{

class OSGUTIL_EXPORT DrawElementTypeSimplifier
{
    public:
        void simplify(osg::Geometry & geometry) const;
};

class OSGUTIL_EXPORT DrawElementTypeSimplifierVisitor : public osg::NodeVisitor
{
    public:
        DrawElementTypeSimplifierVisitor(): osg::NodeVisitor(osg::NodeVisitor::TRAVERSE_ALL_CHILDREN ) {}
        META_NodeVisitor(osgUtil, DrawElementTypeSimplifierVisitor);

        void apply(osg::Geometry& geom);
};

}

#endif // ** OSGUTIL_DRAWELEMENTTYPESIMPLIFIER ** //

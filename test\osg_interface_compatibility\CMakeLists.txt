# OSG Interface Compatibility Test Framework
# Copyright 2025 Pelican Mapping
# MIT License

cmake_minimum_required(VERSION 3.16)

project(OSGInterfaceCompatibilityTests)

# 禁用vcpkg自动查找（仅在非Emscripten环境下）
if(NOT EMSCRIPTEN)
    set(CMAKE_TOOLCHAIN_FILE "")
    set(VCPKG_TARGET_TRIPLET "")
endif()

# 设置C++标准
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 编译器特定设置
if(MSVC)
    add_compile_options(/utf-8)
    add_compile_options(/W4)
else()
    add_compile_options(-Wall -Wextra)
endif()

# 查找依赖 - 手动设置OSG库路径
if(EMSCRIPTEN)
    # WebAssembly版本使用我们编译的OSG库
    set(OSG_ROOT ${CMAKE_CURRENT_SOURCE_DIR}/../../redist_wasm)
else()
    # 桌面版本使用我们编译的OSG库
    set(OSG_ROOT ${CMAKE_CURRENT_SOURCE_DIR}/../../redist_desk)
endif()

# 包含目录
include_directories(${OSG_ROOT}/include)

# 禁用自动查找OSG包，避免链接冲突
set(OSG_FOUND TRUE)
set(OPENSCENEGRAPH_FOUND TRUE)

# 禁用CMake自动查找和链接OSG库
set(CMAKE_DISABLE_FIND_PACKAGE_OpenSceneGraph TRUE)
set(CMAKE_DISABLE_FIND_PACKAGE_osg TRUE)
set(CMAKE_DISABLE_FIND_PACKAGE_osgDB TRUE)
set(CMAKE_DISABLE_FIND_PACKAGE_osgUtil TRUE)
set(CMAKE_DISABLE_FIND_PACKAGE_osgViewer TRUE)
set(CMAKE_DISABLE_FIND_PACKAGE_osgGA TRUE)

# 源文件
set(FRAMEWORK_SOURCES
    OSGInterfaceTestFramework.h
    OSGInterfaceTestFramework.cpp
    CoreRenderingTests.h
    CoreRenderingTests.cpp
    main.cpp
)

# 创建可执行文件
if(EMSCRIPTEN)
    # WebAssembly构建设置
    add_executable(osg_compatibility_test ${FRAMEWORK_SOURCES})
    
    # Emscripten特定设置
    set_target_properties(osg_compatibility_test PROPERTIES
        SUFFIX ".html"
        LINK_FLAGS "-s USE_WEBGL2=1 -s FULL_ES3=1 -s WASM=1 -s ALLOW_MEMORY_GROWTH=1 -s SAFE_HEAP=0 -s ASSERTIONS=0 -s DISABLE_EXCEPTION_CATCHING=1 -s NO_EXIT_RUNTIME=1 -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap'] --shell-file ${CMAKE_CURRENT_SOURCE_DIR}/shell.html"
    )
    
    # 添加预加载文件（如果需要）
    # set_target_properties(osg_compatibility_test PROPERTIES
    #     LINK_FLAGS "${LINK_FLAGS} --preload-file data@/data"
    # )
    
else()
    # 桌面构建设置
    add_executable(osg_compatibility_test ${FRAMEWORK_SOURCES})
    
    # 设置输出目录
    set_target_properties(osg_compatibility_test PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
    )
endif()

# 设置链接器标志，禁用默认库搜索
set(CMAKE_FIND_LIBRARY_SUFFIXES ".lib")

# 链接库
if(EMSCRIPTEN)
    target_link_libraries(osg_compatibility_test
        ${OSG_ROOT}/lib/libOpenThreads.a
        ${OSG_ROOT}/lib/libosg.a
        ${OSG_ROOT}/lib/libosgDB.a
        ${OSG_ROOT}/lib/libosgViewer.a
        ${OSG_ROOT}/lib/libosgGA.a
        ${OSG_ROOT}/lib/libosgUtil.a
    )
else()
    # 使用静态库
    target_link_libraries(osg_compatibility_test
        ${OSG_ROOT}/lib/osg202-osg.lib
        ${OSG_ROOT}/lib/osg202-osgDB.lib
        ${OSG_ROOT}/lib/osg202-osgViewer.lib
        ${OSG_ROOT}/lib/osg202-osgGA.lib
        ${OSG_ROOT}/lib/osg202-osgUtil.lib
        ${OSG_ROOT}/lib/OpenThreads.lib
        opengl32.lib
        glu32.lib
        gdi32.lib
        user32.lib
        kernel32.lib
        winmm.lib
        ws2_32.lib
    )

    # 复制DLL文件到输出目录
    add_custom_command(TARGET osg_compatibility_test POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${OSG_ROOT}/bin/ot21-OpenThreads.dll"
        $<TARGET_FILE_DIR:osg_compatibility_test>
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${OSG_ROOT}/bin/osg202-osg.dll"
        $<TARGET_FILE_DIR:osg_compatibility_test>
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${OSG_ROOT}/bin/osg202-osgDB.dll"
        $<TARGET_FILE_DIR:osg_compatibility_test>
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${OSG_ROOT}/bin/osg202-osgViewer.dll"
        $<TARGET_FILE_DIR:osg_compatibility_test>
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${OSG_ROOT}/bin/osg202-osgGA.dll"
        $<TARGET_FILE_DIR:osg_compatibility_test>
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${OSG_ROOT}/bin/osg202-osgUtil.dll"
        $<TARGET_FILE_DIR:osg_compatibility_test>
    )
endif()

# 平台特定链接库
if(WIN32)
    target_link_libraries(osg_compatibility_test
        opengl32
        glu32
    )
elseif(UNIX AND NOT APPLE)
    target_link_libraries(osg_compatibility_test
        GL
        GLU
        X11
    )
elseif(APPLE)
    find_library(OPENGL_LIBRARY OpenGL)
    target_link_libraries(osg_compatibility_test
        ${OPENGL_LIBRARY}
    )
endif()

# 编译定义
if(EMSCRIPTEN)
    target_compile_definitions(osg_compatibility_test PRIVATE
        __EMSCRIPTEN__
        GL_GLEXT_PROTOTYPES
    )
endif()

# 安装规则
if(NOT EMSCRIPTEN)
    install(TARGETS osg_compatibility_test
        RUNTIME DESTINATION bin
    )
    
    # 安装测试数据（如果有）
    # install(DIRECTORY data/
    #     DESTINATION share/osg_compatibility_test/data
    # )
endif()

# 添加自定义目标用于运行测试
if(NOT EMSCRIPTEN)
    add_custom_target(run_compatibility_tests
        COMMAND osg_compatibility_test
        DEPENDS osg_compatibility_test
        WORKING_DIRECTORY ${CMAKE_BINARY_DIR}/bin
        COMMENT "Running OSG interface compatibility tests"
    )
    
    add_custom_target(run_compatibility_tests_verbose
        COMMAND osg_compatibility_test --verbose
        DEPENDS osg_compatibility_test
        WORKING_DIRECTORY ${CMAKE_BINARY_DIR}/bin
        COMMENT "Running OSG interface compatibility tests (verbose)"
    )
endif()

# 生成编译数据库（用于IDE支持）
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# 打印配置信息
message(STATUS "OSG Interface Compatibility Tests Configuration:")
message(STATUS "  Platform: ${CMAKE_SYSTEM_NAME}")
message(STATUS "  Compiler: ${CMAKE_CXX_COMPILER_ID}")
message(STATUS "  Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ Standard: ${CMAKE_CXX_STANDARD}")

if(EMSCRIPTEN)
    message(STATUS "  Emscripten: YES")
    message(STATUS "  Output: osg_compatibility_test.html")
else()
    message(STATUS "  Emscripten: NO")
    message(STATUS "  Output: osg_compatibility_test")
endif()

message(STATUS "  OpenSceneGraph:")
message(STATUS "    Include: ${OPENSCENEGRAPH_INCLUDE_DIRS}")
message(STATUS "    Libraries: ${OPENSCENEGRAPH_LIBRARIES}")

# 帮助信息
if(NOT EMSCRIPTEN)
    message(STATUS "")
    message(STATUS "Build and run tests:")
    message(STATUS "  make osg_compatibility_test")
    message(STATUS "  make run_compatibility_tests")
    message(STATUS "  make run_compatibility_tests_verbose")
    message(STATUS "")
    message(STATUS "Manual execution:")
    message(STATUS "  ./bin/osg_compatibility_test --help")
    message(STATUS "  ./bin/osg_compatibility_test --suite core-rendering")
    message(STATUS "  ./bin/osg_compatibility_test --report custom_report.html")
endif()

/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/
//osgFX - Copyright (C) 2003 Marco <PERSON>

#ifndef OSGFX_SCRIBE_
#define OSGFX_SCRIBE_

#include <osgFX/Export>
#include <osgFX/Effect>

#include <osg/Material>
#include <osg/LineWidth>

namespace osgFX
{

    /**
     This is a two-passes effect; the first pass renders the subgraph as usual
     while the second pass switches to wireframe mode, sets up lighting and
     material to obtain a fixed (user-defined) color and then renders the subgraph.
     This effect uses the PolygonOffset attribute to avoid Z-fighting, so it
     requires at least OpenGL version 1.1.
    */
    class OSGFX_EXPORT Scribe: public Effect {
    public:
        Scribe();
        Scribe(const Scribe& copy, const osg::CopyOp& copyop = osg::CopyOp::SHALLOW_COPY);

        // effect class information
        META_Effect(
            osgFX,
            Scribe,

            "Scribe",

            "This is a two-passes effect; the first pass renders the subgraph as usual "
            "while the second pass switches to wireframe mode, sets up lighting and "
            "material to obtain a fixed (user-defined) color and then renders the subgraph.\n"
            "This effect uses the PolygonOffset attribute to avoid Z-fighting, so it "
            "requires at least OpenGL version 1.1.",

            "Marco Jez");

        /** get the wireframe color */
        inline const osg::Vec4& getWireframeColor() const;

        /** set the wireframe color */
        inline void setWireframeColor(const osg::Vec4& color);

        /** get the wireframe line width */
        inline float getWireframeLineWidth() const;

        /** set the wireframe line width */
        inline void setWireframeLineWidth(float w);

    protected:
        virtual ~Scribe() {}
        Scribe& operator=(const Scribe&) { return *this; }

        bool define_techniques();

    private:
        osg::ref_ptr<osg::Material> _wf_mat;
        osg::ref_ptr<osg::LineWidth> _wf_lw;
    };

    // INLINE METHODS

    inline const osg::Vec4& Scribe::getWireframeColor() const
    {
        return _wf_mat->getEmission(osg::Material::FRONT_AND_BACK);
    }

    inline void Scribe::setWireframeColor(const osg::Vec4& color)
    {
        _wf_mat->setEmission(osg::Material::FRONT_AND_BACK, color);
    }

    inline float Scribe::getWireframeLineWidth() const
    {
        return _wf_lw->getWidth();
    }

    inline void Scribe::setWireframeLineWidth(float w)
    {
        _wf_lw->setWidth(w);
    }

}

#endif

/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2009 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSG_VERSION
#define OSG_VERSION 1

#include <osg/Export>

extern "C" {

#define OPENSCENEGRAPH_MAJOR_VERSION    3
#define OPENSCENEGRAPH_MINOR_VERSION    7
#define OPENSCENEGRAPH_PATCH_VERSION    0
#define OPENSCENEGRAPH_SOVERSION        202

/* Convenience macro that can be used to decide whether a feature is present or not i.e.
 * #if OSG_MIN_VERSION_REQUIRED(2,9,5)
 *    your code here
 * #endif
 */
#define OSG_MIN_VERSION_REQUIRED(MAJOR, MINOR, PATCH) ((OPENSCENEGRAPH_MAJOR_VERSION>MAJOR) || (OPENSCENEGRAPH_MAJOR_VERSION==MAJOR && (OPENSCENEGRAPH_MINOR_VERSION>MINOR || (OPENSCENEGRAPH_MINOR_VERSION==MINOR && OPENSCENEGRAPH_PATCH_VERSION>=PATCH))))
#define OSG_VERSION_LESS_THAN(MAJOR, MINOR, PATCH) ((OPENSCENEGRAPH_MAJOR_VERSION<MAJOR) || (OPENSCENEGRAPH_MAJOR_VERSION==MAJOR && (OPENSCENEGRAPH_MINOR_VERSION<MINOR || (OPENSCENEGRAPH_MINOR_VERSION==MINOR && OPENSCENEGRAPH_PATCH_VERSION<PATCH))))
#define OSG_VERSION_LESS_OR_EQUAL(MAJOR, MINOR, PATCH) ((OPENSCENEGRAPH_MAJOR_VERSION<MAJOR) || (OPENSCENEGRAPH_MAJOR_VERSION==MAJOR && (OPENSCENEGRAPH_MINOR_VERSION<MINOR || (OPENSCENEGRAPH_MINOR_VERSION==MINOR && OPENSCENEGRAPH_PATCH_VERSION<=PATCH))))
#define OSG_VERSION_GREATER_THAN(MAJOR, MINOR, PATCH) ((OPENSCENEGRAPH_MAJOR_VERSION>MAJOR) || (OPENSCENEGRAPH_MAJOR_VERSION==MAJOR && (OPENSCENEGRAPH_MINOR_VERSION>MINOR || (OPENSCENEGRAPH_MINOR_VERSION==MINOR && OPENSCENEGRAPH_PATCH_VERSION>PATCH))))
#define OSG_VERSION_GREATER_OR_EQUAL(MAJOR, MINOR, PATCH) ((OPENSCENEGRAPH_MAJOR_VERSION>MAJOR) || (OPENSCENEGRAPH_MAJOR_VERSION==MAJOR && (OPENSCENEGRAPH_MINOR_VERSION>MINOR || (OPENSCENEGRAPH_MINOR_VERSION==MINOR && OPENSCENEGRAPH_PATCH_VERSION>=PATCH))))


/**
  * osgGetVersion() returns the library version number.
  * Numbering convention : OpenSceneGraph-1.0 will return 1.0 from osgGetVersion.
  *
  * This C function can be also used to check for the existence of the OpenSceneGraph
  * library using autoconf and its m4 macro AC_CHECK_LIB.
  *
  * Here is the code to add to your configure.in:
 \verbatim
 #
 # Check for the OpenSceneGraph (OSG) library
 #
 AC_CHECK_LIB(osg, osgGetVersion, ,
    [AC_MSG_ERROR(OpenSceneGraph library not found. See http://www.openscenegraph.org)],)
 \endverbatim
*/
extern OSG_EXPORT const char* osgGetVersion();

/** The osgGetSOVersion() method returns the OpenSceneGraph shared object version number. */
extern OSG_EXPORT const char* osgGetSOVersion();

/** The osgGetLibraryName() method returns the library name in human-friendly form. */
extern OSG_EXPORT const char* osgGetLibraryName();

// old defines for backwards compatibility.
#define OSG_VERSION_MAJOR OPENSCENEGRAPH_MAJOR_VERSION
#define OSG_VERSION_MINOR OPENSCENEGRAPH_MINOR_VERSION
#define OSG_VERSION_PATCH OPENSCENEGRAPH_PATCH_VERSION

#define OSG_VERSION_RELEASE OSG_VERSION_PATCH
#define OSG_VERSION_REVISION 0


}

#endif

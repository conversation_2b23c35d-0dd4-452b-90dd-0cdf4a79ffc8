cmake -B build_desk -S . -G "Visual Studio 17 2022" -A x64 -DCMAKE_CXX_STANDARD=20 -DCMAKE_INSTALL_PREFIX=redist_desk -DBUILD_OSG_APPLICATIONS=ON -DBUILD_OSG_EXAMPLES=ON -DDYNAMIC_OPENSCENEGRAPH=OFF

cmake --build build_desk --config Release --parallel 8

cmake --install build_desk


emcmake cmake -B build_wasm -S . -G Ninja -DCMAKE_BUILD_TYPE=Release -DCMAKE_CXX_STANDARD=14 -DOSG_GLES2_AVAILABLE=ON -DOSG_GLES3_AVAILABLE=ON -DOSG_GL1_AVAILABLE=OFF -DOSG_GL2_AVAILABLE=OFF -DOSG_GL3_AVAILABLE=OFF -DOSG_WINDOWING_SYSTEM=None -DBUILD_OSG_APPLICATIONS=ON -DBUILD_OSG_EXAMPLES=ON -DDYNAMIC_OPENSCENEGRAPH=OFF -DCMAKE_INSTALL_PREFIX=./redist_wasm

cmake --build build_wasm --parallel 4

cmake --install build_wasm
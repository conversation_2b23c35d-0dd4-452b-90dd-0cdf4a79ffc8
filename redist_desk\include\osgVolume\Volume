/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2009 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSGVOLUME
#define OSGVOLUME 1

#include <osg/Group>
#include <osg/CoordinateSystemNode>

#include <osgVolume/VolumeTile>

namespace osgVolume {

/** Volume provides a framework for loosely coupling 3d image VolumeTile's with volume algorithms.
  * This allows VolumeTechnique's to be plugged in at runtime.*/
class OSGVOLUME_EXPORT Volume : public osg::Group
{
    public:

        Volume();

        /** Copy constructor using CopyOp to manage deep vs shallow copy.*/
        Volume(const Volume&,const osg::CopyOp& copyop=osg::CopyOp::SHALLOW_COPY);

        META_Node(osgVolume, Volume);

        virtual void traverse(osg::NodeVisitor& nv);

        /** Get the VolumeTile for a given VolumeTileID.*/
        VolumeTile* getVolumeTile(const TileID& tileID);

        /** Get the const VolumeTile for a given VolumeTileID.*/
        const VolumeTile* getVolumeTile(const TileID& tileID) const;


        /** Set the VolumeTechnique prototype that nested VolumeTile should clone if they haven't already been assigned a volume rendering technique. */
        void setVolumeTechniquePrototype(VolumeTechnique* volumeTechnique) { _volumeTechnique = volumeTechnique; }

        /** Get the VolumeTechnique prototype. */
        VolumeTechnique* getVolumeTechniquePrototype() { return _volumeTechnique.get(); }

        /** Get the const VolumeTechnique prototype. */
        const VolumeTechnique* getVolumeTechniquePrototype() const { return _volumeTechnique.get(); }


    protected:

        virtual ~Volume();

        friend class VolumeTile;

        void dirtyRegisteredVolumeTiles();

        void registerVolumeTile(VolumeTile* tile);
        void unregisterVolumeTile(VolumeTile* tile);

        typedef std::map< TileID, VolumeTile* > VolumeTileMap;
        typedef std::set< VolumeTile* >         VolumeTileSet;

        mutable OpenThreads::Mutex              _mutex;
        VolumeTileSet                           _volumeTileSet;
        VolumeTileMap                           _volumeTileMap;

        osg::ref_ptr<VolumeTechnique>           _volumeTechnique;
};

}

#endif

/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/
//osgParticle - Copyright (C) 2002 <PERSON>

#ifndef OSGPARTICLE_EMITTER
#define OSGPARTICLE_EMITTER 1

#include <osgParticle/Export>
#include <osgParticle/ParticleProcessor>
#include <osgParticle/Particle>

#include <osg/Object>
#include <osg/Node>
#include <osg/NodeVisitor>
#include <osg/CopyOp>

namespace osgParticle
{

    /** An abstract base class for particle emitters.
        Descendant classes must override the <CODE>emitParticles()</CODE> method to generate new particles by
        calling the <CODE>ParticleSystem::createParticle()</CODE> method on the particle system associated
        to the emitter.
    */
    class OSGPARTICLE_EXPORT Emitter: public ParticleProcessor {
    public:
        Emitter();
        Emitter(const Emitter& copy, const osg::CopyOp& copyop = osg::CopyOp::SHALLOW_COPY);

        virtual const char* libraryName() const { return "osgParticle"; }
        virtual const char* className() const { return "Emitter"; }
        virtual bool isSameKindAs(const osg::Object* obj) const { return dynamic_cast<const Emitter*>(obj) != 0; }
        virtual void accept(osg::NodeVisitor& nv) { if (nv.validNodeMask(*this)) { nv.pushOntoNodePath(this); nv.apply(*this); nv.popFromNodePath(); } }

        void setParticleSystem(ParticleSystem* ps);

        void setEstimatedMaxNumOfParticles(int num);
        int getEstimatedMaxNumOfParticles() const { return _estimatedMaxNumOfParticles; }


        /// Get the particle template.
        inline const Particle& getParticleTemplate() const;

        /// Set the particle template (particle is copied).
        inline void setParticleTemplate(const Particle& p);

        /// Return whether the particle system's default template should be used.
        inline bool getUseDefaultTemplate() const;

        /** Set whether the default particle template should be used.
            When this flag is true, the particle template is ignored, and the
            particle system's default template is used instead.
        */
        inline void setUseDefaultTemplate(bool v);

    protected:
        virtual ~Emitter() {}
        Emitter& operator=(const Emitter&) { return *this; }

        inline void process(double dt);

        virtual void emitParticles(double dt) = 0;

        bool _usedeftemp;
        Particle _ptemp;

        int _estimatedMaxNumOfParticles;
    };

    // INLINE FUNCTIONS

    inline const Particle& Emitter::getParticleTemplate() const
    {
        return _ptemp;
    }

    inline void Emitter::setParticleTemplate(const Particle& p)
    {
        _ptemp = p;
        _usedeftemp = false;
    }

    inline bool Emitter::getUseDefaultTemplate() const
    {
        return _usedeftemp;
    }

    inline void Emitter::setUseDefaultTemplate(bool v)
    {
        _usedeftemp = v;
    }

    inline void Emitter::process(double dt)
    {
        emitParticles(dt);
    }


}


#endif


/*  -*-c++-*-
 *  Copyright (C) 2009 <PERSON><PERSON> <<EMAIL>>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
 */


#ifndef OSGANIMATION_STACKED_QUATERNION_ELEMENT
#define OSGANIMATION_STACKED_QUATERNION_ELEMENT 1

#include <osgAnimation/Export>
#include <osgAnimation/StackedTransformElement>
#include <osgAnimation/Target>

namespace osgAnimation
{

    class OSGANIMATION_EXPORT StackedQuaternionElement : public StackedTransformElement
    {
    public:
        META_Object(osgAnimation, StackedQuaternionElement);

        StackedQuaternionElement();
        StackedQuaternionElement(const StackedQuaternionElement&, const osg::CopyOp&);
        StackedQuaternionElement(const std::string&, const osg::Quat& q = osg::Quat(0,0,0,1));
        StackedQuaternionElement(const osg::Quat&);

        void applyToMatrix(osg::Matrix& matrix) const;
        osg::Matrix getAsMatrix() const;
        bool isIdentity() const;
        void update(float t = 0.0);

        const osg::Quat& getQuaternion() const;
        void setQuaternion(const osg::Quat&);
        virtual Target* getOrCreateTarget();
        virtual Target* getTarget();
        virtual const Target* getTarget() const;

    protected:
        osg::Quat _quaternion;
        osg::ref_ptr<QuatTarget> _target;
    };

}

#endif

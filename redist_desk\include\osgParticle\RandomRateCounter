/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/
//osgParticle - Copyright (C) 2002 <PERSON>

#ifndef OSGPARTICLE_RANDOMRATE_COUNTER
#define OSGPARTICLE_RANDOMRATE_COUNTER 1

#include <osgParticle/VariableRateCounter>

#include <osg/CopyOp>
#include <osg/Object>

namespace osgParticle
{

    class RandomRateCounter: public VariableRateCounter {
    public:
        inline RandomRateCounter();
        inline RandomRateCounter(const RandomRateCounter& copy, const osg::CopyOp& copyop = osg::CopyOp::SHALLOW_COPY);

        META_Object(osgParticle, RandomRateCounter);

        /// Return the number of particles to be created in this frame
        inline int numParticlesToCreate(double dt) const;

    protected:
        virtual ~RandomRateCounter() {}

        mutable float _np;
    };

    // INLINE FUNCTIONS

    inline RandomRateCounter::RandomRateCounter()
    : VariableRateCounter(), _np(0)
    {
    }

    inline RandomRateCounter::RandomRateCounter(const RandomRateCounter& copy, const osg::CopyOp& copyop)
    : VariableRateCounter(copy, copyop), _np(copy._np)
    {
    }

    inline int RandomRateCounter::numParticlesToCreate(double dt) const
    {
        // compute the number of new particles, clamping it to 1 second of particles at the maximum rate
        float numNewParticles = osg::minimum(static_cast<float>(dt * getRateRange().get_random()), getRateRange().maximum);

        // add the number of new particles to value carried over from the previous call
       _np += numNewParticles;

        // round down the number of particles.
        int n = static_cast<int>(_np);

        // take away the number of rounded number of particles leaving the decimal place
        // this is done so that two frames of 0.5's will results in first frame 0 new particles, second frame 1
        _np -= n;

        // return the rounded number of particles to be created
        return n;
    }

}


#endif

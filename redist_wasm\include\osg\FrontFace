/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSG_FRONTFACE
#define OSG_FRONTFACE 1

#include <osg/StateAttribute>
#include <osg/GL>

namespace osg {

/** Class to specify the orientation of front-facing polygons.
*/
class OSG_EXPORT FrontFace : public StateAttribute
{
    public :

        enum Mode {
            CLOCKWISE = GL_CW,
            COUNTER_CLOCKWISE = GL_CCW
        };

        FrontFace(Mode face=COUNTER_CLOCKWISE);

        /** Copy constructor using CopyOp to manage deep vs shallow copy.*/
        FrontFace(const FrontFace& ff,const CopyOp& copyop=CopyOp::SHALLOW_COPY):
            StateAttribute(ff,copyop),
            _mode(ff._mode) {}

        META_StateAttribute(osg, FrontFace, FRONTFACE);

        /** return -1 if *this < *rhs, 0 if *this==*rhs, 1 if *this>*rhs.*/
        virtual int compare(const StateAttribute& sa) const
        {
            // check the types are equal and then create the rhs variable
            // used by the COMPARE_StateAttribute_Parameter macros below.
            COMPARE_StateAttribute_Types(FrontFace,sa)

            // compare each parameter in turn against the rhs.
            COMPARE_StateAttribute_Parameter(_mode)

            return 0; // passed all the above comparison macros, must be equal.
        }

        inline void setMode(Mode mode) { _mode = mode; }
        inline Mode getMode() const    { return _mode; }

        virtual void apply(State& state) const;

    protected:

        virtual ~FrontFace();

        Mode _mode;

};

}

#endif

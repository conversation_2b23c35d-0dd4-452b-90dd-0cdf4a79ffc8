/*  -*-c++-*-
 *  Copyright (C) 2009 <PERSON><PERSON> <<EMAIL>>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSGANIMATION_ACTION_CALLBACK_H
#define OSGANIMATION_ACTION_CALLBACK_H

#include <osgAnimation/Export>
#include <osgAnimation/Action>

namespace osgAnimation
{

    /** Callback used to run new action on the timeline.*/
    class OSGANIMATION_EXPORT RunAction : public Action::Callback
    {
    public:
        RunAction(Action* a, int priority = 0) : _action(a), _priority(priority) {}
        virtual void operator()(Action* action, ActionVisitor* visitor);

        Action* getAction() const { return _action.get(); }
        int getPriority() const { return _priority; }
    protected:
        osg::ref_ptr<Action> _action;
        int _priority;

    };

}

#endif

/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSG_CLIPNODE
#define OSG_CLIPNODE 1

#include <osg/Group>
#include <osg/ClipPlane>

namespace osg {

/** Node for defining the position of ClipPlanes in the scene.*/
class OSG_EXPORT ClipNode : public Group
{

    public:

        typedef std::vector<ref_ptr<ClipPlane> > ClipPlaneList;


        ClipNode();

        ClipNode(const ClipNode& es, const CopyOp& copyop=CopyOp::SHALLOW_COPY);

        META_Node(osg, ClipNode);

        enum ReferenceFrame
        {
            RELATIVE_RF,
            ABSOLUTE_RF
        };

        /** Set the ClipNode's ReferenceFrame, either to be relative to its
          * parent reference frame, or relative to an absolute coordinate
          * frame. RELATIVE_RF is the default.
          * Note: setting the ReferenceFrame to be ABSOLUTE_RF will
          * also set the CullingActive flag to false on the ClipNode (and
          * consequently all of its parents), thereby disabling culling of it and
          * all its parents.  This is necessary to prevent inappropriate
          * culling, but may impact cull times if the absolute ClipNode is
          * deep in the scene graph.  It is therefore recommended to only use
          * absolute ClipNode at the top of the scene.
        */
        void setReferenceFrame(ReferenceFrame rf);

        ReferenceFrame getReferenceFrame() const { return _referenceFrame; }


        /** Creates six clip planes corresponding to the given BoundingBox. */
        void createClipBox(const BoundingBox& bb,unsigned int clipPlaneNumberBase=0);

        /** Adds the clipplane. Returns true on success, and false if the plane
          * has already been added, or if clipplane is NULL. */
        bool addClipPlane(ClipPlane* clipplane);

        /** Removes the clipplane. Returns true on success, false if clipplane
         * isn't in this ClipNode.*/
        bool removeClipPlane(ClipPlane* clipplane);

        /** Remove the ClipPlane with the given index. Returns true on success,
          * false if pos is not a valid plane index. */
        bool removeClipPlane(unsigned int pos);

        /** Returns the number of ClipPlanes. */
        inline unsigned int getNumClipPlanes() const { return _planes.size(); }


        /** Get ClipPlane at the given index position. */
        inline ClipPlane* getClipPlane(unsigned int pos) { return _planes[pos].get(); }

        /** Get const ClipPlane at the given index position. */
        inline const ClipPlane* getClipPlane(unsigned int pos) const { return _planes[pos].get(); }

        /** Set the ClipPlaneList. */
        inline void setClipPlaneList(const ClipPlaneList& cpl) { _planes=cpl; }

        /** Get the ClipPlaneList. */
        inline ClipPlaneList& getClipPlaneList() { return _planes; }

        /** Get the const ClipPlaneList. */
        inline const ClipPlaneList& getClipPlaneList() const { return _planes; }

        /** Set the GLModes for all ClipPlanes, on the StateSet. */
        void setStateSetModes(StateSet&,StateAttribute::GLModeValue) const;

        /** Set up the local StateSet. */
        void setLocalStateSetModes(StateAttribute::GLModeValue=StateAttribute::ON);

        virtual BoundingSphere computeBound() const;

    protected:

        virtual ~ClipNode();

        StateAttribute::GLModeValue     _value;
        ClipPlaneList                   _planes;

        ReferenceFrame                  _referenceFrame;
};

}

#endif

# This file will be configured to contain variables for CPack. These variables
# should be set in the CMake list file of the project before CPack module is
# included. Example variables are:
#   CPACK_GENERATOR                     - Generator used to create package
#   CPACK_INSTALL_CMAKE_PROJECTS        - For each project (path, name, component)
#   CPACK_CMAKE_GENERATOR               - CMake Generator used for the projects
#   CPACK_INSTALL_COMMANDS              - Extra commands to install components
#   CPACK_INSTALL_DIRECTORIES           - Extra directories to install
#   CPACK_PACKAGE_DESCRIPTION_FILE      - Description file for the package
#   CPACK_PACKAGE_DESCRIPTION_SUMMARY   - Summary of the package
#   CPACK_PACKAGE_EXECUTABLES           - List of pairs of executables and labels
#   CPACK_PACKAGE_FILE_NAME             - Name of the package generated
#   CPACK_PACKAGE_ICON                  - Icon used for the package
#   CPACK_PACKAGE_INSTALL_DIRECTORY     - Name of directory for the installer
#   CPACK_PACKAGE_NAME                  - Package project name
#   CPACK_PACKAGE_VENDOR                - Package project vendor
#   CPACK_PACKAGE_VERSION               - Package project version
#   CPACK_PACKAGE_VERSION_MAJOR         - Package project version (major)
#   CPACK_PACKAGE_VERSION_MINOR         - Package project version (minor)
#   CPACK_PACKAGE_VERSION_PATCH         - Package project version (patch)

# There are certain generator specific ones

# NSIS Generator:
#   CPACK_PACKAGE_INSTALL_REGISTRY_KEY  - Name of the registry key for the installer
#   CPACK_NSIS_EXTRA_UNINSTALL_COMMANDS - Extra commands used during uninstall
#   CPACK_NSIS_EXTRA_INSTALL_COMMANDS   - Extra commands used during install


SET(CPACK_BINARY_BUNDLE "${CPACK_BINARY_BUNDLE}")
SET(CPACK_BINARY_CYGWIN "${CPACK_BINARY_CYGWIN}")
SET(CPACK_BINARY_DEB "${CPACK_BINARY_DEB}")
SET(CPACK_BINARY_NSIS "${CPACK_BINARY_NSIS}")
SET(CPACK_BINARY_OSXX11 "${CPACK_BINARY_OSXX11}")
SET(CPACK_BINARY_PACKAGEMAKER "${CPACK_BINARY_PACKAGEMAKER}")
SET(CPACK_BINARY_RPM "${CPACK_BINARY_RPM}")
SET(CPACK_BINARY_STGZ "${CPACK_BINARY_STGZ}")
SET(CPACK_BINARY_TBZ2 "${CPACK_BINARY_TBZ2}")
SET(CPACK_BINARY_TGZ "${CPACK_BINARY_TGZ}")
SET(CPACK_BINARY_TZ "${CPACK_BINARY_TZ}")
SET(CPACK_BINARY_ZIP "${CPACK_BINARY_ZIP}")
SET(CPACK_CMAKE_GENERATOR "${CMAKE_GENERATOR}")
SET(CPACK_COMPONENTS_ALL "${CPACK_COMPONENTS_ALL}")
SET(CPACK_COMPONENT_UNSPECIFIED_HIDDEN "TRUE")
SET(CPACK_COMPONENT_UNSPECIFIED_REQUIRED "TRUE")
SET(CPACK_GENERATOR "${CPACK_GENERATOR}")
SET(CPACK_INSTALL_CMAKE_PROJECTS "${OpenSceneGraph_BINARY_DIR};OpenSceneGraph;${OSG_CPACK_COMPONENT};/")
SET(CPACK_INSTALL_PREFIX "${CMAKE_INSTALL_PREFIX}")
SET(CPACK_PACKAGING_INSTALL_PREFIX "${CMAKE_INSTALL_PREFIX}")
SET(CPACK_MODULE_PATH "${OpenSceneGraph_SOURCE_DIR}/CMakeModules;")
SET(CPACK_NSIS_DISPLAY_NAME "${CMAKE_PROJECT_NAME} ${OPENSCENEGRAPH_VERSION}")
SET(CPACK_NSIS_INSTALLER_ICON_CODE "")
SET(CPACK_NSIS_INSTALLER_MUI_ICON_CODE "")
SET(CPACK_OUTPUT_CONFIG_FILE "${PROJECT_BINARY_DIR}/CPackConfig-${OSG_CPACK_COMPONENT}.cmake")
SET(CPACK_PACKAGE_DEFAULT_LOCATION "/")
SET(CPACK_PACKAGE_DESCRIPTION_FILE "${OpenSceneGraph_SOURCE_DIR}/README.md")
SET(CPACK_PACKAGE_DESCRIPTION_SUMMARY "The OpenSceneGraph is an open source high performance 3d graphics toolkit")
SET(CPACK_PACKAGE_FILE_NAME "${OSG_PACKAGE_FILE_NAME}")
SET(CPACK_PACKAGE_INSTALL_DIRECTORY "${CPACK_PACKAGE_INSTALL_DIRECTORY}")
SET(CPACK_PACKAGE_INSTALL_REGISTRY_KEY "${CMAKE_PROJECT_NAME}-${OPENSCENEGRAPH_VERSION}")
SET(CPACK_PACKAGE_NAME "${CPACK_PACKAGE_NAME}")
SET(CPACK_PACKAGE_RELOCATABLE "true")
SET(CPACK_PACKAGE_VENDOR "The OpenSceneGraph developers and contributors lead by Robert Osfield")
SET(CPACK_PACKAGE_VERSION "${OPENSCENEGRAPH_VERSION}")
SET(CPACK_PACKAGE_VERSION_MAJOR "${OPENSCENEGRAPH_MAJOR_VERSION}")
SET(CPACK_PACKAGE_VERSION_MINOR "${OPENSCENEGRAPH_MINOR_VERSION}")
SET(CPACK_PACKAGE_VERSION_PATCH "${OPENSCENEGRAPH_PATCH_VERSION}")
SET(CPACK_RESOURCE_FILE_LICENSE "${OpenSceneGraph_SOURCE_DIR}/LICENSE.txt")
SET(CPACK_RESOURCE_FILE_README "${OpenSceneGraph_SOURCE_DIR}/README.md")
SET(CPACK_RESOURCE_FILE_WELCOME "${OpenSceneGraph_SOURCE_DIR}/NEWS.txt")
SET(CPACK_STRIP_FILES "ON")

# DEBIAN OPTIONS
IF(${CPACK_GENERATOR} STREQUAL "DEB")
    SET(CPACK_DEBIAN_PACKAGE_MAINTAINER "${OPENSCENEGRAPH_PACKAGE_MAINTAINER}")
    SET(CPACK_DEBIAN_PACKAGE_HOMEPAGE    "http://www.openscenegraph.org")
    SET(CPACK_DEBIAN_PACKAGE_SECTION     "Development")
    SET(CPACK_DEBIAN_PACKAGE_DEPENDS     "${OSG_PACKAGE_DEPENDS}")
    SET(CPACK_DEBIAN_PACKAGE_CONFLICTS   "${OSG_PACKAGE_CONFLICTS}")
    SET(CPACK_DEBIAN_PACKAGE_CONTROL_EXTRA "${OpenSceneGraph_SOURCE_DIR}/debian_scripts/postinst;")
ENDIF()

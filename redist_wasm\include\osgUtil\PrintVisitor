/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2009 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSGUTIL_PRINTVISITOR
#define OSGUTIL_PRINTVISITOR 1

#include <osg/NodeVisitor>
#include <osgUtil/Export>

#include <ostream>

namespace osgUtil {

class OSGUTIL_EXPORT PrintVisitor : public osg::NodeVisitor
{
    public:

        PrintVisitor(std::ostream& out, int indent=0, int step=2);

        void apply(osg::Node& node);

        std::ostream& output()
        {
            for(unsigned int i=0;i<_indent; ++i) _out<<" ";
            return _out;
        }

        void enter() { _indent += _step; }
        void leave() { _indent -= _step; }

    protected:

        PrintVisitor& operator = (const PrintVisitor&) { return *this; }

        std::ostream& _out;
        unsigned int _indent;
        unsigned int _step;
};

}

#endif


<!doctypehtml><html lang=en><head><meta charset=utf-8><meta content="width=device-width,initial-scale=1"name=viewport><title>OSG Interface Compatibility Tests</title><style>body{font-family:Arial,sans-serif;margin:0;padding:20px;background-color:#f5f5f5}.container{max-width:1200px;margin:0 auto;background-color:#fff;padding:20px;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,.1)}h1{color:#333;text-align:center;margin-bottom:30px}.info-panel{background-color:#e8f4f8;border:1px solid #bee5eb;border-radius:4px;padding:15px;margin-bottom:20px}.canvas-container{text-align:center;margin:20px 0}canvas{border:1px solid #ddd;background-color:#000}.output-container{margin-top:20px}.output{background-color:#f8f9fa;border:1px solid #dee2e6;border-radius:4px;padding:15px;font-family:'Courier New',monospace;font-size:12px;height:300px;overflow-y:auto;white-space:pre-wrap}.controls{margin:20px 0;text-align:center}button{background-color:#007bff;color:#fff;border:none;padding:10px 20px;margin:0 5px;border-radius:4px;cursor:pointer;font-size:14px}button:hover{background-color:#0056b3}button:disabled{background-color:#6c757d;cursor:not-allowed}.status{text-align:center;margin:10px 0;font-weight:700}.status.loading{color:#ffc107}.status.success{color:#28a745}.status.error{color:#dc3545}.progress{width:100%;height:20px;background-color:#e9ecef;border-radius:10px;overflow:hidden;margin:10px 0}.progress-bar{height:100%;background-color:#007bff;width:0%;transition:width .3s ease}</style></head><body><div class=container><h1>OSG Interface Compatibility Tests</h1><div class=info-panel><h3>测试说明</h3><p>此测试套件用于验证OSG（OpenSceneGraph）接口在WebAssembly环境下的兼容性。</p><p>测试将检查核心渲染接口、状态管理、缓冲区操作等关键功能。</p><p><strong>平台：</strong> WebAssembly/Emscripten</p><p><strong>渲染API：</strong> WebGL 2.0</p></div><div class=canvas-container><canvas height=600 id=canvas width=800></canvas></div><div class=controls><button id=startTests onclick=startTests()>开始测试</button> <button id=clearOutput onclick=clearOutput()>清除输出</button> <button id=downloadReport onclick=downloadReport() disabled>下载报告</button></div><div class=status id=status>准备就绪</div><div class=progress><div class=progress-bar id=progressBar></div></div><div class=output-container><h3>测试输出</h3><div class=output id=output></div></div></div><script>var Module={preRun:[],postRun:[],print:function(t){arguments.length>1&&(t=Array.prototype.slice.call(arguments).join(" ")),console.log(t),appendOutput(t)},printErr:function(t){arguments.length>1&&(t=Array.prototype.slice.call(arguments).join(" ")),console.error(t),appendOutput("[ERROR] "+t,"error")},canvas:function(){var t=document.getElementById("canvas");return t.addEventListener("webglcontextlost",(function(t){alert("WebGL context lost. You will need to reload the page."),t.preventDefault()}),!1),t}(),setStatus:function(t){updateStatus(t)},totalDependencies:0,monitorRunDependencies:function(t){this.totalDependencies=Math.max(this.totalDependencies,t),updateProgress((this.totalDependencies-t)/this.totalDependencies*100),0==t?(updateStatus("所有依赖已加载","success"),document.getElementById("startTests").disabled=!1):updateStatus("加载依赖中... ("+t+"/"+this.totalDependencies+")","loading")}};function appendOutput(t,e="normal"){var n=document.getElementById("output"),a="["+(new Date).toLocaleTimeString()+"] "+t+"\n";"error"===e&&(a='<span style="color: red;">'+a+"</span>"),n.innerHTML+=a,n.scrollTop=n.scrollHeight}function updateStatus(t,e="normal"){var n=document.getElementById("status");n.textContent=t,n.className="status "+e}function updateProgress(t){document.getElementById("progressBar").style.width=t+"%"}function startTests(){document.getElementById("startTests").disabled=!0,updateStatus("正在运行测试...","loading"),appendOutput("开始OSG接口兼容性测试...")}function clearOutput(){document.getElementById("output").innerHTML=""}function downloadReport(){appendOutput("报告下载功能待实现...")}function checkWebGLSupport(){var t=document.createElement("canvas"),e=t.getContext("webgl2")||t.getContext("webgl");return e?(appendOutput("WebGL支持检查通过"),appendOutput("WebGL版本: "+e.getParameter(e.VERSION)),appendOutput("渲染器: "+e.getParameter(e.RENDERER)),appendOutput("厂商: "+e.getParameter(e.VENDOR)),!0):(updateStatus("错误：浏览器不支持WebGL","error"),appendOutput("错误：此浏览器不支持WebGL，无法运行测试。","error"),!1)}updateStatus("正在加载...","loading"),updateProgress(0),window.addEventListener("load",(function(){checkWebGLSupport()||(document.getElementById("startTests").disabled=!0)}))</script><script async src=osg_compatibility_test.js></script></body></html>
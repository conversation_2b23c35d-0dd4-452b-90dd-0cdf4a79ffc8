/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/
//osgParticle - Copyright (C) 2002 <PERSON>

#ifndef OSGPARTICLE_OPERATOR
#define OSGPARTICLE_OPERATOR 1

#include <osgParticle/Program>

#include <osg/CopyOp>
#include <osg/Object>
#include <osg/Matrix>

namespace osgParticle
{

    // forward declaration to avoid including the whole header file
    class Particle;

    /** An abstract base class used by <CODE>ModularProgram</CODE> to perform operations on particles before they are updated.
        To implement a new operator, derive from this class and override the <CODE>operate()</CODE> method.
        You should also override the <CODE>beginOperate()</CODE> method to query the calling program for the reference frame
        used, and initialize the right transformations if needed.
    */
    class Operator: public osg::Object {
    public:
        inline Operator();
        inline Operator(const Operator& copy, const osg::CopyOp& copyop = osg::CopyOp::SHALLOW_COPY);

        virtual const char* libraryName() const { return "osgParticle"; }
        virtual const char* className() const { return "Operator"; }
        virtual bool isSameKindAs(const osg::Object* obj) const { return dynamic_cast<const Operator* >(obj) != 0; }

        /// Get whether this operator is enabled.
        inline bool isEnabled() const;

        /// Enable or disable this operator.
        inline void setEnabled(bool v);

        /** Do something on all emitted particles.
            This method is called by <CODE>ModularProgram</CODE> objects to perform some operations
            on the particles. By default, it will call the <CODE>operate()</CODE> method for each particle.
            You must override it in descendant classes.
        */
        virtual void operateParticles(ParticleSystem* ps, double dt)
        {
            int n = ps->numParticles();
            for (int i=0; i<n; ++i)
            {
                Particle* P = ps->getParticle(i);
                if (P->isAlive() && isEnabled()) operate(P, dt);
            }
        }

        /**    Do something on a particle.
            You must override it in descendant classes. Common operations
            consist of modifying the particle's velocity vector. The <CODE>dt</CODE> parameter is
            the time elapsed from last operation.
        */
        virtual void operate(Particle* P, double dt) = 0;

        /** Do something before processing particles via the <CODE>operate()</CODE> method.
            Overriding this method could be necessary to query the calling <CODE>Program</CODE> object
            for the current reference frame. If the reference frame is RELATIVE_RF, then your
            class should prepare itself to do all operations in local coordinates.
        */
        virtual void beginOperate(Program *) {}

        /// Do something after all particles have been processed.
        virtual void endOperate() {}

    protected:
        virtual ~Operator() {}
        Operator &operator=(const Operator &) { return *this; }

    private:
        bool _enabled;
    };

    // INLINE FUNCTIONS

    inline Operator::Operator()
    : osg::Object(), _enabled(true)
    {
    }

    inline Operator::Operator(const Operator& copy, const osg::CopyOp& copyop)
    : osg::Object(copy, copyop), _enabled(copy._enabled)
    {
    }

    inline bool Operator::isEnabled() const
    {
        return _enabled;
    }

    inline void Operator::setEnabled(bool v)
    {
        _enabled = v;
    }


}

#endif

/*  -*-c++-*-
 *  Copyright (C) 2009 <PERSON><PERSON> <<EMAIL>>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
 */

#ifndef OSGANIMATION_ACTION_BLENDOUT_H
#define OSGANIMATION_ACTION_BLENDOUT_H

#include <osgAnimation/Action>
#include <osgAnimation/Export>


namespace osgAnimation {


    /// blend out from weight to 0 in duration
    class OSGANIMATION_EXPORT ActionBlendOut : public Action
    {
    public:
        META_Action(osgAnimation, ActionBlendOut);
        ActionBlendOut();
        ActionBlendOut(const ActionBlendOut& a, const osg::CopyOp& c);
        ActionBlendOut(Animation* animation, double duration);
        Animation* getAnimation() { return _animation.get(); }
        double getWeight() const { return _weight;}
        void computeWeight(unsigned int frame);

    protected:
        double _weight;
        osg::ref_ptr<Animation> _animation;

    };

}

#endif

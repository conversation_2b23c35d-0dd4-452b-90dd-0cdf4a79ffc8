<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="hello">Hello World, osgViewer!</string>
    <string name="app_name">osgAndroidExample</string>
    <color name="uiControlBar">#BBBBBB</color>
    <string name="uiButtonLight">Light (ON/OFF)</string>
    <string name="uiButtonCenter">Center View</string>
    <string name="uiButtonChangeNavigation">Change Navigation</string>
    <string name="uiTextEntryText">Write address</string>
    <string name="uiToastNavPrincipal">Main navigation</string>
    <string name="uiToastNavSecond">Second Navigation</string>
    <string name="uiToastLightOn">Light ON</string>
    <string name="uiToastLightOff">Light OFF</string>
    <string name="menuLoadObject">Load Object</string>
    <string name="menuLoadObjectCondensed">L. Object</string>
    <string name="menuDeleteObject">Delete Object</string>
    <string name="menuDeleteObjectCondensed">D. Object</string>
    <string name="menuCleanScene">Clean Scene</string>
    <string name="menuCleanSceneCondensed">Clean</string>
    <string name="menuChangeBackground">Change Background</string>
    <string name="menuChangeBackgroundCondensed">C.Background</string>
    <string name="menuShowKeyboard">Show Keyboard</string>
    <string name="menuShowKeyboardCondensed">S.Keyboard</string>
    <string name="uiDialogTextChoseRemove">Select Object</string>
    <string name="uiDialogTextAddress">Write file address</string>
    <string name="uiDialogOk">Accept</string>
    <string name="uiDialogCancel">Cancel</string>
</resources>

/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSG_MULTISWITCH
#define OSG_MULTISWITCH 1

#include <osg/Group>
#include <osgSim/Export>

namespace osgSim {

/** MultiSwitch is a Group node which allows switching between sets of selected children.
    MultiSwitch is based on the OpenFlight switch behaviour.
*/
class OSGSIM_EXPORT MultiSwitch : public osg::Group
{
    public :


        MultiSwitch();

        /** Copy constructor using CopyOp to manage deep vs shallow copy.*/
        MultiSwitch(const MultiSwitch&,const osg::CopyOp& copyop=osg::CopyOp::SHALLOW_COPY);

        META_Node(osgSim, MultiSwitch);

        virtual void traverse(osg::NodeVisitor& nv);

        void setNewChildDefaultValue(bool value) { _newChildDefaultValue = value; }

        bool getNewChildDefaultValue() const { return _newChildDefaultValue; }

        virtual bool addChild( osg::Node *child );

        virtual bool insertChild( unsigned int index, osg::Node *child );

        virtual bool removeChild( osg::Node *child );

        void setValue(unsigned int switchSet, unsigned int pos,bool value);

        bool getValue(unsigned int switchSet, unsigned int pos) const;

        void setChildValue(const osg::Node* child,unsigned int switchSet, bool value);

        bool getChildValue(const osg::Node* child,unsigned int switchSet) const;

        /** Set all the children off (false), and set the new default child value to off (false).*/
        bool setAllChildrenOff(unsigned int switchSet);

        /** Set all the children on (true), and set the new default child value to on (true).*/
        bool setAllChildrenOn(unsigned int switchSet);

        /** Set a single child to be on, MultiSwitch off all other children.*/
        bool setSingleChildOn(unsigned int switchSet, unsigned int pos);

        /** Set which of the available switch set lists to use.*/
        void setActiveSwitchSet(unsigned int switchSet) { _activeSwitchSet = switchSet; }

        /** Get which of the available switch set lists to use.*/
        unsigned int getActiveSwitchSet() const { return _activeSwitchSet; }

        typedef std::vector<bool>        ValueList;
        typedef std::vector<ValueList>   SwitchSetList;
        typedef std::vector<std::string> SwitchSetNameList;

        /** Set the compile set of different values.*/
        void setSwitchSetList(const SwitchSetList& switchSetList);

        /** Get the compile set of different values.*/
        const SwitchSetList& getSwitchSetList() const { return _values; }

        /** Set the a single set of different values for a particular switch set.*/
        void setValueList(unsigned int switchSet, const ValueList& values);

        /** Get the a single set of different values for a particular switch set.*/
        const ValueList& getValueList(unsigned int switchSet) const { return _values[switchSet]; }

        void setValueName(unsigned int switchSet, const std::string& name);

        const std::string& getValueName(unsigned int switchSet) const { return _valueNames[switchSet]; }

    protected :

        virtual ~MultiSwitch() {}

        void expandToEncompassSwitchSet(unsigned int switchSet);

        // this is effectively a list of bit mask.
        bool              _newChildDefaultValue;
        unsigned int      _activeSwitchSet;
        SwitchSetList     _values;
        SwitchSetNameList _valueNames;
};

}

#endif

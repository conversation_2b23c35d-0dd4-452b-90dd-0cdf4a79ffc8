/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2014 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSGUI_LABEL
#define OSGUI_LABEL

#include <osgUI/Widget>
#include <osgText/Text>

namespace osgUI
{

class OSGUI_EXPORT Label : public osgUI::Widget
{
public:
    Label();
    Label(const Label& label, const osg::CopyOp& copyop=osg::CopyOp::SHALLOW_COPY);
    META_Node(osgUI, Label);

    void setText(const std::string& text) { _text = text; dirty(); }
    std::string& getText() { return _text; }
    const std::string& getText() const { return _text; }

    virtual void createGraphicsImplementation();

protected:
    virtual ~Label() {}

    std::string                         _text;

    // implementation detail
    osg::ref_ptr<osgText::Text>         _textDrawable;
};

}

#endif

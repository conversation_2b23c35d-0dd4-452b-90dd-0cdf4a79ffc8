/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/
//osgParticle - Copyright (C) 2002 <PERSON>

#ifndef OSGPARTICLE_PROGRAM
#define OSGPARTICLE_PROGRAM 1

#include <osgParticle/Export>
#include <osgParticle/ParticleProcessor>

#include <osg/Object>
#include <osg/Node>
#include <osg/NodeVisitor>
#include <osg/CopyOp>

namespace osgParticle
{

    /**    An abstract <CODE>ParticleProcessor</CODE> descendant for modifying particles "on the fly"
        during the cull traversal.
        Descendants of this class must implement the <CODE>execute()</CODE> method, which should iterate
        through all particles in the linked particle system and modify them somehow
        (usually updating their velocity vector).
    */
    class OSGPARTICLE_EXPORT Program: public ParticleProcessor
    {
    public:
        Program();
        Program(const Program& copy, const osg::CopyOp& copyop = osg::CopyOp::SHALLOW_COPY);

        virtual const char* libraryName() const { return "osgParticle"; }
        virtual const char* className() const { return "Program"; }
        virtual bool isSameKindAs(const osg::Object* obj) const { return dynamic_cast<const Program*>(obj) != 0; }
        virtual void accept(osg::NodeVisitor& nv) { if (nv.validNodeMask(*this)) { nv.pushOntoNodePath(this); nv.apply(*this); nv.popFromNodePath(); } }

    protected:
        virtual ~Program() {}
        Program& operator=(const Program&) { return *this; }

        /// Implementation of <CODE>ParticleProcessor::process()</CODE>. Do not call this method by yourself.
        inline void process(double dt);

        /// Execute the program on the particle system. Must be overridden in descendant classes.
        virtual void execute(double dt) = 0;

    private:
    };

    // INLINE FUNCTIONS

    inline void Program::process(double dt)
    {
        execute(dt);
    }

}

#endif

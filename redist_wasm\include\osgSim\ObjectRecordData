/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2004 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSGSIM_OBJECTRECORDDATA
#define OSGSIM_OBJECTRECORDDATA 1

#include <osg/Object>

#include <ostream>

namespace osgSim {

/** When the OpenFlight importer encounters an Object record, it stores
    the data in one of these classes, and attaches the instance of the
    class as UserData to the corresponding osgLLGroup node.
*/

class ObjectRecordData : public osg::Object
{
    public:

        ObjectRecordData()
          : _flags( 0 ),
            _relativePriority( 0 ),
            _transparency( 0 ),
            _effectID1( 0 ),
            _effectID2( 0 ),

            _significance( 0 )
        {}

        ObjectRecordData( const ObjectRecordData& copy, const osg::CopyOp& copyop = osg::CopyOp::SHALLOW_COPY ):
            osg::Object(copy, copyop)
        {
            _flags = copy._flags;
            _relativePriority = copy._relativePriority;
            _transparency = copy._transparency;
            _effectID1 = copy._effectID1;
            _effectID2 = copy._effectID2;
            _significance = copy._significance;
        }

        META_Object( osgSim, ObjectRecordData );

        static const unsigned int DONT_DISPLAY_IN_DAYLIGHT = 0x80000000u >> 0;
        static const unsigned int DONT_DISPLAY_AT_DUSK     = 0x80000000u >> 1;
        static const unsigned int DONT_DISPLAY_AT_NIGHT    = 0x80000000u >> 2;
        static const unsigned int DONT_ILLUMINATE          = 0x80000000u >> 3;
        static const unsigned int FLAT_SHADED              = 0x80000000u >> 4;
        static const unsigned int GROUPS_SHADOW_OBJECT     = 0x80000000u >> 5;

        unsigned int _flags;
        short _relativePriority;
        unsigned short _transparency; // 0=opaque, 65535=totally clear
        short _effectID1;
        short _effectID2;
        short _significance;

};  // end of class ObjectRecordData

}   // end of namespace osgSim

#endif

/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSGTERRAIN_LAYER
#define OSGTERRAIN_LAYER 1

#include <osg/Image>
#include <osg/Shape>
#include <osg/Array>
#include <osg/TransferFunction>

#include <osgTerrain/Locator>
#include <osgTerrain/ValidDataOperator>

namespace osgTerrain {

#define MAXIMUM_NUMBER_OF_LEVELS 30

/** Extact the setname and filename from a compound string in the from set:setname:filename".
  * Returns a setname of "" when non set:setname: entry is present.*/
extern OSGTERRAIN_EXPORT void extractSetNameAndFileName(const std::string& compoundstring, std::string& setname, std::string& filename);

/** Create a compound string in the form set:setname:filename, or just filename if setname is "".*/
extern OSGTERRAIN_EXPORT std::string createCompoundSetNameAndFileName(const std::string& setname, const std::string& filename);

class OSGTERRAIN_EXPORT Layer : public osg::Object
{
    public:

        Layer();

        /** Copy constructor using CopyOp to manage deep vs shallow copy.*/
        Layer(const Layer&,const osg::CopyOp& copyop=osg::CopyOp::SHALLOW_COPY);

        META_Object(osgTerrain, Layer);

        /** Set the name of this layer. */
        void setSetName(const std::string& setname) { setName(setname); }

        /** Get the name of this layer. */
        const std::string& getSetName() const { return getName(); }

        /** Set the file name of the data associated with this layer. */
        virtual void setFileName(const std::string& filename) { _filename = filename; }

        /** Get the file name of the layer. */
        virtual const std::string& getFileName() const { return _filename; }

        /** Return the compound name of the layer in the form set::name::filename string.*/
        std::string getCompoundName() const { return createCompoundSetNameAndFileName(getName(), getFileName()); }

        void setLocator(Locator* locator) { _locator = locator; }

        template<class T> void setLocator(const osg::ref_ptr<T>& locator) { setLocator(locator.get()); }

        Locator* getLocator() { return _locator.get(); }
        const Locator* getLocator() const { return _locator.get(); }

        void setMinLevel(unsigned int minLevel) { _minLevel = minLevel; }
        unsigned int getMinLevel() const { return _minLevel; }

        void setMaxLevel(unsigned int maxLevel) { _maxLevel = maxLevel; }
        unsigned int getMaxLevel() const { return _maxLevel; }

        /** Set the data validation operator. */
        void setValidDataOperator(ValidDataOperator* validDataOp) { _validDataOperator = validDataOp; }

        /** Get the data validation operator. */
        ValidDataOperator* getValidDataOperator() { return _validDataOperator.get(); }

        /** Get the const data validation operator. */
        const ValidDataOperator* getValidDataOperator() const { return _validDataOperator.get(); }


        /** Get the number of columns. */
        virtual unsigned int getNumColumns() const { return 0; }

        /** Get the number of rows. */
        virtual unsigned int getNumRows() const { return 0; }

        void setDefaultValue(const osg::Vec4& value) { _defaultValue = value; }
        const osg::Vec4& getDefaultValue() const { return _defaultValue; }


        /** Set the minification texture filter to use when a texture is associated with this layer.*/
        void setMinFilter(osg::Texture::FilterMode filter) { _minFilter = filter; }

        /** Get the minification texture filter to use when a texture is associated with this layer.*/
        osg::Texture::FilterMode getMinFilter() const { return _minFilter; }


        /** Set the magnification texture filter to use when a texture is associated with this layer.*/
        void setMagFilter(osg::Texture::FilterMode filter) { _magFilter = filter; }

        /** Get the magnification texture filter to use when a texture is associated with this layer.*/
        osg::Texture::FilterMode getMagFilter() const { return _magFilter; }



        /** Return image associated with layer if supported. */
        virtual osg::Image* getImage() { return 0; }

        /** Return const image associated with layer if supported. */
        virtual const osg::Image* getImage() const { return 0; }


        virtual bool transform(float /*offset*/, float /*scale*/) { return false; }

        /**
         * Get the layer value at position i,j.
         * param i X-axis (or column) index.
         * param j Y-axis (or row) index.
         * param value Returned layer value.
         * @return true if value is valid, else false
         */
        virtual bool getValue(unsigned int /*i*/, unsigned int /*j*/, float& /*value*/) const { return false; }
        virtual bool getValue(unsigned int /*i*/, unsigned int /*j*/, osg::Vec2& /*value*/) const { return false; }
        virtual bool getValue(unsigned int /*i*/, unsigned int /*j*/, osg::Vec3& /*value*/) const { return false; }
        virtual bool getValue(unsigned int /*i*/, unsigned int /*j*/, osg::Vec4& /*value*/) const { return false; }

        inline bool getValidValue(unsigned int i, unsigned int j, float& value) const
        {
            if (getValue(i,j,value)) return _validDataOperator.valid() ? (*_validDataOperator)(value) : true;
            return false;
        }

        inline bool getValidValue(unsigned int i, unsigned int j, osg::Vec2& value) const
        {
            if (getValue(i,j,value)) return _validDataOperator.valid() ? (*_validDataOperator)(value) : true;
            return false;
        }

        inline bool getValidValue(unsigned int i, unsigned int j, osg::Vec3& value) const
        {
            if (getValue(i,j,value)) return _validDataOperator.valid() ? (*_validDataOperator)(value) : true;
            return false;
        }

        inline bool getValidValue(unsigned int i, unsigned int j, osg::Vec4& value) const
        {
            if (getValue(i,j,value)) return _validDataOperator.valid() ? (*_validDataOperator)(value) : true;
            return false;
        }


        /**
         * Compute column,row indices from normalized coordinates.
         * @param[in] ndc_x Normalized X-axis coordinate.
         * @param[in] ndc_y Normalized Y-axis coordinate.
         * @param[out] i Returned X-axis (or column) index.
         * @param[out] j Returned Y-axis (or row) index.
         * @param[out] ir Returned X-axis fraction.
         * @param[out] jr Returned Y-axis fraction.
         */
        inline void computeIndices(double ndc_x, double ndc_y, unsigned int& i, unsigned int& j, double& ir, double& jr) const
        {
            ndc_x *= double(getNumColumns()-1);
            ndc_y *= double(getNumRows()-1);
            i = (unsigned int)(ndc_x);
            j = (unsigned int)(ndc_y);
            ir = ndc_x - double(i);
            jr = ndc_y - double(j);
        }

        /**
         * Calculate the interpolated layer value at the given normalized coordinates.
         * @param[in] ndc_x Normalized X-axis coordinate.
         * @param[in] ndc_y Normalized Y-axis coordinate.
         * @param[out] value Returned layer value.
         * @return true if value is valid, else false
         */
        inline bool getInterpolatedValue(double ndc_x, double ndc_y, float& value) const
        {
            unsigned int i,j;
            double ir, jr;
            computeIndices(ndc_x, ndc_y, i, j, ir, jr);
            value = 0.0f;
            double div = 0.0f;
            float v,r;

            r = (1.0f-ir)*(1.0f-jr);
            if (r>0.0 && getValue(i,j,v))
            {
                value += v*r;
                div += r;
            }

            r = (ir)*(1.0f-jr);
            if (r>0.0 && getValue(i+1,j,v))
            {
                value += v*r;
                div += r;
            }

            r = (ir)*(jr);
            if (r>0.0 && getValue(i+1,j+1,v))
            {
                value += v*r;
                div += r;
            }

            r = (1.0f-ir)*(jr);
            if (r>0.0 && getValue(i,j+1,v))
            {
                value += v*r;
                div += r;
            }

            if (div != 0.0)
            {
                value /= div;
                return true;
            }

            value = 0.0;
            return false;
        }

        inline bool getInterpolatedValidValue(double ndc_x, double ndc_y, float& value) const
        {
            unsigned int i,j;
            double ir, jr;
            computeIndices(ndc_x, ndc_y, i, j, ir, jr);
            value = 0.0f;
            double div = 0.0f;
            float v,r;

            r = (1.0f-ir)*(1.0f-jr);
            if (r>0.0 && getValidValue(i,j,v))
            {
                value += v*r;
                div += r;
            }

            r = (ir)*(1.0f-jr);
            if (r>0.0 && getValidValue(i+1,j,v))
            {
                value += v*r;
                div += r;
            }

            r = (ir)*(jr);
            if (r>0.0 && getValidValue(i+1,j+1,v))
            {
                value += v*r;
                div += r;
            }

            r = (1.0f-ir)*(jr);
            if (r>0.0 && getValidValue(i,j+1,v))
            {
                value += v*r;
                div += r;
            }

            if (div != 0.0)
            {
                value /= div;
                return true;
            }

            value = 0.0;
            return false;
        }

        /** increment the modified count."*/
        virtual void dirty() {}

        /** Set the modified count value.  */
        virtual void setModifiedCount(unsigned int /*value*/) {}

        /** Get modified count value. */
        virtual unsigned int getModifiedCount() const { return 0; }

        virtual osg::BoundingSphere computeBound(bool treatAsElevationLayer) const;

    protected:

        virtual ~Layer();

        std::string                     _filename;
        osg::ref_ptr<Locator>           _locator;
        unsigned int                    _minLevel;
        unsigned int                    _maxLevel;
        osg::ref_ptr<ValidDataOperator> _validDataOperator;
        osg::Vec4                       _defaultValue;
        osg::Texture::FilterMode        _minFilter;
        osg::Texture::FilterMode        _magFilter;

};

class OSGTERRAIN_EXPORT ImageLayer : public Layer
{
    public:

        ImageLayer(osg::Image* image=0);

        /** Copy constructor using CopyOp to manage deep vs shallow copy.*/
        ImageLayer(const ImageLayer& imageLayer,const osg::CopyOp& copyop=osg::CopyOp::SHALLOW_COPY);

        META_Object(osgTerrain, ImageLayer);

        void setFileName(const std::string& filename) { _filename = filename; if (_image.valid()) _image->setFileName(filename); }
        virtual const std::string& getFileName() const { return _image.get() ? _image->getFileName() : _filename; }

        virtual bool transform(float offset, float scale);

        void setImage(osg::Image* image);

        template<class T> void setImage(const osg::ref_ptr<T>& image) { return setImage(image.get()); }

        /** Return image associated with layer. */
        virtual osg::Image* getImage() { return _image.get(); }

        /** Return const image associated with layer. */
        virtual const osg::Image* getImage() const { return _image.get(); }

        virtual unsigned int getNumColumns() const { return _image.valid() ? _image->s() : 0; }
        virtual unsigned int getNumRows() const { return _image.valid() ? _image->t() : 0;  }

        virtual bool getValue(unsigned int i, unsigned int j, float& value) const;
        virtual bool getValue(unsigned int i, unsigned int j, osg::Vec2& value) const;
        virtual bool getValue(unsigned int i, unsigned int j, osg::Vec3& value) const;
        virtual bool getValue(unsigned int i, unsigned int j, osg::Vec4& value) const;

        virtual void dirty();
        virtual void setModifiedCount(unsigned int value);
        virtual unsigned int getModifiedCount() const;

    protected:

        virtual ~ImageLayer() {}

        osg::ref_ptr<osg::Image>    _image;

};

class OSGTERRAIN_EXPORT ContourLayer : public Layer
{
    public:

        ContourLayer(osg::TransferFunction1D* tf=0);

        /** Copy constructor using CopyOp to manage deep vs shallow copy.*/
        ContourLayer(const ContourLayer& tfLayer,const osg::CopyOp& copyop=osg::CopyOp::SHALLOW_COPY);

        META_Object(osgTerrain, ContourLayer);

        virtual bool transform(float offset, float scale);

        void setTransferFunction(osg::TransferFunction1D* tf);

        template<class T> void setTransferFunction(const osg::ref_ptr<T>& tf) { return setTransferFunction(tf.get()); }

        osg::TransferFunction1D* getTransferFunction() { return _tf.get(); }
        const osg::TransferFunction1D* getTransferFunction() const { return _tf.get(); }

        /** Return image associated with layer. */
        virtual osg::Image* getImage() { return _tf.valid() ? _tf->getImage() : 0; }

        /** Return const image associated with layer. */
        virtual const osg::Image* getImage() const { return _tf.valid() ? _tf->getImage() : 0; }


        virtual unsigned int getNumColumns() const { return _tf.valid() ? _tf->getNumberImageCells() : 0; }
        virtual unsigned int getNumRows() const { return _tf.valid() ? 1 : 0;  }

        virtual bool getValue(unsigned int i, unsigned int j, float& value) const;
        virtual bool getValue(unsigned int i, unsigned int j, osg::Vec2& value) const;
        virtual bool getValue(unsigned int i, unsigned int j, osg::Vec3& value) const;
        virtual bool getValue(unsigned int i, unsigned int j, osg::Vec4& value) const;

        virtual void dirty();
        virtual void setModifiedCount(unsigned int value);
        virtual unsigned int getModifiedCount() const;

    protected:

        virtual ~ContourLayer() {}

        osg::ref_ptr<osg::TransferFunction1D>    _tf;

};

class OSGTERRAIN_EXPORT HeightFieldLayer : public Layer
{
    public:

        HeightFieldLayer(osg::HeightField* hf=0);

        /** Copy constructor using CopyOp to manage deep vs shallow copy.*/
        HeightFieldLayer(const HeightFieldLayer& hfLayer,const osg::CopyOp& copyop=osg::CopyOp::SHALLOW_COPY);

        META_Object(osgTerrain, HeightFieldLayer);

        void setFileName(const std::string& filename) { _filename = filename; }
        virtual const std::string& getFileName() const { return _filename; }

        virtual bool transform(float offset, float scale);

        void setHeightField(osg::HeightField* hf);

        template<class T> void setHeightField(const osg::ref_ptr<T>& hf) { return setHeightField(hf.get()); }

        osg::HeightField* getHeightField() { return _heightField.get(); }
        const osg::HeightField* getHeightField() const { return _heightField.get(); }

        virtual unsigned int getNumColumns() const { return _heightField.valid() ? _heightField->getNumColumns() : 0; }
        virtual unsigned int getNumRows() const { return _heightField.valid() ? _heightField->getNumRows() : 0;  }

        virtual bool getValue(unsigned int i, unsigned int j, float& value) const;
        virtual bool getValue(unsigned int i, unsigned int j, osg::Vec2& value) const;
        virtual bool getValue(unsigned int i, unsigned int j, osg::Vec3& value) const;
        virtual bool getValue(unsigned int i, unsigned int j, osg::Vec4& value) const;

        virtual void dirty();
        virtual void setModifiedCount(unsigned int value);
        virtual unsigned int getModifiedCount() const;

    protected:

        virtual ~HeightFieldLayer() {}

        unsigned int                    _modifiedCount;
        osg::ref_ptr<osg::HeightField>  _heightField;

};


class OSGTERRAIN_EXPORT ProxyLayer : public Layer
{
    public:

        ProxyLayer();

        /** Copy constructor using CopyOp to manage deep vs shallow copy.*/
        ProxyLayer(const ProxyLayer& proxyLayer,const osg::CopyOp& copyop=osg::CopyOp::SHALLOW_COPY);

        META_Object(osgTerrain, ProxyLayer);

        /** Return image associated with layer if supported. */
        virtual osg::Image* getImage()
        {
            return _implementation.valid() ? _implementation->getImage() : 0;
        }

        /** Return const image associated with layer if supported. */
        virtual const osg::Image* getImage() const
        {
            return _implementation.valid() ? _implementation->getImage() : 0;
        }

        /** Set the implementation layer that does the actual work.*/
        void setImplementation(Layer* layer) { _implementation = layer; }

        /** Get the implementation layer that does the actual work.*/
        Layer* getImplementation() { return _implementation.get(); }

        /** Get the const implementation layer that does the actual work.*/
        const Layer* getImplementation() const { return _implementation.get(); }

        virtual void setFileName(const std::string& filename);
        virtual const std::string& getFileName() const { return _filename; }

        virtual unsigned int getNumColumns() const;
        virtual unsigned int getNumRows() const;

        virtual bool transform(float offset, float scale);

        virtual bool getValue(unsigned int i, unsigned int j, float& value) const;
        virtual bool getValue(unsigned int i, unsigned int j, osg::Vec2& value) const;
        virtual bool getValue(unsigned int i, unsigned int j, osg::Vec3& value) const;
        virtual bool getValue(unsigned int i, unsigned int j, osg::Vec4& value) const;

        virtual void dirty();
        virtual void setModifiedCount(unsigned int value);
        virtual unsigned int getModifiedCount() const;

        virtual osg::BoundingSphere computeBound(bool treatAsElevationLayer) const;

    protected:

        virtual ~ProxyLayer();

        osg::ref_ptr<Layer> _implementation;


};

class OSGTERRAIN_EXPORT CompositeLayer : public Layer
{
    public:

        CompositeLayer();

        /** Copy constructor using CopyOp to manage deep vs shallow copy.*/
        CompositeLayer(const CompositeLayer& compositeLayer,const osg::CopyOp& copyop=osg::CopyOp::SHALLOW_COPY);

        META_Object(osgTerrain, CompositeLayer);

        void clear();

        /** Set the set name of layer 'i'. */
        void setSetName(unsigned int i, const std::string& setname) { _layers[i].setname = setname; if (_layers[i].layer.valid()) _layers[i].layer->setName(setname); }

        /** Get the set name of layer 'i'. */
        const std::string& getSetName(unsigned int i) const { return _layers[i].layer.valid() ? _layers[i].layer->getName() : _layers[i].setname; }

        /** Set the file name of the data associated with layer 'i'. */
        void setFileName(unsigned int i, const std::string& filename) { _layers[i].filename = filename; if (_layers[i].layer.valid()) _layers[i].layer->setFileName(filename); }

        /** Get the file name of the data associated with layer 'i'. */
        const std::string& getFileName(unsigned int i) const { return _layers[i].layer.valid() ? _layers[i].layer->getFileName() : _layers[i].filename; }

        void setCompoundName(unsigned int i, const std::string& compoundname);
        std::string getCompoundName(unsigned int i) const;


        void setLayer(unsigned int i, Layer* layer) { if (i>=_layers.size()) _layers.resize(i+1); _layers[i].layer = layer; }

        template<class T> void setLayer(unsigned int i, const osg::ref_ptr<T>& layer) { setLayer(i, layer.get()); }

        Layer* getLayer(unsigned int i) { return i<_layers.size() ? _layers[i].layer.get() : 0; }

        const Layer* getLayer(unsigned int i) const { return i<_layers.size() ? _layers[i].layer.get() : 0; }

        void addLayer(const std::string& compoundname);

        void addLayer(const std::string& setname, const std::string& filename);

        void addLayer(Layer* layer) { _layers.push_back(CompoundNameLayer(layer->getName(),layer->getFileName(),layer)); }

        template<class T> void addLayer(const osg::ref_ptr<T>& layer) { return addLayer(layer.get()); }

        void removeLayer(unsigned int i) { _layers.erase(_layers.begin()+i); }

        unsigned int getNumLayers() const { return static_cast<unsigned int>(_layers.size()); }

    protected:

        virtual ~CompositeLayer() {}

        struct CompoundNameLayer
        {
            CompoundNameLayer() {}

            CompoundNameLayer(const CompoundNameLayer& cnl):
                setname(cnl.setname),
                filename(cnl.filename),
                layer(cnl.layer) {}

            CompoundNameLayer(const std::string& sn, const std::string& fn, Layer* l):
                setname(sn),
                filename(fn),
                layer(l) {}

            CompoundNameLayer& operator = (const CompoundNameLayer& cnl)
            {
                if (&cnl==this) return *this;

                setname = cnl.setname;
                filename = cnl.filename;
                layer = cnl.layer;
                return *this;
            }

            std::string         setname;
            std::string         filename;
            osg::ref_ptr<Layer> layer;
        };

        typedef std::vector< CompoundNameLayer > Layers;

        Layers _layers;
};


class OSGTERRAIN_EXPORT SwitchLayer : public CompositeLayer
{
    public:

        SwitchLayer();

        /** Copy constructor using CopyOp to manage deep vs shallow copy.*/
        SwitchLayer(const SwitchLayer& switchLayer,const osg::CopyOp& copyop=osg::CopyOp::SHALLOW_COPY);

        META_Object(osgTerrain, SwitchLayer);

        void setActiveLayer(int i) { _activeLayer = i; }
        int getActiveLayer() const { return _activeLayer; }

        /** Return image associated with layer if supported. */
        virtual osg::Image* getImage()
        {
            if (_activeLayer < 0) return 0;
            if (_activeLayer >= static_cast<int>(getNumLayers())) return 0;
            return _layers[_activeLayer].layer->getImage();
        }

        /** Return const image associated with layer if supported. */
        virtual const osg::Image* getImage() const
        {
            if (_activeLayer < 0) return 0;
            if (_activeLayer >= static_cast<int>(getNumLayers())) return 0;
            return _layers[_activeLayer].layer->getImage();
        }

    protected:

        virtual ~SwitchLayer() {}

        int _activeLayer;
};



}

#endif

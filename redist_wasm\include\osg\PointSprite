/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
 */

#ifndef OSG_POINTSPRITE
#define OSG_POINTSPRITE 1

#include <osg/GL>
#include <osg/StateAttribute>

#ifndef GL_ARB_point_sprite
#define GL_POINT_SPRITE_ARB               0x8861
#define GL_COORD_REPLACE_ARB              0x8862
#endif

#ifndef GL_POINT_SPRITE_COORD_ORIGIN
#define GL_POINT_SPRITE_COORD_ORIGIN      0x8CA0
#define GL_LOWER_LEFT                     0x8CA1
#define GL_UPPER_LEFT                     0x8CA2
#endif

namespace osg {

/** PointSprite base class which encapsulates enabling of point sprites .*/
class OSG_EXPORT PointSprite : public osg::StateAttribute {
public:

        PointSprite();

        /** Copy constructor using CopyOp to manage deep vs shallow copy.*/
        PointSprite(const PointSprite& ps,const osg::CopyOp& copyop=osg::CopyOp::SHALLOW_COPY):
            StateAttribute(ps,copyop),
            _coordOriginMode(ps._coordOriginMode) {}


        META_StateAttribute(osg, PointSprite, POINTSPRITE);

        /** return -1 if *this < *rhs, 0 if *this==*rhs, 1 if *this>*rhs.*/
        virtual int compare(const StateAttribute& sa) const;

        virtual bool getModeUsage(StateAttribute::ModeUsage& usage) const
        {
            usage.usesMode(GL_POINT_SPRITE_ARB);
            return true;
        }

        virtual bool checkValidityOfAssociatedModes(osg::State&) const;

        virtual bool isTextureAttribute() const { return true; }

        virtual void apply(osg::State& state) const;

        enum CoordOriginMode {
            UPPER_LEFT = GL_UPPER_LEFT,
            LOWER_LEFT = GL_LOWER_LEFT
        };

        inline void setCoordOriginMode(CoordOriginMode mode) { _coordOriginMode = mode; }
        inline CoordOriginMode getCoordOriginMode() const { return _coordOriginMode; }

protected:
        virtual ~PointSprite();

        CoordOriginMode _coordOriginMode;
};

}

#endif

/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/
//osgManipulator - Copyright (C) 2007 Fugro-Jason B.V.

#ifndef OSGMANIPULATOR_SCALEAXISDRAGGER
#define OSGMANIPULATOR_SCALEAXISDRAGGER 1

#include <osgManipulator/Scale1DDragger>
#include <osg/ShapeDrawable>
#include <osg/Geode>
#include <osg/LineWidth>

namespace osgManipulator {

/**
 * <PERSON><PERSON> for performing scaling on all 3 axes.
 */
class OSGMANIPULATOR_EXPORT ScaleAxisDragger : public CompositeDragger
{
    public:

        ScaleAxisDragger();

        META_OSGMANIPULATOR_Object(osgManipulator,ScaleAxisDragger)

        /** Setup default geometry for dragger. */
        void setupDefaultGeometry();

        /** Sets the width of the axis lines in pixels. */
        void setAxisLineWidth(float linePixelWidth);

        /** Retrieves the width of the axis lines in pixels. */
        float getAxisLineWidth() const { return _axisLineWidth; }

        /** Sets the size of the boxes. */
        void setBoxSize(float size);

        /** Retrieves the size of the boxes. */
        float getBoxSize() const { return _boxSize; }

    protected:

        virtual ~ScaleAxisDragger();

        osg::ref_ptr< Scale1DDragger >        _xDragger;
        osg::ref_ptr< Scale1DDragger >        _yDragger;
        osg::ref_ptr< Scale1DDragger >        _zDragger;

        float _boxSize;
        float _axisLineWidth;

        osg::ref_ptr<osg::Geode> _lineGeode;
        osg::ref_ptr<osg::LineWidth> _lineWidth;
        osg::ref_ptr<osg::Box> _box;
};


}

#endif

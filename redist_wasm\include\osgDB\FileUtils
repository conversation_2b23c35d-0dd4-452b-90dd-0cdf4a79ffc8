/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSGDB_FILEUTILS
#define OSGDB_FILEUTILS 1

#include <osgDB/Registry>

#include <vector>
#include <deque>
#include <string>
#include <stdio.h>

namespace osgDB {

/** Overload of the standard fopen function. If OSG_USE_UTF8_FILENAME is defined,
  * filename will be expanded from UTF8 to UTF16 and _wfopen will be called. */
extern OSGDB_EXPORT FILE* fopen(const char* filename, const char* mode);

/** Make a new directory.  Returns true if directory exists or was created. */
extern OSGDB_EXPORT bool makeDirectory( const std::string &directoryPath );

/** Make a new directory for a given file. */
extern OSGDB_EXPORT bool makeDirectoryForFile( const std::string &filePath );

/** Get current working directory. */
extern OSGDB_EXPORT std::string getCurrentWorkingDirectory( void );

/** Set current working directory. */
extern OSGDB_EXPORT bool setCurrentWorkingDirectory( const std::string &newCurrentWorkingDirectory );


/** return true if a file exists. */
extern OSGDB_EXPORT bool fileExists(const std::string& filename);

enum FileType
{
    FILE_NOT_FOUND,
    REGULAR_FILE,
    DIRECTORY
};

/** return type of file. */
extern OSGDB_EXPORT FileType fileType(const std::string& filename);

/** find specified file in specified file path.*/
extern OSGDB_EXPORT std::string findFileInPath(const std::string& filename, const FilePathList& filePath,CaseSensitivity caseSensitivity=CASE_SENSITIVE);

/** return the directory/filename of a file if its is contained within specified directory.
  * return "" if directory does not contain file.  If caseInsensitive is set to true then
  * a case insensitive comparison is used to compare fileName to directory contents.
  * This is useful when unix programs attempt read case insensitive windows filenames.
  */
extern OSGDB_EXPORT std::string findFileInDirectory(const std::string& fileName,const std::string& dirName,CaseSensitivity caseSensitivity=CASE_SENSITIVE);

/** simple list of names to represent a directory's contents. */
typedef std::vector<std::string> DirectoryContents;

/** Return the contents of a directory.
  * Return value will contain filenames only, not absolute paths.
  * Returns an empty array on any error.*/
extern OSGDB_EXPORT DirectoryContents getDirectoryContents(const std::string& dirName);

/** Return the contents of a directory, sorting the names into alphabetic and numberical order.
  * Return value will contain filenames only, not absolute paths.
  * Returns an empty array on any error.*/
extern OSGDB_EXPORT DirectoryContents getSortedDirectoryContents(const std::string& dirName);

/** Return the list of filenames that match the given filename with wildcards.
  * Will only expand '*', and will not expand wildcards in directory, only in
  * filename part of the given filename.
  * Return value will contain path+filename so that if ever the above
  * limitation (expanding wildcards in directory) is fixed, client code will
  * still work unchanged. */
extern OSGDB_EXPORT DirectoryContents expandWildcardsInFilename(const std::string& filename);

enum CopyFileResult
{
    COPY_FILE_OK,                          /**< Operation done. */
    COPY_FILE_SOURCE_EQUALS_DESTINATION,   /**< Operation is useless (source == destination). */
    COPY_FILE_BAD_ARGUMENT,
    COPY_FILE_SOURCE_MISSING,              /**< Source file doesn't exist. */
    COPY_FILE_SOURCE_NOT_OPENED,           /**< Error opening source file. */
    COPY_FILE_DESTINATION_NOT_OPENED,      /**< Error opening destination file. */
    COPY_FILE_READ_ERROR,
    COPY_FILE_WRITE_ERROR
};

/** Copy a file to another location, overwriting if necessary.
  * You must provide full path for both source and destination.
  * \return true on success, or if source and destination are the same.
*/
extern OSGDB_EXPORT CopyFileResult copyFile(const std::string & source, const std::string & destination);



inline void setDataFilePathList(const FilePathList& filepath) { osgDB::Registry::instance()->setDataFilePathList(filepath); }

inline void setDataFilePathList(const std::string& paths) { osgDB::Registry::instance()->setDataFilePathList(paths); }

inline FilePathList& getDataFilePathList() { return osgDB::Registry::instance()->getDataFilePathList(); }

/** Search for specified file in file system, checking the DataFilePathList for possible paths,
  * returning the full path of the first valid file found, return an empty string if no string is found.
  */
extern OSGDB_EXPORT std::string findDataFile(const std::string& filename,CaseSensitivity caseSensitivity=CASE_SENSITIVE);

/** Search for specified file in file system, checking first the database path set in the Options structure, then the DataFilePathList for possible paths,
  * returning the full path of the first valid file found, return an empty string if no string is found.
  */
extern OSGDB_EXPORT std::string findDataFile(const std::string& filename,const Options* options, CaseSensitivity caseSensitivity=CASE_SENSITIVE);

inline void setLibraryFilePathList(const FilePathList& filepaths) { osgDB::Registry::instance()->setLibraryFilePathList(filepaths); }

inline void setLibraryFilePathList(const std::string& paths) { osgDB::Registry::instance()->setLibraryFilePathList(paths); }

inline FilePathList& getLibraryFilePathList() { return osgDB::Registry::instance()->getLibraryFilePathList(); }

extern OSGDB_EXPORT std::string findLibraryFile(const std::string& filename,CaseSensitivity caseSensitivity=CASE_SENSITIVE);

/** convert a string containing a list of paths delimited either with ';' (Windows) or ':' (All other platforms) into FilePath representation.*/
extern OSGDB_EXPORT void convertStringPathIntoFilePathList(const std::string& paths,FilePathList& filepath);

/** Return true if FilePathList contains a filepath that is significies checking of the current working directory.*/
extern OSGDB_EXPORT bool containsCurrentWorkingDirectoryReference(const FilePathList& paths);

extern OSGDB_EXPORT void appendPlatformSpecificLibraryFilePaths(FilePathList& filepath);
extern OSGDB_EXPORT void appendPlatformSpecificResourceFilePaths(FilePathList& filepath);

}        // namespace osgDB

#endif

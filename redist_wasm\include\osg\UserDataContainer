/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSG_USERDATACONTAINER
#define OSG_USERDATACONTAINER 1

#include <osg/Object>

#include <string>
#include <vector>

namespace osg {

/** Internal structure for storing all user data.*/
class OSG_EXPORT UserDataContainer : public osg::Object
{
    public:
        UserDataContainer();
        UserDataContainer(const UserDataContainer& udc, const osg::CopyOp& copyop=osg::CopyOp::SHALLOW_COPY);

        virtual bool isSameKindAs(const Object* obj) const { return dynamic_cast<const UserDataContainer*>(obj)!=0; }

        /** return the name of the object's library. Must be defined
            by derived classes. The OpenSceneGraph convention is that the
            namespace of a library is the same as the library name.*/
        virtual const char* libraryName() const { return "osg"; }

        /** return the name of the object's class type. Must be defined
            by derived classes.*/
        virtual const char* className() const  { return "UserDataContainer"; }

        /** Convert 'this' into a UserDataContainer pointer if Object is a UserDataContainer, otherwise return 0.
          * Equivalent to dynamic_cast<UserDataContainer*>(this).*/
        virtual UserDataContainer* asUserDataContainer() { return this; }

        /** convert 'const this' into a const UserDataContainer pointer if Object is a UserDataContainer, otherwise return 0.
          * Equivalent to dynamic_cast<const UserDataContainer*>(this).*/
        virtual const UserDataContainer* asUserDataContainer() const { return this; }

        /**
         * Set user data, data must be subclassed from Referenced to allow
         * automatic memory handling.  If your own data isn't directly
         * subclassed from Referenced then create an adapter object
         * which points to your own object and handles the memory addressing.
         */
        virtual void setUserData(Referenced* obj) = 0;

        using osg::Object::setUserData;

        /** Get user data.*/
        virtual Referenced* getUserData() = 0;

        /** Get const user data.*/
        virtual const Referenced* getUserData() const  = 0;

        /** Add user data object. Returns the index position of object added. */
        virtual unsigned int addUserObject(Object* obj)  = 0;

        template<class T> unsigned int addUserObject(const osg::ref_ptr<T>& obj) { return addUserObject(obj.get()); }

         /** Add element to list of user data objects.*/
        virtual void setUserObject(unsigned int i, Object* obj)  = 0;

        /** Remove element from the list of user data objects.*/
        virtual void removeUserObject(unsigned int i)  = 0;


        /** Get user data object as specified index position. */
        virtual Object* getUserObject(unsigned int i)  = 0;

        /** Get const user data object as specified index position. */
        virtual const Object* getUserObject(unsigned int i) const  = 0;

        /** Get number of user objects assigned to this object.*/
        virtual unsigned int getNumUserObjects() const  = 0;

        /** Get the index position of specified user data object.*/
        virtual unsigned int getUserObjectIndex(const osg::Object* obj, unsigned int startPos=0) const = 0;

        /** Get the index position of first user data object that matches specified name.*/
        virtual unsigned int getUserObjectIndex(const std::string& name, unsigned int startPos=0) const = 0;


        /** Get first user data object with specified name. */
        virtual Object* getUserObject(const std::string& name, unsigned int startPos=0);

        /** Get first const user data object with specified name. */
        virtual const Object* getUserObject(const std::string& name, unsigned int startPos=0) const;


        typedef std::vector<std::string> DescriptionList;

        /** Set the list of string descriptions.*/
        virtual void setDescriptions(const DescriptionList& descriptions) = 0;

        /** Get the description list.*/
        virtual DescriptionList& getDescriptions() = 0;

        /** Get the const description list.*/
        virtual const DescriptionList& getDescriptions() const = 0;

        /** Get number of description strings.*/
        virtual unsigned int getNumDescriptions() const = 0;

        /** Add a description string.*/
        virtual void addDescription(const std::string& desc) = 0;

    protected:
        virtual ~UserDataContainer() {}
};

/** Internal structure for storing all user data.*/
class OSG_EXPORT DefaultUserDataContainer : public osg::UserDataContainer
{
    public:
        DefaultUserDataContainer();
        DefaultUserDataContainer(const DefaultUserDataContainer& udc, const osg::CopyOp& copyop=osg::CopyOp::SHALLOW_COPY);

        META_Object(osg, DefaultUserDataContainer)


        virtual void setThreadSafeRefUnref(bool threadSafe);

        /**
         * Set user data, data must be subclassed from Referenced to allow
         * automatic memory handling.  If your own data isn't directly
         * subclassed from Referenced then create an adapter object
         * which points to your own object and handles the memory addressing.
         */
        virtual void setUserData(Referenced* obj);

        using osg::Object::setUserData;
        using osg::UserDataContainer::addUserObject;

        /** Get user data.*/
        virtual Referenced* getUserData();

        /** Get const user data.*/
        virtual const Referenced* getUserData() const;

        /** Add user data object. Returns the index position of object added. */
        virtual unsigned int addUserObject(Object* obj);

         /** Add element to list of user data objects.*/
        virtual void setUserObject(unsigned int i, Object* obj);

        /** Remove element from the list of user data objects.*/
        virtual void removeUserObject(unsigned int i);


        /** Get user data object as specified index position. */
        virtual Object* getUserObject(unsigned int i);

        /** Get const user data object as specified index position. */
        virtual const Object* getUserObject(unsigned int i) const;

        /** Get number of user objects assigned to this object.*/
        virtual unsigned int getNumUserObjects() const;

        /** Get the index position of specified user data object.*/
        virtual unsigned int getUserObjectIndex(const osg::Object* obj, unsigned int startPos=0) const;

        /** Get the index position of first user data object that matches specified name.*/
        virtual unsigned int getUserObjectIndex(const std::string& name, unsigned int startPos=0) const;




        /** Set the list of string descriptions.*/
        virtual void setDescriptions(const DescriptionList& descriptions);

        /** Get the description list.*/
        virtual DescriptionList& getDescriptions();

        /** Get the const description list.*/
        virtual const DescriptionList& getDescriptions() const;

        /** Get number of description strings.*/
        virtual unsigned int getNumDescriptions() const;

        /** Add a description string.*/
        virtual void addDescription(const std::string& desc);

protected:

        virtual ~DefaultUserDataContainer() {}

        typedef std::vector< osg::ref_ptr<osg::Object> > ObjectList;

        ref_ptr<Referenced>     _userData;
        DescriptionList         _descriptionList;
        ObjectList              _objectList;
};


/** Convenience function for getting the User Object associated with specified name from an Object's UserDataContainer.*/
inline Object* getUserObject(osg::Object* object, const std::string& name)
{
    osg::UserDataContainer* udc = object->getUserDataContainer();
    return udc ? udc->getUserObject(name) : 0;
}

/** Convenience function for getting the User Object associated with specified name from an Object's UserDataContainer.*/
inline const Object* getUserObject(const osg::Object* object, const std::string& name)
{
    const osg::UserDataContainer* udc = object->getUserDataContainer();
    return udc ? udc->getUserObject(name) : 0;
}

}

#endif

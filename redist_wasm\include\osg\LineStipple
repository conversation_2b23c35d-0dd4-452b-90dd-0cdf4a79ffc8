/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2004 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSG_LINESTIPPLE
#define OSG_LINESTIPPLE 1

#include <osg/StateAttribute>

#ifndef GL_LINE_STIPPLE
    #define GL_LINE_STIPPLE 0x0B24
#endif

namespace osg {

class OSG_EXPORT LineStipple : public StateAttribute
{
    public :

        LineStipple();

        LineStipple(GLint factor, GLushort pattern):
            _factor(factor),
            _pattern(pattern) {}

        /** Copy constructor using CopyOp to manage deep vs shallow copy.*/
        LineStipple(const LineStipple& lw,const CopyOp& copyop=CopyOp::SHALLOW_COPY):
          StateAttribute(lw,copyop),
          _factor(lw._factor),
          _pattern(lw._pattern) {}

        META_StateAttribute(osg, LineStipple, LINESTIPPLE);

        /** return -1 if *this < *rhs, 0 if *this==*rhs, 1 if *this>*rhs. */
        virtual int compare(const StateAttribute& sa) const
        {
            // check if the types are equal and then create the rhs variable.
            // used by the COMPARE_StateAttribute_Parameter macros below.
            COMPARE_StateAttribute_Types(LineStipple,sa)

            // compare each parameter in turn against the rhs.
            COMPARE_StateAttribute_Parameter(_factor)
            COMPARE_StateAttribute_Parameter(_pattern)

            return 0; // passed all the above comparison macros, must be equal.
        }

        virtual bool getModeUsage(StateAttribute::ModeUsage& usage) const
        {
            usage.usesMode(GL_LINE_STIPPLE);
            return true;
        }

        void setFactor(GLint factor);
        inline GLint getFactor() const { return _factor; }

        void setPattern(GLushort pattern);
        inline GLushort getPattern() const { return _pattern; }

        virtual void apply(State& state) const;


    protected :

        virtual ~LineStipple();

        GLint           _factor;
        GLushort        _pattern;

};

}

#endif

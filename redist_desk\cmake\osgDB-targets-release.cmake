#----------------------------------------------------------------
# Generated CMake target import file for configuration "Release".
#----------------------------------------------------------------

# Commands may need to know the format version.
set(CMAKE_IMPORT_FILE_VERSION 1)

# Import target "osg3::osgDB" for configuration "Release"
set_property(TARGET osg3::osgDB APPEND PROPERTY IMPORTED_CONFIGURATIONS RELEASE)
set_target_properties(osg3::osgDB PROPERTIES
  IMPORTED_LINK_INTERFACE_LANGUAGES_RELEASE "CXX;RC"
  IMPORTED_LINK_INTERFACE_LIBRARIES_RELEASE "osg3::osg;osg3::osgUtil;osg3::OpenThreads;opengl32"
  IMPORTED_LOCATION_RELEASE "${_IMPORT_PREFIX}/lib/osg202-osgDB.lib"
  )

list(APPEND _cmake_import_check_targets osg3::osgDB )
list(APPEND _cmake_import_check_files_for_osg3::osgDB "${_IMPORT_PREFIX}/lib/osg202-osgDB.lib" )

# Commands beyond this point should not need to know the version.
set(CMAKE_IMPORT_FILE_VERSION)

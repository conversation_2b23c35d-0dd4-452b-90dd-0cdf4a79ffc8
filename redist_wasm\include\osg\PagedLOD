/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSG_PagedLOD
#define OSG_PagedLOD 1

#include <osg/LOD>

namespace osg {

/** PagedLOD.
*/
class OSG_EXPORT PagedLOD : public LOD
{
    public :

        /** Default constructor
          * The default constructor sets
          *  - the center mode to USER_DEFINED_CENTER and
          *  - enables the paging of external children.
          * @warning The center mode setting of the default constructor differs
          *          from the setting of LOD's default constructor! Therefore,
          *          the PagedLOD and LOD nodes may show different behaviour with
          *          their default settings when using them with the same subgraph.
          *          This is a result of the differences in determining the node's center.
          * @note Though the bounding spheres of both nodes with their default settings
          *       will not differ if they have same subgraph.
          * @sa LOD::setCenter, LOD::setRadius */
        PagedLOD();

        /** Copy constructor using CopyOp to manage deep vs shallow copy.*/
        PagedLOD(const PagedLOD&,const CopyOp& copyop=CopyOp::SHALLOW_COPY);

        META_Node(osg, PagedLOD);

        virtual void traverse(NodeVisitor& nv);

        using osg::Group::addChild;

        virtual bool addChild(Node *child);

        virtual bool addChild(Node *child, float rmin, float rmax);

        template<class T> bool addChild( const ref_ptr<T>& child, float rmin, float rmax) { return addChild(child.get(), rmin, rmax); }

        virtual bool addChild(Node *child, float rmin, float rmax, const std::string& filename, float priorityOffset=0.0f, float priorityScale=1.0f);

        template<class T> bool addChild( const ref_ptr<T>& child, float rmin, float rmax, const std::string& filename, float priorityOffset=0.0f, float priorityScale=1.0f) { return addChild(child.get(), rmin, rmax, filename, priorityOffset, priorityScale); }

        virtual bool removeChildren(unsigned int pos,unsigned int numChildrenToRemove=1);


        /** Set the optional database osgDB::Options object to use when reading children.*/
        void setDatabaseOptions(osg::Referenced* options) { _databaseOptions = options; }

        /** Get the optional database osgDB::Options object used when reading children.*/
        osg::Referenced* getDatabaseOptions() { return _databaseOptions.get(); }

        /** Get the optional database osgDB::Options object used when reading children.*/
        const osg::Referenced* getDatabaseOptions() const { return _databaseOptions.get(); }


        /** Set the database path to prepend to children's filenames.*/
        void setDatabasePath(const std::string& path);

        /** Get the database path used to prepend to children's filenames.*/
        inline const std::string& getDatabasePath() const { return _databasePath; }


        struct OSG_EXPORT PerRangeData
        {
            PerRangeData();
            PerRangeData(const PerRangeData& prd);
            PerRangeData& operator = (const PerRangeData& prd);

            std::string                     _filename;
            float                           _priorityOffset;
            float                           _priorityScale;
            double                          _minExpiryTime;
            unsigned int                    _minExpiryFrames;
            double                          _timeStamp;
            unsigned int                    _frameNumber;
            unsigned int                    _frameNumberOfLastReleaseGLObjects;
            osg::ref_ptr<osg::Referenced>   _databaseRequest;
        };

        typedef std::vector<PerRangeData> PerRangeDataList;

        void setFileName(unsigned int childNo, const std::string& filename) { expandPerRangeDataTo(childNo); _perRangeDataList[childNo]._filename=filename; }
        const std::string& getFileName(unsigned int childNo) const { return _perRangeDataList[childNo]._filename; }
        unsigned int getNumFileNames() const { return static_cast<unsigned int>(_perRangeDataList.size()); }


        void setPriorityOffset(unsigned int childNo, float priorityOffset) { expandPerRangeDataTo(childNo); _perRangeDataList[childNo]._priorityOffset=priorityOffset; }
        float getPriorityOffset(unsigned int childNo) const { return _perRangeDataList[childNo]._priorityOffset; }
        unsigned int getNumPriorityOffsets() const { return static_cast<unsigned int>(_perRangeDataList.size()); }

        void setPriorityScale(unsigned int childNo, float priorityScale) { expandPerRangeDataTo(childNo); _perRangeDataList[childNo]._priorityScale=priorityScale; }
        float getPriorityScale(unsigned int childNo) const { return _perRangeDataList[childNo]._priorityScale; }
        unsigned int getNumPriorityScales() const { return static_cast<unsigned int>(_perRangeDataList.size()); }

        /** Sets the minimum amount of time, in seconds, that must pass without a child being traversed before it can be expired. */
        void setMinimumExpiryTime(unsigned int childNo, double minTime) { expandPerRangeDataTo(childNo); _perRangeDataList[childNo]._minExpiryTime=minTime; }
        double getMinimumExpiryTime(unsigned int childNo) const { return _perRangeDataList[childNo]._minExpiryTime; }
        unsigned int getNumMinimumExpiryTimes() const { return static_cast<unsigned int>(_perRangeDataList.size()); }

         /** Sets the minimum number of frames that must be rendered without a child being traversed before it can be expired. */
        void setMinimumExpiryFrames(unsigned int childNo, unsigned int minFrames) { expandPerRangeDataTo(childNo); _perRangeDataList[childNo]._minExpiryFrames=minFrames; }
        unsigned int getMinimumExpiryFrames(unsigned int childNo) const { return _perRangeDataList[childNo]._minExpiryFrames; }
        unsigned int getNumMinimumExpiryFrames() const { return static_cast<unsigned int>(_perRangeDataList.size()); }


        void setTimeStamp(unsigned int childNo, double timeStamp) { expandPerRangeDataTo(childNo); _perRangeDataList[childNo]._timeStamp=timeStamp; }
        double getTimeStamp(unsigned int childNo) const { return _perRangeDataList[childNo]._timeStamp; }
        unsigned int getNumTimeStamps() const { return static_cast<unsigned int>(_perRangeDataList.size()); }

        void setFrameNumber(unsigned int childNo, unsigned int frameNumber) { expandPerRangeDataTo(childNo); _perRangeDataList[childNo]._frameNumber=frameNumber; }
        unsigned getFrameNumber(unsigned int childNo) const { return _perRangeDataList[childNo]._frameNumber; }
        unsigned int getNumFrameNumbers() const { return static_cast<unsigned int>(_perRangeDataList.size()); }


        /** Return the DatabaseRequest object used by the DatabasePager to keep track of file load requests
          * being carried on behalf of the DatabasePager.
          * Note, in normal OSG usage you should not set this value yourself, as this will be managed by
          * the osgDB::DatabasePager.*/
        osg::ref_ptr<osg::Referenced>& getDatabaseRequest(unsigned int childNo) { return _perRangeDataList[childNo]._databaseRequest; }

        /** Return the const DatabaseRequest object.*/
        const osg::ref_ptr<osg::Referenced>& getDatabaseRequest(unsigned int childNo) const { return _perRangeDataList[childNo]._databaseRequest; }


        /** Set the frame number of the last time that this PageLOD node was traversed.
          * Note, this frame number is automatically set by the traverse() method for all traversals (update, cull etc.).
          */
        inline void setFrameNumberOfLastTraversal(unsigned int frameNumber) { _frameNumberOfLastTraversal=frameNumber; }

        /** Get the frame number of the last time that this PageLOD node was traversed.*/
        inline unsigned int getFrameNumberOfLastTraversal() const { return _frameNumberOfLastTraversal; }


        /** Set the number of children that the PagedLOD must keep around, even if they are older than their expiry time.*/
        inline void setNumChildrenThatCannotBeExpired(unsigned int num) { _numChildrenThatCannotBeExpired = num; }

        /** Get the number of children that the PagedLOD must keep around, even if they are older than their expiry time.*/
        unsigned int getNumChildrenThatCannotBeExpired() const { return _numChildrenThatCannotBeExpired; }

        /** Set whether you want to disable the paging in of external nodes.*/
        void setDisableExternalChildrenPaging(bool flag) { _disableExternalChildrenPaging = flag; }

        bool getDisableExternalChildrenPaging() const { return _disableExternalChildrenPaging; }



        /** Remove the children from the PagedLOD which haven't been visited since specified expiry time and expiry frame number.
          * The removed children are added to the removeChildren list passed into the method,
          * this allows the children to be deleted later at the caller's discretion.
          * Return true if children are removed, false otherwise. */
        virtual bool removeExpiredChildren(double expiryTime, unsigned int expiryFrame, NodeList& removedChildren);

    protected :

        virtual ~PagedLOD();

        void expandPerRangeDataTo(unsigned int pos);

        ref_ptr<Referenced> _databaseOptions;
        std::string         _databasePath;

        unsigned int        _frameNumberOfLastTraversal;
        unsigned int        _numChildrenThatCannotBeExpired;
        bool                _disableExternalChildrenPaging;

        PerRangeDataList    _perRangeDataList;
};

}

#endif

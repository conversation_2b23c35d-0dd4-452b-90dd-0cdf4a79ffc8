<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OSG Interface Compatibility Tests</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .info-panel {
            background-color: #e8f4f8;
            border: 1px solid #bee5eb;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .canvas-container {
            text-align: center;
            margin: 20px 0;
        }
        
        canvas {
            border: 1px solid #ddd;
            background-color: #000;
        }
        
        .output-container {
            margin-top: 20px;
        }
        
        .output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .controls {
            margin: 20px 0;
            text-align: center;
        }
        
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 0 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background-color: #0056b3;
        }
        
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        
        .status {
            text-align: center;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status.loading {
            color: #ffc107;
        }
        
        .status.success {
            color: #28a745;
        }
        
        .status.error {
            color: #dc3545;
        }
        
        .progress {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-bar {
            height: 100%;
            background-color: #007bff;
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>OSG Interface Compatibility Tests</h1>
        
        <div class="info-panel">
            <h3>测试说明</h3>
            <p>此测试套件用于验证OSG（OpenSceneGraph）接口在WebAssembly环境下的兼容性。</p>
            <p>测试将检查核心渲染接口、状态管理、缓冲区操作等关键功能。</p>
            <p><strong>平台：</strong> WebAssembly/Emscripten</p>
            <p><strong>渲染API：</strong> WebGL 2.0</p>
        </div>
        
        <div class="canvas-container">
            <canvas id="canvas" width="800" height="600"></canvas>
        </div>
        
        <div class="controls">
            <button id="startTests" onclick="startTests()">开始测试</button>
            <button id="clearOutput" onclick="clearOutput()">清除输出</button>
            <button id="downloadReport" onclick="downloadReport()" disabled>下载报告</button>
        </div>
        
        <div class="status" id="status">准备就绪</div>
        
        <div class="progress">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        
        <div class="output-container">
            <h3>测试输出</h3>
            <div class="output" id="output"></div>
        </div>
    </div>

    <script type='text/javascript'>
        var Module = {
            preRun: [],
            postRun: [],
            print: function(text) {
                if (arguments.length > 1) text = Array.prototype.slice.call(arguments).join(' ');
                console.log(text);
                appendOutput(text);
            },
            printErr: function(text) {
                if (arguments.length > 1) text = Array.prototype.slice.call(arguments).join(' ');
                console.error(text);
                appendOutput('[ERROR] ' + text, 'error');
            },
            canvas: (function() {
                var canvas = document.getElementById('canvas');
                canvas.addEventListener("webglcontextlost", function(e) {
                    alert('WebGL context lost. You will need to reload the page.');
                    e.preventDefault();
                }, false);
                return canvas;
            })(),
            setStatus: function(text) {
                updateStatus(text);
            },
            totalDependencies: 0,
            monitorRunDependencies: function(left) {
                this.totalDependencies = Math.max(this.totalDependencies, left);
                updateProgress((this.totalDependencies - left) / this.totalDependencies * 100);
                
                if (left == 0) {
                    updateStatus('所有依赖已加载', 'success');
                    document.getElementById('startTests').disabled = false;
                } else {
                    updateStatus('加载依赖中... (' + left + '/' + this.totalDependencies + ')', 'loading');
                }
            }
        };
        
        function appendOutput(text, type = 'normal') {
            var output = document.getElementById('output');
            var timestamp = new Date().toLocaleTimeString();
            var line = '[' + timestamp + '] ' + text + '\n';
            
            if (type === 'error') {
                line = '<span style="color: red;">' + line + '</span>';
            }
            
            output.innerHTML += line;
            output.scrollTop = output.scrollHeight;
        }
        
        function updateStatus(text, type = 'normal') {
            var status = document.getElementById('status');
            status.textContent = text;
            status.className = 'status ' + type;
        }
        
        function updateProgress(percent) {
            var progressBar = document.getElementById('progressBar');
            progressBar.style.width = percent + '%';
        }
        
        function startTests() {
            document.getElementById('startTests').disabled = true;
            updateStatus('正在运行测试...', 'loading');
            appendOutput('开始OSG接口兼容性测试...');
            
            // 这里会调用编译后的C++代码
            // 实际的测试逻辑在main.cpp中
        }
        
        function clearOutput() {
            document.getElementById('output').innerHTML = '';
        }
        
        function downloadReport() {
            // 这个功能需要在C++代码中实现
            // 可以通过Emscripten的文件系统API来实现
            appendOutput('报告下载功能待实现...');
        }
        
        // 初始化
        updateStatus('正在加载...', 'loading');
        updateProgress(0);
        
        // 检查WebGL支持
        function checkWebGLSupport() {
            var canvas = document.createElement('canvas');
            var gl = canvas.getContext('webgl2') || canvas.getContext('webgl');
            
            if (!gl) {
                updateStatus('错误：浏览器不支持WebGL', 'error');
                appendOutput('错误：此浏览器不支持WebGL，无法运行测试。', 'error');
                return false;
            }
            
            appendOutput('WebGL支持检查通过');
            appendOutput('WebGL版本: ' + gl.getParameter(gl.VERSION));
            appendOutput('渲染器: ' + gl.getParameter(gl.RENDERER));
            appendOutput('厂商: ' + gl.getParameter(gl.VENDOR));
            
            return true;
        }
        
        // 页面加载完成后检查WebGL支持
        window.addEventListener('load', function() {
            if (!checkWebGLSupport()) {
                document.getElementById('startTests').disabled = true;
            }
        });
    </script>
    
    {{{ SCRIPT }}}
</body>
</html>

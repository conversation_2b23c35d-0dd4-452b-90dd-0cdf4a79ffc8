/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2010 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
 *
 * StandardManipulator code Copyright (C) 2010 PCJohn (<PERSON>)
 * while some pieces of code were taken from OSG.
 * Thanks to company Cadwork (www.cadwork.ch) and
 * Brno University of Technology (www.fit.vutbr.cz) for open-sourcing this work.
*/

#ifndef OSGGA_CAMERA_MANIPULATOR
#define OSGGA_CAMERA_MANIPULATOR 1

#include <osgGA/CameraManipulator>


namespace osgGA {


/** StandardManipulator class provides basic functionality
    for user controlled manipulation.*/
class OSGGA_EXPORT StandardManipulator : public CameraManipulator
{
        typedef CameraManipulator inherited;

    public:

        // flags
        enum UserInteractionFlags
        {
            UPDATE_MODEL_SIZE = 0x01,
            COMPUTE_HOME_USING_BBOX = 0x02,
            PROCESS_MOUSE_WHEEL = 0x04,
            SET_CENTER_ON_WHEEL_FORWARD_MOVEMENT = 0x08,
            DEFAULT_SETTINGS = UPDATE_MODEL_SIZE /*| COMPUTE_HOME_USING_BBOX*/ | PROCESS_MOUSE_WHEEL
        };

        StandardManipulator( int flags = DEFAULT_SETTINGS );
        StandardManipulator( const StandardManipulator& m,
                             const osg::CopyOp& copyOp = osg::CopyOp::SHALLOW_COPY );

        // We are not using META_Object as this is abstract class.
        // Use META_Object(osgGA,YourManipulator); in your descendant non-abstract classes.
        virtual const char* className() const { return "StandardManipulator"; }

        /** Sets manipulator by eye position and eye orientation.*/
        virtual void setTransformation( const osg::Vec3d& eye, const osg::Quat& rotation ) = 0;

        /** Sets manipulator by eye position, center of rotation, and up vector.*/
        virtual void setTransformation( const osg::Vec3d& eye, const osg::Vec3d& center, const osg::Vec3d& up ) = 0;

        /** Gets manipulator's eye position and eye orientation.*/
        virtual void getTransformation( osg::Vec3d& eye, osg::Quat& rotation ) const = 0;

        /** Gets manipulator's focal center, eye position, and up vector.*/
        virtual void getTransformation( osg::Vec3d& eye, osg::Vec3d& center, osg::Vec3d& up ) const = 0;

        virtual void setNode( osg::Node* );
        virtual const osg::Node* getNode() const;
        virtual osg::Node* getNode();

        virtual void setVerticalAxisFixed( bool value );
        inline bool getVerticalAxisFixed() const;
        inline bool getAllowThrow() const;
        virtual void setAllowThrow( bool allowThrow );

        virtual void setAnimationTime( const double t );
        double getAnimationTime() const;
        bool isAnimating() const;
        virtual void finishAnimation();

        virtual void home( const osgGA::GUIEventAdapter& ea, osgGA::GUIActionAdapter& us );
        virtual void home( double );

        virtual void init( const osgGA::GUIEventAdapter& ea, osgGA::GUIActionAdapter& us );
        virtual bool handle( const osgGA::GUIEventAdapter& ea, osgGA::GUIActionAdapter& us );
        virtual void getUsage( osg::ApplicationUsage& usage ) const;

    protected:

        virtual bool handleFrame( const osgGA::GUIEventAdapter& ea, osgGA::GUIActionAdapter& us );
        virtual bool handleResize( const osgGA::GUIEventAdapter& ea, osgGA::GUIActionAdapter& us );
        virtual bool handleMouseMove( const osgGA::GUIEventAdapter& ea, osgGA::GUIActionAdapter& us );
        virtual bool handleMouseDrag( const osgGA::GUIEventAdapter& ea, osgGA::GUIActionAdapter& us );
        virtual bool handleMousePush( const osgGA::GUIEventAdapter& ea, osgGA::GUIActionAdapter& us );
        virtual bool handleMouseRelease( const osgGA::GUIEventAdapter& ea, osgGA::GUIActionAdapter& us );
        virtual bool handleKeyDown( const osgGA::GUIEventAdapter& ea, osgGA::GUIActionAdapter& us );
        virtual bool handleKeyUp( const osgGA::GUIEventAdapter& ea, osgGA::GUIActionAdapter& us );
        virtual bool handleMouseWheel( const osgGA::GUIEventAdapter& ea, osgGA::GUIActionAdapter& us );
        virtual bool handleMouseDeltaMovement( const osgGA::GUIEventAdapter& ea, osgGA::GUIActionAdapter& us );

        virtual bool performMovement();
        virtual bool performMovementLeftMouseButton( const double eventTimeDelta, const double dx, const double dy );
        virtual bool performMovementMiddleMouseButton( const double eventTimeDelta, const double dx, const double dy );
        virtual bool performMovementRightMouseButton( const double eventTimeDelta, const double dx, const double dy );
        virtual bool performMouseDeltaMovement( const float dx, const float dy );
        virtual bool performAnimationMovement( const osgGA::GUIEventAdapter& ea, osgGA::GUIActionAdapter& us );
        virtual void applyAnimationStep( const double currentProgress, const double prevProgress );

        void addMouseEvent( const osgGA::GUIEventAdapter& ea );
        void flushMouseEventStack();
        virtual bool isMouseMoving() const;
        float getThrowScale( const double eventTimeDelta ) const;
        virtual void centerMousePointer( const osgGA::GUIEventAdapter& ea, osgGA::GUIActionAdapter& us );

        static void rotateYawPitch( osg::Quat& rotation, const double yaw, const double pitch,
                                    const osg::Vec3d& localUp = osg::Vec3d( 0.,0.,0.) );
        static void fixVerticalAxis( osg::Quat& rotation, const osg::Vec3d& localUp, bool disallowFlipOver );
        void fixVerticalAxis( osg::Vec3d& eye, osg::Quat& rotation, bool disallowFlipOver );
        static void fixVerticalAxis( const osg::Vec3d& forward, const osg::Vec3d& up, osg::Vec3d& newUp,
                                     const osg::Vec3d& localUp, bool disallowFlipOver );
        virtual bool setCenterByMousePointerIntersection( const osgGA::GUIEventAdapter& ea, osgGA::GUIActionAdapter& us );
        virtual bool startAnimationByMousePointerIntersection( const osgGA::GUIEventAdapter& ea, osgGA::GUIActionAdapter& us );

        // mouse state
        bool _thrown;
        bool _allowThrow;
        float _mouseCenterX, _mouseCenterY;

        // internal event stack comprising last two mouse events.
        osg::ref_ptr< const osgGA::GUIEventAdapter > _ga_t1;
        osg::ref_ptr< const osgGA::GUIEventAdapter > _ga_t0;

        /** The approximate amount of time it is currently taking to draw a frame.
            * This is used to compute the delta in translation/rotation during a thrown display update.
            * It allows us to match an delta in position/rotation independent of the rendering frame rate.
            */
        double _delta_frame_time;

        /** The time the last frame started.
            * Used when _rate_sensitive is true so that we can match display update rate to rotation/translation rate.
            */
        double _last_frame_time;

        // scene data
        osg::ref_ptr< osg::Node > _node;
        double _modelSize;
        bool _verticalAxisFixed;

        // animation stuff
        class OSGGA_EXPORT AnimationData : public osg::Referenced {
        public:
            double _animationTime;
            bool _isAnimating;
            double _startTime;
            double _phase;

            AnimationData();
            void start( const double startTime );
        };
        osg::ref_ptr< AnimationData > _animationData;
        virtual void allocAnimationData() { _animationData = new AnimationData(); }

        // flags
        int _flags;

        // flags indicating that a value is relative to model size
        int _relativeFlags;
        inline bool getRelativeFlag( int index ) const;
        inline void setRelativeFlag( int index, bool value );
        static int numRelativeFlagsAllocated;
        static int allocateRelativeFlag();
};


//
//  inline methods
//

inline bool StandardManipulator::getRelativeFlag( int index ) const
{
    return ( _relativeFlags & (0x01 << index) ) != 0;
}

inline void StandardManipulator::setRelativeFlag( int index, bool value )
{
    if( value )  _relativeFlags |= (0x01 << index);
    else  _relativeFlags &= ~(0x01 << index);
}

/// Returns whether manipulator preserves camera's "UP" vector.
inline bool StandardManipulator::getVerticalAxisFixed() const
{
    return _verticalAxisFixed;
}

/// Returns true if the camera can be thrown, false otherwise. It defaults to true.
inline bool StandardManipulator::getAllowThrow() const
{
    return _allowThrow;
}


}

#endif /* OSGGA_CAMERA_MANIPULATOR */

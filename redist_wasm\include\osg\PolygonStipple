/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/
// -*- Mode: c++ -*-

#ifndef OSG_POLYGONSTIPPLE
#define OSG_POLYGONSTIPPLE 1

#include <osg/StateAttribute>

#ifndef GL_POLYGON_STIPPLE
    #define GL_POLYGON_STIPPLE  0x0B42
#endif

namespace osg
{

class OSG_EXPORT PolygonStipple : public StateAttribute
{
    public :

        PolygonStipple();

        PolygonStipple(const GLubyte* mask);

        /** Copy constructor using CopyOp to manage deep vs shallow copy.*/
        PolygonStipple(const PolygonStipple& lw,const CopyOp& copyop=CopyOp::SHALLOW_COPY);

        META_StateAttribute(osg, PolygonStipple, POLYGONSTIPPLE);

        /** return -1 if *this < *rhs, 0 if *this==*rhs, 1 if *this>*rhs.*/
        virtual int compare(const StateAttribute& sa) const;

        virtual bool getModeUsage(StateAttribute::ModeUsage& usage) const
        {
            usage.usesMode(GL_POLYGON_STIPPLE);
            return true;
        }

        /** set the mask up, copying 128 bytes (32x32 bitfield) from mask into the local _mask.*/
        void setMask(const GLubyte* mask);

        /** get a pointer to the mask.*/
        inline const GLubyte* getMask() const {return _mask;}


        virtual void apply(State& state) const;

    protected :

        virtual ~PolygonStipple();

        GLubyte _mask[128];

};

}

#endif

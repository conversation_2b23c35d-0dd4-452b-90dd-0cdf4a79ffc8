/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSG_TEXENV
#define OSG_TEXENV 1

#include <osg/TextureAttribute>
#include <osg/Uniform>
#include <osg/StateSet>

#ifndef OSG_GL_FIXED_FUNCTION_AVAILABLE
    #define GL_MODULATE         0x2100
    #define GL_ADD              0x0104
    #define GL_MODULATE         0x2100
    #define GL_DECAL            0x2101
#endif

namespace osg {

/** TexEnv encapsulates the OpenGL glTexEnv (texture environment) state.
*/
class OSG_EXPORT TexEnv : public TextureAttribute
{
    public :

        enum Mode {
            DECAL     = GL_DECAL,
            MODULATE  = GL_MODULATE,
            BLEND     = GL_BLEND,
            REPLACE   = GL_REPLACE,
            ADD       = GL_ADD
        };

        TexEnv(Mode mode=MODULATE);

        /** Copy constructor using CopyOp to manage deep vs shallow copy. */
        TexEnv(const TexEnv& texenv,const CopyOp& copyop=CopyOp::SHALLOW_COPY);


        META_StateAttribute(osg, TexEnv, TEXENV);

        /** Return -1 if *this < *rhs, 0 if *this==*rhs, 1 if *this>*rhs. */
        virtual int compare(const StateAttribute& sa) const
        {
            // Check for equal types, then create the rhs variable
            // used by the COMPARE_StateAttribute_Parameter macros below.
            COMPARE_StateAttribute_Types(TexEnv,sa)

            // Compare each parameter in turn against the rhs.
            COMPARE_StateAttribute_Parameter(_mode)
            COMPARE_StateAttribute_Parameter(_color)

            return 0; // Passed all the above comparison macros, so must be equal.
        }


        void setMode( Mode mode );

        Mode getMode() const { return _mode; }


        void setColor( const Vec4& color ) { _color->setValue(color); }

        Vec4& getColor() { return _color->getValue(); }

        const Vec4& getColor() const { return _color->getValue(); }


        virtual void apply(State& state) const;

    protected :

        virtual ~TexEnv( void );

        void configureUniformNames();

        Mode                    _mode;
        ref_ptr<Vec4Uniform>    _color;
        StateSet::DefineList    _defineList;
};

}

#endif

/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2009 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSGVOLUME_LOCATOR
#define OSGVOLUME_LOCATOR 1

#include <osgVolume/Export>

#include <osg/Object>
#include <osg/observer_ptr>
#include <osg/Matrixd>
#include <osg/TexGen>
#include <osg/MatrixTransform>

#include <vector>

namespace osgVolume {

class OSGVOLUME_EXPORT Locator : public osg::Object
{
    public:

        Locator() {}

        Locator(const osg::Matrixd& transform) { setTransform(transform); }

        /** Copy constructor using CopyOp to manage deep vs shallow copy.*/
        Locator(const Locator& locator,const osg::CopyOp& copyop=osg::CopyOp::SHALLOW_COPY):
            osg::Object(locator, copyop),
            _transform(locator._transform) {}

        META_Object(osgVolume, Locator);

        /** Set the transformation from local coordinates to model coordinates.*/
        void setTransform(const osg::Matrixd& transform) { _transform = transform; _inverse.invert(_transform); locatorModified(); }

        /** Set the transformation from local coordinates to model coordinates.*/
        const osg::Matrixd& getTransform() const { return _transform; }

        /** Set the extents of the local coords.*/
        void setTransformAsExtents(double minX, double minY, double maxX, double maxY, double minZ, double maxZ);


        virtual bool convertLocalToModel(const osg::Vec3d& /*local*/, osg::Vec3d& /*world*/) const;

        virtual bool convertModelToLocal(const osg::Vec3d& /*world*/, osg::Vec3d& /*local*/) const;

        static bool convertLocalCoordBetween(const Locator& source, const osg::Vec3d& sourceNDC,
                                             const Locator& destination, osg::Vec3d& destinationNDC)
        {
            osg::Vec3d model;
            if (!source.convertLocalToModel(sourceNDC, model)) return false;
            if (!destination.convertModelToLocal(model, destinationNDC)) return false;
            return true;
        }

        bool computeLocalBounds(osg::Vec3d& bottomLeft, osg::Vec3d& topRight) const;
        bool computeLocalBounds(Locator& source, osg::Vec3d& bottomLeft, osg::Vec3d& topRight) const;

        /** Return true if the axis of the Locator are inverted requiring the faces of any cubes used from rendering to be flipped to ensure the correct front/back face is used.*/
        bool inverted() const;

        /** apply the appropriate FrontFace setting to provided StateSet to ensure that the rendering of hull of the volume is the correct orientation.*/
        void applyAppropriateFrontFace(osg::StateSet* ss) const;


        /** Callback interface for enabling the monitoring of changes to the Locator.*/
        class LocatorCallback : virtual public osg::Object
        {
            public:
                LocatorCallback() {}
                LocatorCallback(const LocatorCallback& rhs, const osg::CopyOp& copyop=osg::CopyOp::SHALLOW_COPY): osg::Object(rhs,copyop) {}
                META_Object(osgVolume, LocatorCallback);

                virtual void locatorModified(Locator* /*locator*/) {};

            protected:
                virtual ~LocatorCallback() {}
        };

        void addCallback(LocatorCallback* callback);
        template<class T> void addCallback(const osg::ref_ptr<T>& callback) { addCallback(callback.get()); }


        void removeCallback(LocatorCallback* callback);

        typedef std::vector< osg::ref_ptr<LocatorCallback> > LocatorCallbacks;
        LocatorCallbacks& getLocatorCallbacks() { return _locatorCallbacks; }
        const LocatorCallbacks& getLocatorCallbacks() const { return _locatorCallbacks; }

    protected:

        void locatorModified();
        osg::Matrixd        _transform;
        osg::Matrixd        _inverse;

        LocatorCallbacks    _locatorCallbacks;
};

class OSGVOLUME_EXPORT TransformLocatorCallback : public Locator::LocatorCallback
{
    public:

        TransformLocatorCallback(osg::MatrixTransform* transform);

        void locatorModified(Locator* locator);

    protected:

        osg::observer_ptr<osg::MatrixTransform> _transform;
};


class OSGVOLUME_EXPORT TexGenLocatorCallback : public Locator::LocatorCallback
{
    public:

        TexGenLocatorCallback(osg::TexGen* texgen, Locator* geometryLocator, Locator* imageLocator);

        void locatorModified(Locator*);

    protected:

        osg::observer_ptr<osg::TexGen> _texgen;
        osg::observer_ptr<osgVolume::Locator> _geometryLocator;
        osg::observer_ptr<osgVolume::Locator> _imageLocator;
};


}

#endif

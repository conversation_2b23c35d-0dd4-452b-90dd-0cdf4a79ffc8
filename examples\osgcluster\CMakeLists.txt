SET(TARGET_SRC
    broadcaster.cpp
    receiver.cpp
    osgcluster.cpp
)

IF   (WIN32)
   SET(TARGET_EXTERNAL_LIBRARIES ws2_32)
ELSE(WIN32)
   CHECK_LIBRARY_EXISTS("nsl" "gethostbyname" "" LIB_NSL_HAS_GETHOSTBYNAME)
   IF(LIB_NSL_HAS_GETHOSTBYNAME)
      SET(TARGET_EXTERNAL_LIBRARIES ${TARGET_EXTERNAL_LIBRARIES} nsl)
   ENDIF()
   CHECK_LIBRARY_EXISTS("socket" "socket" "" LIB_SOCKET_HAS_SOCKET)
   IF(LIB_SOCKET_HAS_SOCKET)
      SET(TARGET_EXTERNAL_LIBRARIES ${TARGET_EXTERNAL_LIBRARIES} socket)
   ENDIF()
ENDIF(WIN32)

#### end var setup  ###
SETUP_EXAMPLE(osgcluster)

/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/
//osgManipulator - Copyright (C) 2007 Fugro-Jason B.V.

#ifndef OSGMANIPULATOR_TABPLANEDRAGGER
#define OSGMANIPULATOR_TABPLANEDRAGGER 1

#include <osgManipulator/TranslatePlaneDragger>
#include <osgManipulator/Scale2DDragger>
#include <osgManipulator/Scale1DDragger>

namespace osgManipulator {

/**
 * Tab plane dragger consists of a plane with tabs on it's corners and edges
 * for scaling. And the plane is used as a 2D translate dragger.
 */
class OSGMANIPULATOR_EXPORT TabPlaneDragger : public CompositeDragger
{
    public:

        TabPlaneDragger(float handleScaleFactor=20.0f);

        META_OSGMANIPULATOR_Object(osgManipulator,TabPlaneDragger)

        virtual bool handle(const PointerInfo& pi, const osgGA::GUIEventAdapter& ea, osgGA::GUIActionAdapter& us);

        /** Setup default geometry for dragger. */
        void setupDefaultGeometry(bool twoSidedHandle = true);

        void setPlaneColor(const osg::Vec4& color) { _translateDragger->setColor(color); }

    protected:

        virtual ~TabPlaneDragger();

        osg::ref_ptr< TranslatePlaneDragger >   _translateDragger;
        osg::ref_ptr< Scale2DDragger >          _cornerScaleDragger;
        osg::ref_ptr< Scale1DDragger >          _horzEdgeScaleDragger;
        osg::ref_ptr< Scale1DDragger >          _vertEdgeScaleDragger;

        float                                   _handleScaleFactor;
};


}

#endif

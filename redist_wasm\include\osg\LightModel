/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSG_LIGHTMODEL
#define OSG_LIGHTMODEL 1

#include <osg/StateAttribute>
#include <osg/Vec4>

namespace osg {

class OSG_EXPORT LightModel : public StateAttribute
{
    public :

        LightModel();

        /** Copy constructor using CopyOp to manage deep vs shallow copy. */
        LightModel(const LightModel& lw,const CopyOp& copyop=CopyOp::SHALLOW_COPY):
          StateAttribute(lw,copyop),
          _ambient(lw._ambient),
          _colorControl(lw._colorControl),
          _localViewer(lw._localViewer),
          _twoSided(lw._twoSided) {}


        META_StateAttribute(osg, LightModel, LIGHTMODEL);

        /** Return -1 if *this < *rhs, 0 if *this==*rhs, 1 if *this>*rhs. */
        virtual int compare(const StateAttribute& sa) const
        {
            // check the types are equal and then create the rhs variable
            // used by the COMPARE_StateAttribute_Parameter macros below.
            COMPARE_StateAttribute_Types(LightModel,sa)

            // compare each parameter in turn against the rhs.
            COMPARE_StateAttribute_Parameter(_ambient)
            COMPARE_StateAttribute_Parameter(_colorControl)
            COMPARE_StateAttribute_Parameter(_localViewer)
            COMPARE_StateAttribute_Parameter(_twoSided)

            return 0; // passed all the above comparison macros, must be equal.
        }


        void setAmbientIntensity(const osg::Vec4& ambient) { _ambient = ambient; }
        const osg::Vec4& getAmbientIntensity() const { return _ambient; }


        enum ColorControl
        {
            SEPARATE_SPECULAR_COLOR,
            SINGLE_COLOR
        };

        void setColorControl(ColorControl cc) { _colorControl = cc; }
        inline ColorControl getColorControl() const { return _colorControl; }


        void setLocalViewer(bool localViewer) { _localViewer=localViewer; }
        inline bool getLocalViewer() const { return _localViewer; }


        void setTwoSided(bool twoSided) { _twoSided = twoSided; }
        inline bool getTwoSided() const { return _twoSided; }



        virtual void apply(State& state) const;


    protected :

        virtual ~LightModel();

        osg::Vec4 _ambient;
        ColorControl _colorControl;
        bool _localViewer;
        bool _twoSided;

};

}

#endif

/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/
//osgFX - Copyright (C) 2003 Marco <PERSON>

#ifndef OSGFX_REGISTRY_
#define OSGFX_REGISTRY_

#include <osgFX/Export>
#include <osgFX/Effect>

#include <osg/ref_ptr>

#include <map>
#include <string>

namespace osgFX
{

    class OSGFX_EXPORT Registry : public osg::Referenced
    {
    public:

        struct Proxy {
            Proxy(const Effect* effect): _effect(effect)
            {
                Registry::instance()->registerEffect(effect);
            }

            ~Proxy()
            {
                Registry::instance()->removeEffect(_effect.get());
            }
        private:
            osg::ref_ptr<const Effect> _effect;
        };

        typedef std::map<std::string, osg::ref_ptr<const Effect> > EffectMap;

        static Registry* instance();

        inline void registerEffect(const Effect* effect);

        inline void removeEffect(const Effect* effect);

        inline const EffectMap& getEffectMap() const;

    protected:

        // Registry is a singleton; constructor and destructor must be protected
        Registry();
        ~Registry() {}

    private:
        EffectMap _effects;
    };

    // INLINE METHODS



    inline const Registry::EffectMap& Registry::getEffectMap() const
    {
        return _effects;
    }

    inline void Registry::registerEffect(const Effect* effect)
    {
        _effects[effect->effectName()] = effect;
    }

    inline void Registry::removeEffect(const Effect* effect)
    {
        EffectMap::iterator itr = _effects.find(effect->effectName());
        if (itr != _effects.end())
        {
            _effects.erase(itr);
        }
    }

}

#endif

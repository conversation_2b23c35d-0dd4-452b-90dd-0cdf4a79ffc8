/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSG_IMAGESEQUENCE
#define OSG_IMAGESEQUENCE 1

#include <OpenThreads/Mutex>
#include <osg/ImageStream>

#include <list>
#include <set>

namespace osg {

/**
  * Image Buffer class.
*/
class OSG_EXPORT ImageSequence : public ImageStream
{
    public:
        ImageSequence();

        /** Copy constructor using CopyOp to manage deep vs shallow copy. */
        ImageSequence(const ImageSequence& ImageSequence, const CopyOp& copyop=CopyOp::SHALLOW_COPY);

        virtual Object* cloneType() const { return new ImageSequence(); }
        virtual Object* clone(const CopyOp& copyop) const { return new ImageSequence(*this,copyop); }
        virtual bool isSameKindAs(const Object* obj) const { return dynamic_cast<const ImageSequence*>(obj)!=0; }
        virtual const char* libraryName() const { return "osg"; }
        virtual const char* className() const { return "ImageSequence"; }

        /** Return -1 if *this < *rhs, 0 if *this==*rhs, 1 if *this>*rhs. */
        virtual int compare(const Image& rhs) const;

        virtual void setReferenceTime(double t) { _referenceTime = t; }
        virtual double getReferenceTime() const { return _referenceTime; }

        virtual void setTimeMultiplier(double tm) { _timeMultiplier = tm; }
        virtual double getTimeMultiplier() const { return _timeMultiplier; }

        struct OSG_EXPORT ImageData
        {
            ImageData();
            ImageData(const ImageData& id);
            ImageData& operator = (const ImageData& id);

            std::string                     _filename;
            osg::ref_ptr<osg::Image>        _image;
            osg::ref_ptr<osg::Referenced>   _imageRequest;
        };

        typedef std::vector<ImageData> ImageDataList;

        virtual void seek(double time);

        virtual void play();

        virtual void pause();

        virtual void rewind();

        enum Mode
        {
            PRE_LOAD_ALL_IMAGES,
            PAGE_AND_RETAIN_IMAGES,
            PAGE_AND_DISCARD_USED_IMAGES,
            LOAD_AND_RETAIN_IN_UPDATE_TRAVERSAL,
            LOAD_AND_DISCARD_IN_UPDATE_TRAVERSAL
        };

        void setMode(Mode mode);
        Mode getMode() const { return _mode; }

        void setLength(double length);
        virtual double getLength() const { return _length; }


        void addImageFile(const std::string& fileName);

        void setImageFile(unsigned int pos, const std::string& fileName);
        std::string getImageFile(unsigned int pos) const;

        void addImage(osg::Image* image);

        template<class  T> void addImage(const osg::ref_ptr<T>& image) { addImage(image.get()); }

        void setImage(int s,int t,int r,
                      GLint internalTextureformat,
                      GLenum pixelFormat,GLenum type,
                      unsigned char* data,
                      AllocationMode mode,
                      int packing=1) { Image::setImage(s,t,r,internalTextureformat, pixelFormat, type, data, mode, packing); }

        void setImage(unsigned int pos, osg::Image* image);

        template<class  T> void setImage(unsigned int pos, const osg::ref_ptr<T>& image) { setImage(pos, image.get()); }

        Image* getImage(unsigned int pos);
        const Image* getImage(unsigned int pos) const;

        unsigned int getNumImageData() const { return _imageDataList.size(); }

        ImageDataList& getImageDataList() { return _imageDataList; }
        const ImageDataList& getImageDataList() const { return _imageDataList; }


        /** ImageSequence requires a call to update(NodeVisitor*) during the update traversal so return true.*/
        virtual bool requiresUpdateCall() const { return true; }

        /** update method for osg::Image subclasses that update themselves during the update traversal.*/
        virtual void update(NodeVisitor* nv);


        /** Set the optional osgDB::Options object to use when reading images.*/
        void setReadOptions(osg::Referenced* options) { _readOptions = options; }

        /** Get the optional osgDB::Options object used when reading images.*/
        osg::Referenced* getReadOptions() { return _readOptions.get(); }

        /** Get the optional osgDB::Options object used when reading images.*/
        const osg::Referenced* getReadOptions() const { return _readOptions.get(); }

protected:

        virtual ~ImageSequence() {}

        virtual void applyLoopingMode();

        void setImageToChild(int pos);

        void computeTimePerImage();

        int imageIndex(double time);

        // setImage without acquiring mutex.
        void _setImage(unsigned int pos, osg::Image* image);

        double                          _referenceTime;
        double                          _timeMultiplier;

        Mode                            _mode;
        double                          _length;

        double                          _timePerImage;

        mutable OpenThreads::Mutex      _mutex;

        ImageDataList                   _imageDataList;

        int                             _previousAppliedImageIndex;


        bool                            _seekTimeSet;
        double                          _seekTime;

        osg::ref_ptr<osg::Referenced>       _readOptions;

};

} // namespace

#endif

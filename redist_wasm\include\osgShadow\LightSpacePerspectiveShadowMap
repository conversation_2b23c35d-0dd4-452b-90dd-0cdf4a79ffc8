/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
 *
 * ViewDependentShadow codes Copyright (C) 2008 W<PERSON><PERSON><PERSON><PERSON>
 * Thanks to to my company http://www.ai.com.pl for allowing me free this work.
*/


#ifndef OSGSHADOW_LIGHTSPACEPERSPECTIVESHADOWMAP
#define OSGSHADOW_LIGHTSPACEPERSPECTIVESHADOWMAP 1

#include <osgShadow/MinimalCullBoundsShadowMap>
#include <osgShadow/MinimalDrawBoundsShadowMap>
#include <osgShadow/ProjectionShadowMap>

namespace osgShadow {

// Class implements
// "Light Space Perspective Shadow Maps" algorithm by
// <PERSON> Wimmer, <PERSON>herzer, <PERSON> Purgathofer
// http://www.cg.tuwien.ac.at/research/vr/lispsm/

class LispSM;

class OSGSHADOW_EXPORT LightSpacePerspectiveShadowMapAlgorithm
{
    public:
        LightSpacePerspectiveShadowMapAlgorithm();
        ~LightSpacePerspectiveShadowMapAlgorithm();

        void operator() (
            const osgShadow::ConvexPolyhedron* hullShadowedView,
            const osg::Camera* cameraMain,
            osg::Camera* cameraShadow ) const;

    protected:
        LispSM * lispsm;
};

// Optimized for draw traversal shadow bounds
class OSGSHADOW_EXPORT LightSpacePerspectiveShadowMapDB: public ProjectionShadowMap< MinimalDrawBoundsShadowMap, LightSpacePerspectiveShadowMapAlgorithm >
{
    public:
        /** Convenient typedef used in definition of ViewData struct and methods */
        typedef ProjectionShadowMap< MinimalDrawBoundsShadowMap, LightSpacePerspectiveShadowMapAlgorithm > BaseClass;

        /** Classic OSG constructor */
        LightSpacePerspectiveShadowMapDB()
        {
        }

        /** Classic OSG cloning constructor */
        LightSpacePerspectiveShadowMapDB(
            const LightSpacePerspectiveShadowMapDB& copy,
            const osg::CopyOp& copyop=osg::CopyOp::SHALLOW_COPY) : BaseClass(copy,copyop)
        {
        }

        /** Declaration of standard OSG object methods */
        META_Object( osgShadow, LightSpacePerspectiveShadowMapDB );
};

// Optimized for cull traversal shadow bounds
class OSGSHADOW_EXPORT LightSpacePerspectiveShadowMapCB: public ProjectionShadowMap< MinimalCullBoundsShadowMap, LightSpacePerspectiveShadowMapAlgorithm >
{
    public:
        /** Convenient typedef used in definition of ViewData struct and methods */
        typedef ProjectionShadowMap< MinimalCullBoundsShadowMap, LightSpacePerspectiveShadowMapAlgorithm > BaseClass;

        /** Classic OSG constructor */
        LightSpacePerspectiveShadowMapCB()
        {
        }

        /** Classic OSG cloning constructor */
        LightSpacePerspectiveShadowMapCB(
            const LightSpacePerspectiveShadowMapCB& copy,
            const osg::CopyOp& copyop=osg::CopyOp::SHALLOW_COPY) : BaseClass(copy,copyop)
        {
        }

        /** Declaration of standard OSG object methods */
        META_Object( osgShadow, LightSpacePerspectiveShadowMapCB );
};

// Optimized for view frustum bounds
class OSGSHADOW_EXPORT LightSpacePerspectiveShadowMapVB: public ProjectionShadowMap< MinimalShadowMap, LightSpacePerspectiveShadowMapAlgorithm >
{
    public:
        /** Convenient typedef used in definition of ViewData struct and methods */
        typedef ProjectionShadowMap< MinimalShadowMap, LightSpacePerspectiveShadowMapAlgorithm > BaseClass;

        /** Classic OSG constructor */
        LightSpacePerspectiveShadowMapVB()
        {
        }

        /** Classic OSG cloning constructor */
        LightSpacePerspectiveShadowMapVB(
            const LightSpacePerspectiveShadowMapVB& copy,
            const osg::CopyOp& copyop=osg::CopyOp::SHALLOW_COPY) : BaseClass(copy,copyop)
        {
        }

        /** Declaration of standard OSG object methods */
        META_Object( osgShadow, LightSpacePerspectiveShadowMapVB );
};

typedef LightSpacePerspectiveShadowMapDB LightSpacePerspectiveShadowMap;

} // namespace osgShadow

#endif

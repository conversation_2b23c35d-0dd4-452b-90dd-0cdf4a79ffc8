/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSGUTIL_UPDATEVISITOR
#define OSGUTIL_UPDATEVISITOR 1

#include <osg/NodeVisitor>
#include <osg/Geode>
#include <osg/Billboard>
#include <osg/LOD>
#include <osg/Switch>
#include <osg/LightSource>
#include <osg/Transform>
#include <osg/Projection>
#include <osg/OccluderNode>
#include <osg/ScriptEngine>

#include <osgUtil/Export>

namespace osgUtil {

/**
 * Basic UpdateVisitor implementation for animating a scene.
 * This visitor traverses the scene graph, calling each nodes appCallback if
 * it exists.
 */
class OSGUTIL_EXPORT UpdateVisitor : public osg::NodeVisitor
{
    public:

        UpdateVisitor();
        virtual ~UpdateVisitor();

        META_NodeVisitor(osgUtil, UpdateVisitor)

        /** Convert 'this' into a osgUtil::UpdateVisitor pointer if Object is a osgUtil::UpdateVisitor, otherwise return 0.
          * Equivalent to dynamic_cast<osgUtil::UpdateVisitor*>(this).*/
        virtual osgUtil::UpdateVisitor* asUpdateVisitor() { return this; }

        /** convert 'const this' into a const osgUtil::UpdateVisitor pointer if Object is a osgUtil::UpdateVisitor, otherwise return 0.
          * Equivalent to dynamic_cast<const osgUtil::UpdateVisitor*>(this).*/
        virtual const osgUtil::UpdateVisitor* asUpdateVisitor() const { return this; }

        virtual void reset();

        /** During traversal each type of node calls its callbacks and its children traversed. */
        virtual void apply(osg::Node& node) { handle_callbacks_and_traverse(node); }

        virtual void apply(osg::Drawable& drawable)
        {
            osg::Callback* callback = drawable.getUpdateCallback();
            if (callback)
            {
                osg::DrawableUpdateCallback* drawable_callback = callback->asDrawableUpdateCallback();
                osg::NodeCallback* node_callback = callback->asNodeCallback();

                if (drawable_callback) drawable_callback->update(this,&drawable);
                if (node_callback) (*node_callback)(&drawable, this);

                if (!drawable_callback && !node_callback)  callback->run(&drawable, this);
            }

            handle_callbacks(drawable.getStateSet());
        }

        // The following overrides are technically redundant as the default implementation would eventually trickle down to
        // apply(osg::Node&); - however defining these explicitly should save a couple of virtual function calls
        virtual void apply(osg::Geode& node)        { handle_callbacks_and_traverse(node); }
        virtual void apply(osg::Billboard& node)    { handle_callbacks_and_traverse(node); }
        virtual void apply(osg::LightSource& node)  { handle_callbacks_and_traverse(node); }
        virtual void apply(osg::Group& node)        { handle_callbacks_and_traverse(node); }
        virtual void apply(osg::Transform& node)    { handle_callbacks_and_traverse(node); }
        virtual void apply(osg::Projection& node)   { handle_callbacks_and_traverse(node); }
        virtual void apply(osg::Switch& node)       { handle_callbacks_and_traverse(node); }
        virtual void apply(osg::LOD& node)          { handle_callbacks_and_traverse(node); }
        virtual void apply(osg::OccluderNode& node) { handle_callbacks_and_traverse(node); }


    protected:

//         /** Prevent unwanted copy construction.*/
//         UpdateVisitor(const UpdateVisitor&):osg::NodeVisitor() {}

        /** Prevent unwanted copy operator.*/
        UpdateVisitor& operator = (const UpdateVisitor&) { return *this; }

        inline void handle_callbacks(osg::StateSet* stateset)
        {
            if (stateset && stateset->requiresUpdateTraversal())
            {
                stateset->runUpdateCallbacks(this);
            }
        }

        inline void handle_callbacks_and_traverse(osg::Node& node)
        {
            handle_callbacks(node.getStateSet());

            osg::Callback* callback = node.getUpdateCallback();
            if (callback) callback->run(&node,this);
            else if (node.getNumChildrenRequiringUpdateTraversal()>0) traverse(node);
        }
};

}

#endif


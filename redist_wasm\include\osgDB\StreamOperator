#ifndef OSGDB_STREAMOPERATOR
#define OSGDB_STREAMOPERATOR

#include <iostream>
#include <sstream>
#include <osg/Referenced>
#include <osgDB/Export>
#include <osgDB/DataTypes>

namespace osgDB
{

// forward declare
class InputStream;
class OutputStream;

class OSGDB_EXPORT OutputIterator : public osg::Referenced
{
public:
    OutputIterator() : _out(0), _outputStream(0), _supportBinaryBrackets(false) {}
    virtual ~OutputIterator() {}

    void setStream( std::ostream* ostream ) { _out = ostream; }
    std::ostream* getStream() { return _out; }
    const std::ostream* getStream() const { return _out; }

    void setOutputStream( OutputStream* outputStream) { _outputStream = outputStream; }
    OutputStream* getOutputStream() { return _outputStream; }
    const OutputStream* getOutputStream() const { return _outputStream; }

    void setSupportBinaryBrackets( bool b ) { _supportBinaryBrackets = b; }
    bool getSupportBinaryBrackets() const { return _supportBinaryBrackets; }

    virtual bool isBinary() const = 0;

    virtual void writeBool( bool b ) = 0;
    virtual void writeChar( char c ) = 0;
    virtual void writeUChar( unsigned char c ) = 0;
    virtual void writeShort( short s ) = 0;
    virtual void writeUShort( unsigned short s ) = 0;
    virtual void writeInt( int i ) = 0;
    virtual void writeUInt( unsigned int i ) = 0;
    virtual void writeLong( long l ) = 0;
    virtual void writeULong( unsigned long l ) = 0;
    virtual void writeInt64( GLint64 ll ) = 0;
    virtual void writeUInt64( GLuint64 ull ) = 0;
    virtual void writeFloat( float f ) = 0;
    virtual void writeDouble( double d ) = 0;
    virtual void writeString( const std::string& s ) = 0;
    virtual void writeStream( std::ostream& (*fn)(std::ostream&) ) = 0;
    virtual void writeBase( std::ios_base& (*fn)(std::ios_base&) ) = 0;

    virtual void writeGLenum( const ObjectGLenum& value ) = 0;
    virtual void writeProperty( const ObjectProperty& prop ) = 0;
    virtual void writeMark( const ObjectMark& mark ) = 0;
    virtual void writeCharArray( const char* s, unsigned int size ) = 0;
    virtual void writeWrappedString( const std::string& str ) = 0;

    virtual void flush() { _out->flush(); }

protected:
    // Return true if the manipulator is std::endl
    bool isEndl( std::ostream& (*fn)(std::ostream&) )
    {
#if defined (__sun) || (defined _WIN32 && !defined OSG_LIBRARY_STATIC)
        // What a mess, but solaris does not like taking the address below
        // windows std::endl is a template with different adresses in different dll's
        std::stringstream ss;
        ss << fn;
        std::string s = ss.str();
        return !s.empty() && s[0] == '\n';
#else
        return fn==static_cast<std::ostream& (*)(std::ostream&)>(std::endl);
#endif
    }

    std::ostream* _out;
    osgDB::OutputStream* _outputStream;
    bool _supportBinaryBrackets;
};

class OSGDB_EXPORT InputIterator : public osg::Referenced
{
public:
    InputIterator() : _in(0), _inputStream(0), _byteSwap(0), _supportBinaryBrackets(false), _failed(false) {}
    virtual ~InputIterator() {}

    void setStream( std::istream* istream ) { _in = istream; }
    std::istream* getStream() { return _in; }
    const std::istream* getStream() const { return _in; }

    void setInputStream( InputStream* inputStream) { _inputStream = inputStream; }
    InputStream* getInputStream() { return _inputStream; }
    const InputStream* getInputStream() const { return _inputStream; }

    void setByteSwap(int byteSwap) { _byteSwap = byteSwap; }
    int getByteSwap() const { return _byteSwap; }

    void setSupportBinaryBrackets( bool b ) { _supportBinaryBrackets = b; }
    bool getSupportBinaryBrackets() const { return _supportBinaryBrackets; }

    void checkStream() const;
    bool isFailed() const { return _failed; }

    virtual bool isBinary() const = 0;

    virtual void readBool( bool& b ) = 0;
    virtual void readChar( char& c ) = 0;
    virtual void readSChar( signed char& c ) = 0;
    virtual void readUChar( unsigned char& c ) = 0;
    virtual void readShort( short& s ) = 0;
    virtual void readUShort( unsigned short& s ) = 0;
    virtual void readInt( int& i ) = 0;
    virtual void readUInt( unsigned int& i ) = 0;
    virtual void readLong( long& l ) = 0;
    virtual void readULong( unsigned long& l ) = 0;
    virtual void readFloat( float& f ) = 0;
    virtual void readDouble( double& d ) = 0;
    virtual void readString( std::string& s ) = 0;
    virtual void readStream( std::istream& (*fn)(std::istream&) ) = 0;
    virtual void readBase( std::ios_base& (*fn)(std::ios_base&) ) = 0;

    virtual void readGLenum( ObjectGLenum& value ) = 0;
    virtual void readProperty( ObjectProperty& prop ) = 0;
    virtual void readMark( ObjectMark& mark ) = 0;
    virtual void readCharArray( char* s, unsigned int size ) = 0;
    virtual void readWrappedString( std::string& str ) = 0;

    virtual bool matchString( const std::string& /*str*/ ) { return false; }
    virtual void advanceToCurrentEndBracket() {}

    void throwException( const std::string& msg );

    void readComponentArray( char* s, unsigned int numElements, unsigned int numComponentsPerElements, unsigned int componentSizeInBytes);

protected:
    std::istream*       _in;
    osgDB::InputStream* _inputStream;
    int                 _byteSwap;
    bool                _supportBinaryBrackets;
    mutable bool        _failed;
};

}

#endif

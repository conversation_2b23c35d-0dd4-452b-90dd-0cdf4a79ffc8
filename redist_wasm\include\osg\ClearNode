/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSG_CLEARNODE
#define OSG_CLEARNODE 1

#include <osg/Group>
#include <osg/Vec4>

namespace osg {

/** A Group node for clearing the color and depth buffers. Use setClearColor
  * to change the clear color, and setRequiresClear to disable/enable the call
  * clearing. You might want to disable clearing if you perform your clear by
  * drawing fullscreen geometry. If you do this, add child nodes to perform
  * such drawing. The default StateSet associated with this node places
  * children in render bin -1 to ensure that children are rendered prior to
  * the rest of the scene graph.
*/
class OSG_EXPORT ClearNode : public Group
{
    public :

        ClearNode();

        ClearNode(const ClearNode& cs, const CopyOp& copyop=CopyOp::SHALLOW_COPY):
            Group(cs,copyop),
            _requiresClear(cs._requiresClear),
            _clearColor(cs._clearColor),
            _clearMask(cs._clearMask) {}


        META_Node(osg, ClearNode);

        /** Enable/disable clearing via glClear. */
        inline void setRequiresClear(bool requiresClear) { _requiresClear = requiresClear; }

        /** Gets whether clearing is enabled or disabled. */
        inline bool getRequiresClear() const { return _requiresClear; }

        /** Sets the clear color. */
        inline void setClearColor(const Vec4& color) { _clearColor = color; }

        /** Returns the clear color. */
        inline const Vec4& getClearColor() const { return _clearColor; }

        /** Set the clear mask used in glClear(..).
          * Defaults to GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT. */
        inline void setClearMask(GLbitfield mask) { _clearMask = mask; }

        /** Get the clear mask.*/
        inline GLbitfield getClearMask() const { return _clearMask; }

    protected :

        virtual ~ClearNode() {}

        bool        _requiresClear;
        Vec4        _clearColor;
        GLbitfield  _clearMask;
};

}

#endif

/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef __SphericalManipulator_h__
#define __SphericalManipulator_h__

#include <osgGA/CameraManipulator>
#include <osg/Math>
#include <osg/Quat>

namespace osgGA
{

class OSGGA_EXPORT SphericalManipulator : public CameraManipulator
{
    public:
        SphericalManipulator();

        virtual const char* className() const { return "Spherical Manipulator"; }

        /** set the position of the matrix manipulator using a 4x4 Matrix.*/
        virtual void setByMatrix(const osg::Matrixd& matrix);

        /** set the position of the matrix manipulator using a 4x4 Matrix.*/
        virtual void setByInverseMatrix(const osg::Matrixd& matrix) { setByMatrix(osg::Matrixd::inverse(matrix)); }

        /** get the position of the manipulator as 4x4 Matrix.*/
        virtual osg::Matrixd getMatrix() const;

        /** get the position of the manipulator as a inverse matrix of the manipulator, typically used as a model view matrix.*/
        virtual osg::Matrixd getInverseMatrix() const;

        /** Get the FusionDistanceMode. Used by SceneView for setting up stereo convergence.*/
        virtual osgUtil::SceneView::FusionDistanceMode getFusionDistanceMode() const { return osgUtil::SceneView::USE_FUSION_DISTANCE_VALUE; }

        /** Get the FusionDistanceValue. Used by SceneView for setting up stereo convergence.*/
        virtual float getFusionDistanceValue() const { return _distance; }

        /** Attach a node to the manipulator.
        Automatically detaches previously attached node.
        setNode(NULL) detaches previously nodes.
        Is ignored by manipulators which do not require a reference model.*/
        virtual void setNode(osg::Node*);

        /** Return node if attached.*/
        virtual const osg::Node* getNode() const;

        /** Return node if attached.*/
        virtual osg::Node* getNode();

        /** Move the camera to the default position.
        May be ignored by manipulators if home functionality is not appropriate.*/
        virtual void home(const osgGA::GUIEventAdapter& ea,osgGA::GUIActionAdapter& us);
        virtual void home(double);

        /** Start/restart the manipulator.*/
        virtual void init(const osgGA::GUIEventAdapter& ea,osgGA::GUIActionAdapter& us);

        void zoomOn(const osg::BoundingSphere& bound);

        /** handle events, return true if handled, false otherwise.*/
        virtual bool handle(const osgGA::GUIEventAdapter& ea,osgGA::GUIActionAdapter& us);

        /** Compute the home position.*/
        virtual void computeHomePosition();

        void computeViewPosition(const osg::BoundingSphere& bound,double& scale,double& distance,osg::Vec3d& center);

        void setCenter(const osg::Vec3d& center) {_center=center;}
        const osg::Vec3d& getCenter() const {return _center;}

        bool setDistance(double distance);
        double getDistance() const { return _distance; }

        double getHomeDistance() const { return _homeDistance; }

        void setHeading(double azimuth) { _heading = azimuth; }
        double getHeading() const {return _heading;}

        void setElevation(double elevation) { _elevation = elevation; }
        double getElevtion() const {return _elevation;}


        /** get the minimum distance (as ratio) the eye point can be zoomed in */
        double getMinimumZoomScale() const { return _minimumZoomScale; }

        /** set the minimum distance (as ratio) the eye point can be zoomed in towards the
            center before the center is pushed forward.*/
        void setMinimumZoomScale(double minimumZoomScale) {_minimumZoomScale=minimumZoomScale;}


         /** set the mouse scroll wheel zoom delta.
           * Range -1.0 to ****,  -ve value inverts wheel direction and zero switches off scroll wheel. */
        void setScroolWheelZoomDelta(double zoomDelta) { _zoomDelta = zoomDelta; }

        /** get the mouse scroll wheel zoom delta. */
        double getScroolWheelZoomDelta() const { return _zoomDelta; }


        /** Get the keyboard and mouse usage of this manipulator.*/
        virtual void getUsage(osg::ApplicationUsage& usage) const;

        enum RotationMode
        {
                ELEVATION_HEADING=0,
                HEADING,
                ELEVATION,
                MAP
        };

        RotationMode getRotationMode() const {return _rotationMode;}
        void setRotationMode(RotationMode mode);

         /** Returns true if the camera can be thrown, false otherwise. This defaults to true. */
        bool getAllowThrow() const { return _allowThrow; }
        /** Set the 'allow throw' flag. Releasing the mouse button while moving the camera results in a throw. */
        void setAllowThrow(bool allowThrow) { _allowThrow = allowThrow; }

    protected:

        virtual ~SphericalManipulator();

        /** Reset the internal GUIEvent stack.*/
        void flushMouseEventStack();
        /** Add the current mouse GUIEvent to internal stack.*/
        void addMouseEvent(const osgGA::GUIEventAdapter& ea);

        /** For the give mouse movement calculate the movement of the camera.
        Return true is camera has moved and a redraw is required.*/
        bool calcMovement();

        /** Check the speed at which the mouse is moving.
        If speed is below a threshold then return false, otherwise return true.*/
        bool isMouseMoving();

        // Internal event stack comprising last two mouse events.
        osg::ref_ptr<const osgGA::GUIEventAdapter> _ga_t1;
        osg::ref_ptr<const osgGA::GUIEventAdapter> _ga_t0;

        osg::observer_ptr<osg::Node>       _node;

        double _modelScale;
        double _minimumZoomScale;

        bool _thrown;
        bool _allowThrow;

        /** The approximate amount of time it is currently taking to draw a frame.
        * This is used to compute the delta in translation/rotation during a thrown display update.
        * It allows us to match an delta in position/rotation independent of the rendering frame rate.
        */
        double _delta_frame_time;

        /** The time the last frame started.
        * Used when _rate_sensitive is true so that we can match display update rate to rotation/translation rate.
        */
        double _last_frame_time;

        RotationMode    _rotationMode;
        osg::Vec3d      _center;
        double          _distance;
        double          _heading;  // angle from x axis in xy plane
        double          _elevation;   // angle from xy plane, positive upwards towards the z axis
        double          _homeDistance;
        double          _zoomDelta;
};
}
#endif

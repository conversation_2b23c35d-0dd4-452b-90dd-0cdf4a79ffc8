/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSG_SHADERCOMPOSER
#define OSG_SHADERCOMPOSER 1

#include <osg/Object>
#include <osg/StateAttribute>
#include <osg/Program>

namespace osg {

// forward declare osg::State
class State;

typedef std::vector<osg::ShaderComponent*> ShaderComponents;

/// deprecated
class OSG_EXPORT ShaderComposer : public osg::Object
{
    public:

        ShaderComposer();
        ShaderComposer(const ShaderComposer& sa,const CopyOp& copyop=CopyOp::SHALLOW_COPY);
        META_Object(osg, ShaderComposer);

        virtual osg::Program* getOrCreateProgram(const ShaderComponents& shaderComponents);

        typedef std::vector< const osg::Shader* >  Shaders;
        virtual osg::Shader* composeMain(const Shaders& shaders);
        virtual void addShaderToProgram(Program* program, const Shaders& shaders);

        virtual void releaseGLObjects(osg::State* state) const;

    protected:

        virtual ~ShaderComposer();


        typedef std::map< ShaderComponents, ref_ptr<Program> > ProgramMap;
        ProgramMap _programMap;

        typedef std::map< Shaders, ref_ptr<Shader> > ShaderMainMap;
        ShaderMainMap _shaderMainMap;

};

}

#endif

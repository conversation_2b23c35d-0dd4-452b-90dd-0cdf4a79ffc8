/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2010 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSGTEXT_STYLE
#define OSGTEXT_STYLE 1

#include <osg/Object>
#include <osg/Vec2>
#include <osgText/Export>

#include <iosfwd>
#include <vector>

namespace osgText
{

class OSGTEXT_EXPORT Bevel : public osg::Object
{
    public:

        Bevel();
        Bevel(const Bevel& bevel, const osg::CopyOp& copyop=osg::CopyOp::SHALLOW_COPY);

        META_Object(osgText, Bevel)

        bool operator == (const Bevel& rhs) const
        {
            if (_smoothConcaveJunctions != rhs._smoothConcaveJunctions) return false;
            if (_thickness != rhs._thickness) return false;
            return _vertices==rhs._vertices;
        }

        void setSmoothConcaveJunctions(bool flag) { _smoothConcaveJunctions = flag; }
        bool getSmoothConcaveJunctions() const { return _smoothConcaveJunctions; }

        void setBevelThickness(float thickness) { _thickness = thickness; }
        float getBevelThickness() const { return _thickness; }

        void flatBevel(float width=0.25f);

        void roundedBevel(float width=0.5f, unsigned int numSteps=10);

        void roundedBevel2(float width=0.5f, unsigned int numSteps=10);

        typedef std::vector<osg::Vec2> Vertices;

        void setVertices(const Vertices& vertices) { _vertices = vertices; }
        Vertices& getVertices() { return _vertices; }
        const Vertices& getVertices() const { return _vertices; }

        void print(std::ostream& fout);

    protected:

        bool            _smoothConcaveJunctions;
        float           _thickness;
        Vertices        _vertices;
};


class OSGTEXT_EXPORT Style : public osg::Object
{
    public:

        Style();
        Style(const Style& style, const osg::CopyOp& copyop=osg::CopyOp::SHALLOW_COPY);

        META_Object(osgText, Style);

        /// default Layout implementation used if no other is specified on TextNode
        static osg::ref_ptr<Style>& getDefaultStyle();

        bool operator == (const Style& style) const;

        /// NULL is no bevel
        void setBevel(Bevel* bevel) { _bevel = bevel; }
        const Bevel* getBevel() const { return _bevel.get(); }


        /// 1 is the default width of the text
        void setWidthRatio(float widthRatio) { _widthRatio = widthRatio; }
        float getWidthRatio() const { return _widthRatio; }

        /// 0 is 2D text
        void setThicknessRatio(float thicknessRatio) { _thicknessRatio = thicknessRatio; }
        float getThicknessRatio() const { return _thicknessRatio; }

        /// 0 is off
        void setOutlineRatio(float outlineRatio) { _outlineRatio = outlineRatio; }
        float getOutlineRatio() const { return _outlineRatio; }

        /// 1.0 is default number of samples
        void setSampleDensity(float sd) { _sampleDensity = sd; }
        float getSampleDensity() const { return _sampleDensity; }

    protected:

        osg::ref_ptr<Bevel>     _bevel;

        float                   _widthRatio;
        float                   _thicknessRatio;
        float                   _outlineRatio;
        float                   _sampleDensity;
};

}

#endif

/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSGGA_VIEWLISTMANIPULATOR
#define OSGGA_VIEWLISTMANIPULATOR 1

#include <osgGA/CameraManipulator>
#include <osg/Quat>
#include <osg/CameraView>

namespace osgGA{

class OSGGA_EXPORT CameraViewSwitchManipulator : public CameraManipulator
{
    public:
        CameraViewSwitchManipulator() {}

        virtual const char* className() const { return "CameraViewSwitcher"; }

        /** set the position of the matrix manipulator using a 4x4 Matrix.*/
        virtual void setByMatrix(const osg::Matrixd& /*matrix*/) {}

        /** set the position of the matrix manipulator using a 4x4 Matrix.*/
        virtual void setByInverseMatrix(const osg::Matrixd& /*matrix*/) {}

        /** get the position of the manipulator as 4x4 Matrix.*/
        virtual osg::Matrixd getMatrix() const;

        /** get the position of the manipulator as a inverse matrix of the manipulator, typically used as a model view matrix.*/
        virtual osg::Matrixd getInverseMatrix() const;


        /** Attach a node to the manipulator.
            Automatically detaches previously attached node.
            setNode(NULL) detaches previously nodes.
            Is ignored by manipulators which do not require a reference model.*/
        virtual void setNode(osg::Node*);

        /** Return node if attached.*/
        virtual const osg::Node* getNode() const { return _node.get();}

        /** Return node if attached.*/
        virtual osg::Node* getNode() { return _node.get();}

        /** Start/restart the manipulator.*/
        virtual void init(const GUIEventAdapter& /*ea*/,GUIActionAdapter& /*aa*/) { _currentView = 0; }

        /** handle events, return true if handled, false otherwise.*/
        virtual bool handle(const GUIEventAdapter& ea,GUIActionAdapter& us);

        /** Get the keyboard and mouse usage of this manipulator.*/
        virtual void getUsage(osg::ApplicationUsage& usage) const;

       typedef std::vector< osg::ref_ptr<osg::CameraView> > CameraViewList;

    protected:

        virtual ~CameraViewSwitchManipulator() {}

       osg::ref_ptr<osg::Node>       _node;

       CameraViewList _cameraViews;
       unsigned int _currentView;
};

}

#endif


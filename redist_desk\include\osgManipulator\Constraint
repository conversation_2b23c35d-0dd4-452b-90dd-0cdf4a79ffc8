/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/
//osgManipulator - Copyright (C) 2007 Fugro-Jason B.V.

#ifndef OSGMANIPULATOR_CONSTRAINT
#define OSGMANIPULATOR_CONSTRAINT 1

#include <osgManipulator/Export>

#include <osg/observer_ptr>
#include <osg/Node>
#include <osg/Matrix>

namespace osgManipulator {

class MotionCommand;
class TranslateInLineCommand;
class TranslateInPlaneCommand;
class Scale1DCommand;
class Scale2DCommand;
class ScaleUniformCommand;
class Rotate3DCommand;


class DraggerCallback : virtual public osg::Object
{
    public:
        DraggerCallback():
            osg::Object(true) {}

        DraggerCallback(const DraggerCallback& org, const osg::CopyOp& copyop = osg::CopyOp::SHALLOW_COPY):
            osg::Object(org, copyop) {}

        META_Object(osgManipulator, DraggerCallback);

        /**
         * Receive motion commands. Returns true on success.
         */
        virtual bool receive(const MotionCommand&) { return false; }
        virtual bool receive(const TranslateInLineCommand& command)  { return receive((MotionCommand&)command); }
        virtual bool receive(const TranslateInPlaneCommand& command) { return receive((MotionCommand&)command); }
        virtual bool receive(const Scale1DCommand& command)          { return receive((MotionCommand&)command); }
        virtual bool receive(const Scale2DCommand& command)          { return receive((MotionCommand&)command); }
        virtual bool receive(const ScaleUniformCommand& command)     { return receive((MotionCommand&)command); }
        virtual bool receive(const Rotate3DCommand& command)         { return receive((MotionCommand&)command); }
};


class OSGMANIPULATOR_EXPORT Constraint : public osg::Referenced
{
    public:

        virtual bool constrain(MotionCommand&) const                   { return false; }
        virtual bool constrain(TranslateInLineCommand& command) const  { return constrain((MotionCommand&)command); }
        virtual bool constrain(TranslateInPlaneCommand& command) const { return constrain((MotionCommand&)command); }
        virtual bool constrain(Scale1DCommand& command) const          { return constrain((MotionCommand&)command); }
        virtual bool constrain(Scale2DCommand& command) const          { return constrain((MotionCommand&)command); }
        virtual bool constrain(ScaleUniformCommand& command) const     { return constrain((MotionCommand&)command); }
        virtual bool constrain(Rotate3DCommand& command) const         { return constrain((MotionCommand&)command); }

protected:

        Constraint() {}
        Constraint(osg::Node& refNode) : _refNode(&refNode) {}
        virtual ~Constraint() {}

        osg::Node&       getReferenceNode()       { return *_refNode; }
        const osg::Node& getReferenceNode() const { return *_refNode; }

        const osg::Matrix& getLocalToWorld() const { return _localToWorld; }
        const osg::Matrix& getWorldToLocal() const { return _worldToLocal; }

        void computeLocalToWorldAndWorldToLocal() const;

    private:

        osg::observer_ptr<osg::Node> _refNode;
        mutable osg::Matrix     _localToWorld;
        mutable osg::Matrix     _worldToLocal;
};

/**
 * Constraint to snap motion commands to a sugar cube grid.
 */
class OSGMANIPULATOR_EXPORT GridConstraint : public Constraint
{
    public:

        GridConstraint(osg::Node& refNode, const osg::Vec3d& origin, const osg::Vec3d& spacing);

        void setOrigin(const osg::Vec3d& origin)   { _origin = origin; }
        const osg::Vec3d& getOrigin() const  { return _origin; }

        void setSpacing(const osg::Vec3d& spacing) { _spacing = spacing; }
        const osg::Vec3d& getSpacing() const { return _spacing; }

        virtual bool constrain(TranslateInLineCommand& command) const;
        virtual bool constrain(TranslateInPlaneCommand& command) const;
        virtual bool constrain(Scale1DCommand& command) const;
        virtual bool constrain(Scale2DCommand& command) const;
        virtual bool constrain(ScaleUniformCommand& command) const;

    protected:

        virtual ~GridConstraint() {}

    private:

        osg::Vec3d _origin;
        osg::Vec3d _spacing;
        mutable osg::Matrix     _startMatrix;
        mutable osg::Matrix     _matrix;
};

}

#endif

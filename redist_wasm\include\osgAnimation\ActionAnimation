/*  -*-c++-*-
 *  Copyright (C) 2009 <PERSON><PERSON> <<EMAIL>>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSGANIMATION_ACTION_ANIMATION_H
#define OSGANIMATION_ACTION_ANIMATION_H

#include <osgAnimation/Action>
#include <osgAnimation/Export>


namespace osgAnimation {


    class OSGANIMATION_EXPORT ActionAnimation : public Action
    {
    public:
        META_Action(osgAnimation, ActionAnimation);
        ActionAnimation();
        ActionAnimation(const ActionAnimation& a, const osg::CopyOp& c);
        ActionAnimation(Animation* animation);
        void updateAnimation(unsigned int frame, int priority);
        Animation* getAnimation() { return _animation.get(); }

    protected:
        osg::ref_ptr<Animation> _animation;
    };

}

#endif

/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSGUTIL_TRANSFORMATTRIBUTEFUNCTOR
#define OSGUTIL_TRANSFORMATTRIBUTEFUNCTOR 1

#include <osg/Drawable>
#include <osg/Notify>

#include <osgUtil/Export>

namespace osgUtil {

/** Functor for transforming a drawable's vertex and normal attributes by specified matrix.
  * typically used for flattening transform down onto drawable leaves. */
class OSGUTIL_EXPORT TransformAttributeFunctor : public osg::Drawable::AttributeFunctor
{
    public:

        /** Construct a functor to transform a drawable's vertex and normal attributes by specified matrix.*/
        TransformAttributeFunctor(const osg::Matrix& m);

        virtual ~TransformAttributeFunctor();

        /** Do the work of transforming vertex and normal attributes. */
        virtual void apply(osg::Drawable::AttributeType type,unsigned int count,osg::Vec3* begin);
        virtual void apply(osg::Drawable::AttributeType type,unsigned int count,osg::Vec3d* begin);

        osg::Matrix _m;
        osg::Matrix _im;

};

}

#endif

/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSG_Scissor
#define OSG_Scissor 1

#include <osg/StateAttribute>

namespace osg {

/** Encapsulate OpenGL glScissor. */
class OSG_EXPORT Scissor : public StateAttribute
{
    public :
        Scissor();

        Scissor(int x,int y,int width,int height):
            _x(x),
            _y(y),
            _width(width),
            _height(height) {}


        /** Copy constructor using CopyOp to manage deep vs shallow copy. */
        Scissor(const Scissor& vp,const CopyOp& copyop=CopyOp::SHALLOW_COPY):
            StateAttribute(vp,copyop),
            _x(vp._x),
            _y(vp._y),
            _width(vp._width),
            _height(vp._height) {}

        META_StateAttribute(osg, Scissor, SCISSOR);

        /** Return -1 if *this < *rhs, 0 if *this==*rhs, 1 if *this>*rhs. */
        virtual int compare(const StateAttribute& sa) const
        {
            // check the types are equal and then create the rhs variable
            // used by the COMPARE_StateAttribute_Parameter macros below.
            COMPARE_StateAttribute_Types(Scissor,sa)

            // compare each parameter in turn against the rhs.
            COMPARE_StateAttribute_Parameter(_x)
            COMPARE_StateAttribute_Parameter(_y)
            COMPARE_StateAttribute_Parameter(_width)
            COMPARE_StateAttribute_Parameter(_height)

            return 0; // passed all the above comparison macros, must be equal.
        }


        virtual bool getModeUsage(StateAttribute::ModeUsage& usage) const
        {
            usage.usesMode(GL_SCISSOR_TEST);
            return true;
        }

        inline void setScissor(int x,int y,int width,int height)
        {
            _x = x;
            _y = y;
            _width = width;
            _height = height;
        }

        void getScissor(int& x,int& y,int& width,int& height) const
        {
            x = _x;
            y = _y;
            width = _width;
            height = _height;
        }

        inline int& x() { return _x; }
        inline int x() const { return _x; }

        inline int& y() { return _y; }
        inline int y() const { return _y; }

        inline int& width() { return _width; }
        inline int width() const { return _width; }

        inline int& height() { return _height; }
        inline int height() const { return _height; }

        virtual void apply(State& state) const;

    protected:

        virtual ~Scissor();

        int _x;
        int _y;
        int _width;
        int _height;

};

}

#endif

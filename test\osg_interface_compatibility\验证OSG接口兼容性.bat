@echo off
chcp 65001 >nul
echo.
echo ========================================
echo   OSG接口兼容性测试验证脚本
echo ========================================
echo.

echo 🔍 检查测试文件是否存在...
echo.

REM 检查桌面版测试文件
if exist "redist_desktop\osg_compatibility_test.exe" (
    echo ✅ 桌面版测试文件: redist_desktop\osg_compatibility_test.exe
) else (
    echo ❌ 桌面版测试文件缺失
    goto :error
)

REM 检查WebAssembly完整版测试文件
if exist "redist_wasm\osg_compatibility_test.html" (
    echo ✅ WebAssembly完整版: redist_wasm\osg_compatibility_test.html
) else (
    echo ❌ WebAssembly完整版测试文件缺失
    goto :error
)

REM 检查WebAssembly简化版测试文件
if exist "redist_wasm\osg_simple_test.html" (
    echo ✅ WebAssembly简化版: redist_wasm\osg_simple_test.html
) else (
    echo ❌ WebAssembly简化版测试文件缺失
    goto :error
)

echo.
echo 🚀 开始运行测试...
echo.

echo ==========================================
echo   1. 运行桌面版OSG接口兼容性测试
echo ==========================================
echo.

cd redist_desktop
echo 执行: osg_compatibility_test.exe --verbose
osg_compatibility_test.exe --verbose

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 桌面版测试完成
) else (
    echo.
    echo ❌ 桌面版测试失败，错误代码: %ERRORLEVEL%
)

cd ..
echo.

echo ==========================================
echo   2. 启动WebAssembly测试服务器
echo ==========================================
echo.

echo 启动HTTP服务器用于WebAssembly测试...
echo 服务器地址: http://localhost:8080
echo.
echo 📋 测试页面:
echo   - 完整版: http://localhost:8080/osg_compatibility_test.html
echo   - 简化版: http://localhost:8080/osg_simple_test.html
echo.
echo ⚠️  重要提醒:
echo   - 如需访问外部资源，请设置浏览器代理为: 127.0.0.1:10809
echo   - 推荐使用Chrome/Firefox/Edge最新版本
echo   - 确保浏览器启用WebGL 2.0支持
echo.

cd redist_wasm

REM 检查Python是否可用
python --version >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo 使用Python启动HTTP服务器...
    echo 按 Ctrl+C 停止服务器
    echo.
    python -m http.server 8080
) else (
    echo Python未找到，请手动启动HTTP服务器
    echo 建议命令: python -m http.server 8080
)

cd ..
goto :end

:error
echo.
echo ❌ 测试文件检查失败，请确保已正确编译所有测试版本
echo.
echo 📋 编译指南:
echo   桌面版: 在build_desk目录运行 cmake .. && ninja
echo   WebAssembly版: 在build_wasm目录运行 emcmake cmake .. && ninja
echo.
pause
exit /b 1

:end
echo.
echo ========================================
echo   OSG接口兼容性测试验证完成
echo ========================================
echo.
echo 📊 测试结果总结:
echo   ✅ 桌面版OSG接口测试: 已运行
echo   🌐 WebAssembly版本: 服务器已启动
echo   📚 详细报告: OSG接口兼容性测试修复总结报告.md
echo.
echo 🎯 下一步:
echo   1. 在浏览器中访问WebAssembly测试页面
echo   2. 验证所有OSG接口功能正常
echo   3. 查看详细的测试报告和文档
echo.
pause

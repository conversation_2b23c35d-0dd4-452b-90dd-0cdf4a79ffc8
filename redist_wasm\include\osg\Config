/* -*-c++-*- OpenSceneGraph - Copyright (C) 2008-2009 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

/****************************************************************************
 * THIS FILE IS AUTOGENERATED BY CMAKE. DO NOT EDIT!
 ****************************************************************************/

/* Changes to the configuration reflected here can be made with ccmake on
 * unix or with cmake-gui on windows. Alternatively you can use cmake's -D
 * or -P switches to set some configuration values at cmake configuration time.
 */

#ifndef OSG_CONFIG
#define OSG_CONFIG 1

/* #undef OSG_NOTIFY_DISABLED */
/* #undef OSG_USE_FLOAT_MATRIX */
/* #undef OSG_USE_FLOAT_PLANE */
#define OSG_USE_FLOAT_BOUNDINGSPHERE
#define OSG_USE_FLOAT_BOUNDINGBOX
/* #undef OSG_USE_FLOAT_QUAT */
#define OSG_USE_REF_PTR_IMPLICIT_OUTPUT_CONVERSION
/* #undef OSG_USE_REF_PTR_SAFE_DEREFERENCE */
/* #undef OSG_USE_UTF8_FILENAME */
#define OSG_DISABLE_MSVC_WARNINGS
#define OSG_PROVIDE_READFILE
#define OSG_USE_DEPRECATED_API
#define OSG_ENVVAR_SUPPORTED
/* #undef OSG_WINDOWING_SYSTEM_CARBON */
#define OSG_WINDOWING_SYSTEM_NONE

#endif

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OSG接口兼容性测试 - WebAssembly简化版</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
        }
        
        .info-panel {
            background-color: #e8f4fd;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        
        .warning-panel {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        
        .success-panel {
            background-color: #d4edda;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        
        .error-panel {
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        
        .console-output {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.4;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin: 20px 0;
        }
        
        .controls {
            text-align: center;
            margin: 30px 0;
        }
        
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 0 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #2980b9;
        }
        
        button:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }
        
        .status {
            text-align: center;
            font-size: 18px;
            margin: 20px 0;
            font-weight: bold;
        }
        
        .loading {
            color: #f39c12;
        }
        
        .ready {
            color: #27ae60;
        }
        
        .error {
            color: #e74c3c;
        }
        
        #canvas {
            display: none;
        }
        
        .test-results {
            margin-top: 30px;
        }
        
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            background-color: #f8f9fa;
        }
        
        .test-pass {
            border-left: 4px solid #28a745;
        }
        
        .test-fail {
            border-left: 4px solid #dc3545;
        }
        
        .test-skip {
            border-left: 4px solid #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔬 OSG接口兼容性测试 - WebAssembly简化版</h1>
        
        <div class="info-panel">
            <h3>📋 测试说明</h3>
            <p>这是一个简化版的OSG接口兼容性测试，专门为WebAssembly环境设计。</p>
            <p>测试内容包括：基础C++功能、内存分配、WebGL上下文创建等。</p>
        </div>
        
        <div class="warning-panel">
            <h3>⚠️ 重要提醒</h3>
            <p>如果需要访问外部资源（如地图瓦片），请确保浏览器代理设置为：<strong>127.0.0.1:10809</strong></p>
        </div>
        
        <div class="status loading" id="status">🔄 正在加载WebAssembly模块...</div>
        
        <div class="controls">
            <button id="runTests" onclick="runCompatibilityTests()" disabled>🚀 运行兼容性测试</button>
            <button id="clearOutput" onclick="clearConsole()">🗑️ 清空输出</button>
        </div>
        
        <div class="console-output" id="output">等待WebAssembly模块加载完成...</div>
        
        <canvas id="canvas" width="800" height="600"></canvas>
    </div>

    <script type='text/javascript'>
        let outputElement = document.getElementById('output');
        let statusElement = document.getElementById('status');
        let runButton = document.getElementById('runTests');
        
        // 重定向console输出到页面
        function addOutput(text, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const line = `[${timestamp}] ${text}\n`;
            outputElement.textContent += line;
            outputElement.scrollTop = outputElement.scrollHeight;
            
            // 根据输出内容更新状态
            if (text.includes('All tests passed')) {
                statusElement.textContent = '✅ 所有测试通过！';
                statusElement.className = 'status ready';
            } else if (text.includes('Some tests failed')) {
                statusElement.textContent = '❌ 部分测试失败';
                statusElement.className = 'status error';
            }
        }
        
        // 重写console方法
        const originalLog = console.log;
        const originalError = console.error;
        
        console.log = function(...args) {
            addOutput(args.join(' '), 'info');
            originalLog.apply(console, args);
        };
        
        console.error = function(...args) {
            addOutput('ERROR: ' + args.join(' '), 'error');
            originalError.apply(console, args);
        };
        
        function clearConsole() {
            outputElement.textContent = '';
            addOutput('控制台已清空');
        }
        
        function runCompatibilityTests() {
            addOutput('开始运行OSG接口兼容性测试...');
            runButton.disabled = true;
            
            try {
                // 调用WebAssembly中的主函数
                Module.ccall('main', 'number', [], []);
            } catch (error) {
                addOutput('测试执行出错: ' + error.message, 'error');
                console.error('Test execution error:', error);
            } finally {
                runButton.disabled = false;
            }
        }
        
        // WebAssembly模块配置
        var Module = {
            preRun: [],
            postRun: [],
            print: function(text) {
                addOutput(text);
            },
            printErr: function(text) {
                addOutput('STDERR: ' + text, 'error');
            },
            canvas: (function() {
                var canvas = document.getElementById('canvas');
                canvas.addEventListener("webglcontextlost", function(e) {
                    alert('WebGL context lost. You will need to reload the page.');
                    e.preventDefault();
                }, false);
                return canvas;
            })(),
            setStatus: function(text) {
                if (!Module.setStatus.last) Module.setStatus.last = { time: Date.now(), text: '' };
                if (text === Module.setStatus.last.text) return;
                
                var m = text.match(/([^(]+)\((\d+(\.\d+)?)\/(\d+)\)/);
                var now = Date.now();
                
                if (m && now - Module.setStatus.last.time < 30) return;
                Module.setStatus.last.time = now;
                Module.setStatus.last.text = text;
                
                if (text) {
                    statusElement.textContent = '🔄 ' + text;
                    statusElement.className = 'status loading';
                } else {
                    statusElement.textContent = '✅ WebAssembly模块加载完成';
                    statusElement.className = 'status ready';
                    runButton.disabled = false;
                    addOutput('WebAssembly模块加载完成，可以开始测试');
                }
            },
            totalDependencies: 0,
            monitorRunDependencies: function(left) {
                this.totalDependencies = Math.max(this.totalDependencies, left);
                Module.setStatus(left ? 'Preparing... (' + (this.totalDependencies-left) + '/' + this.totalDependencies + ')' : 'All downloads complete.');
            }
        };
        
        Module.setStatus('正在下载WebAssembly模块...');
        
        window.onerror = function() {
            Module.setStatus('发生异常，请查看控制台');
            statusElement.className = 'status error';
        };
    </script>
    
    {{{ SCRIPT }}}
</body>
</html>

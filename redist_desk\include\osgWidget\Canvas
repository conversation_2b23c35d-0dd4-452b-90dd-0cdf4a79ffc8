/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2008 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

// Code by: <PERSON> (cubicool) 2007-2008

#ifndef OSGWIDGET_CANVAS
#define OSGWIDGET_CANVAS

#include <osgWidget/Window>

namespace osgWidget {

class OSGWIDGET_EXPORT Canvas: public Window
{
    public:

        META_Object(osgWidget, Canvas);

        Canvas (const std::string& = "");
        Canvas (const Canvas&, const osg::CopyOp&);

        //! Adds a Widget at the given XY coordinate.
        virtual bool addWidget(Widget*, point_type, point_type);

    protected:
        virtual void _resizeImplementation(point_type, point_type);

};

}

#endif

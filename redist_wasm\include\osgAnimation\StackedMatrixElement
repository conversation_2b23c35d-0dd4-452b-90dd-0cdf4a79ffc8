/*  -*-c++-*-
 *  Copyright (C) 2009 <PERSON><PERSON> <<EMAIL>>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
 */


#ifndef OSGANIMATION_STACKED_MATRIX_ELEMENT
#define OSGANIMATION_STACKED_MATRIX_ELEMENT 1

#include <osg/Object>
#include <osgAnimation/Export>
#include <osgAnimation/StackedTransformElement>
#include <osgAnimation/Target>

namespace osgAnimation
{

    class OSGANIMATION_EXPORT StackedMatrixElement : public StackedTransformElement
    {
    public:
        META_Object(osgAnimation, StackedMatrixElement);

        StackedMatrixElement();
        StackedMatrixElement(const StackedMatrixElement&, const osg::CopyOp&);
        StackedMatrixElement(const std::string& name, const osg::Matrix& matrix);
        StackedMatrixElement(const osg::Matrix& matrix);

        void applyToMatrix(osg::Matrix& matrix) const { matrix = _matrix * matrix; }
        osg::Matrix getAsMatrix() const { return _matrix; }
        const osg::Matrix& getMatrix() const { return _matrix;}
        void setMatrix(const osg::Matrix& matrix) { _matrix = matrix;}
        bool isIdentity() const { return _matrix.isIdentity(); }
        void update(float t = 0.0);
        virtual Target* getOrCreateTarget();
        virtual Target* getTarget() {return _target.get();}
        virtual const Target* getTarget() const {return _target.get();}

    protected:
        osg::Matrix _matrix;
        osg::ref_ptr<MatrixTarget> _target;
    };

}

#endif

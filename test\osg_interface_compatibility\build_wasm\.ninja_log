# ninja log v5
13	5882	7741737380142462	CMakeFiles/osg_compatibility_test.dir/main.cpp.o	6599d29fb86b06d9
0	6371	7741737385492068	CMakeFiles/osg_compatibility_test.dir/OSGInterfaceTestFramework.cpp.o	9f2219c9296d6bc3
9	6518	7741737386903969	CMakeFiles/osg_compatibility_test.dir/CoreRenderingTests.cpp.o	a8b8f6c1038664cf
12	15552	7741752888283123	osg_compatibility_test.html	ce26c0364557b6eb
2	1903	7741763383772085	build.ninja	772ed51aef24264b
19	5221	7741763434945116	CMakeFiles/osg_compatibility_test.dir/main.cpp.o	6599d29fb86b06d9
8	5560	7741763439113496	CMakeFiles/osg_compatibility_test.dir/OSGInterfaceTestFramework.cpp.o	9f2219c9296d6bc3
0	29	0	clean	72b299ace68a50e
24	6290	7741772188812982	CMakeFiles/osg_compatibility_test.dir/main.cpp.o	6599d29fb86b06d9
18	6898	7741772194768960	CMakeFiles/osg_compatibility_test.dir/OSGInterfaceTestFramework.cpp.o	9f2219c9296d6bc3
11	5032	7741772903879176	CMakeFiles/osg_compatibility_test.dir/CoreRenderingTests.cpp.o	a8b8f6c1038664cf
5038	21794	7741773071044302	osg_compatibility_test.html	9a1e0544047aefaa
27	5374	7741777158472121	CMakeFiles/osg_compatibility_test.dir/main.cpp.o	6599d29fb86b06d9
12	5857	7741777163359857	CMakeFiles/osg_compatibility_test.dir/OSGInterfaceTestFramework.cpp.o	9f2219c9296d6bc3
20	6097	7741777165098193	CMakeFiles/osg_compatibility_test.dir/CoreRenderingTests.cpp.o	a8b8f6c1038664cf
6098	23165	7741777336285685	osg_compatibility_test.html	9a1e0544047aefaa
21	5388	7741780608732911	CMakeFiles/osg_compatibility_test.dir/main.cpp.o	6599d29fb86b06d9
9	5712	7741780614092502	CMakeFiles/osg_compatibility_test.dir/CoreRenderingTests.cpp.o	a8b8f6c1038664cf
18	4767	7741781999000383	CMakeFiles/osg_compatibility_test.dir/main.cpp.o	6599d29fb86b06d9
12	5308	7741782004359951	CMakeFiles/osg_compatibility_test.dir/CoreRenderingTests.cpp.o	a8b8f6c1038664cf

/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2004 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSG_LIGHTSOURCE
#define OSG_LIGHTSOURCE 1

#include <osg/NodeVisitor>
#include <osg/Light>
#include <osg/Group>

namespace osg {

/** Leaf Node for defining a light in the scene. */
class OSG_EXPORT LightSource : public Group
{
    public:

        LightSource();

        /** Copy constructor using CopyOp to manage deep vs shallow copy. */
        LightSource(const LightSource& ls,
                    const CopyOp& copyop=CopyOp::SHALLOW_COPY):
            Group(ls,copyop),
            _value(ls._value),
            _light(dynamic_cast<osg::Light*>(copyop(ls._light.get()))),
            _referenceFrame(ls._referenceFrame) {}

        META_Node(osg, LightSource);

        enum ReferenceFrame
        {
            RELATIVE_RF,
            ABSOLUTE_RF
        };

        /** Set the light sources's ReferenceFrame, either to be relative to its
          * parent reference frame, or relative to an absolute coordinate
          * frame. RELATIVE_RF is the default.
          * Note: setting the ReferenceFrame to be ABSOLUTE_RF will
          * also set the CullingActive flag on the light source, and hence all
          * of its parents, to false, thereby disabling culling of it and
          * all its parents.  This is necessary to prevent inappropriate
          * culling, but may impact cull times if the absolute light source is
          * deep in the scene graph.  It is therefore recommended to only use
          * absolute light source at the top of the scene.
        */
        void setReferenceFrame(ReferenceFrame rf);

        ReferenceFrame getReferenceFrame() const { return _referenceFrame; }

        /** Set the attached light. */
        void setLight(Light* light);

        /** Get the attached light. */
        inline Light* getLight() { return _light.get(); }

        /** Get the const attached light. */
        inline const Light* getLight() const { return _light.get(); }

        /** Set the GLModes on StateSet associated with the LightSource. */
        void setStateSetModes(StateSet&,StateAttribute::GLModeValue) const;

        /** Set up the local StateSet. */
        void setLocalStateSetModes(StateAttribute::GLModeValue value = StateAttribute::ON);

        /** Set whether to use a mutex to ensure ref() and unref() are thread safe.*/
        virtual void setThreadSafeRefUnref(bool threadSafe);

        virtual BoundingSphere computeBound() const;

    protected:

        virtual ~LightSource();

        StateAttribute::GLModeValue     _value;
        ref_ptr<Light>                  _light;

        ReferenceFrame                  _referenceFrame;
};

}

#endif

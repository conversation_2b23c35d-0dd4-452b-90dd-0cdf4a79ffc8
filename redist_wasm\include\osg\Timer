/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSG_TIMER
#define OSG_TIMER 1

#include <osg/Export>

namespace osg {

#if defined(_MSC_VER) && !defined(__clang__)
    typedef unsigned __int64 Timer_t;
#else
    typedef unsigned long long Timer_t;
#endif

/** Timer class is used for measuring elapsed time or time between two points. */
class OSG_EXPORT Timer {

    public:

        Timer();
        ~Timer() {}

        static Timer* instance();

        /** Get the timers tick value.*/
        Timer_t tick() const;

        /** Set the start.*/
        void setStartTick() { _startTick = tick(); }
        void setStartTick(Timer_t t) { _startTick = t; }
        Timer_t getStartTick() const { return _startTick; }


        /** Get elapsed time in seconds.*/
        inline double time_s() const { return delta_s(_startTick, tick()); }

        /** Get elapsed time in milliseconds.*/
        inline double time_m() const { return delta_m(_startTick, tick()); }

        /** Get elapsed time in microseconds.*/
        inline double time_u() const { return delta_u(_startTick, tick()); }

        /** Get elapsed time in nanoseconds.*/
        inline double time_n() const { return delta_n(_startTick, tick()); }

        /** Get the time in seconds between timer ticks t1 and t2.*/
        inline double delta_s( Timer_t t1, Timer_t t2 ) const { return (t2>t1) ? (double)(t2 - t1)*_secsPerTick : -(double)(t1 - t2)*_secsPerTick; }

        /** Get the time in milliseconds between timer ticks t1 and t2.*/
        inline double delta_m( Timer_t t1, Timer_t t2 ) const { return delta_s(t1,t2)*1e3; }

        /** Get the time in microseconds between timer ticks t1 and t2.*/
        inline double delta_u( Timer_t t1, Timer_t t2 ) const { return delta_s(t1,t2)*1e6; }

        /** Get the time in nanoseconds between timer ticks t1 and t2.*/
        inline double delta_n( Timer_t t1, Timer_t t2 ) const { return delta_s(t1,t2)*1e9; }

        /** Get the number of seconds per tick. */
        inline double getSecondsPerTick() const { return _secsPerTick; }

    protected :

        Timer_t _startTick;
        double  _secsPerTick;
};

/** Helper class for timing sections of code. */
class ElapsedTime
{
    public:
        inline ElapsedTime(double* elapsedTime, osg::Timer* timer = 0):
            _time(elapsedTime)
        {
            init(timer);
        }

        inline ElapsedTime(osg::Timer* timer = 0):
            _time(0)
        {
            init(timer);
        }

        inline ~ElapsedTime()
        {
            finish();
        }

        inline void reset()
        {
            _startTick = _timer->tick();
        }

        /** elapsed time in seconds. */
        inline double elapsedTime() const
        {
            return _timer->delta_s(_startTick, _timer->tick());
        }

        /** elapsed time in milliseconds. */
        inline double elapsedTime_m() const
        {
            return _timer->delta_m(_startTick, _timer->tick());
        }

        /** elapsed time in microseconds. */
        inline double elapsedTime_u() const
        {
            return _timer->delta_u(_startTick, _timer->tick());
        }

        /** elapsed time in nanoseconds. */
        inline double elapsedTime_n() const
        {
            return _timer->delta_n(_startTick, _timer->tick());
        }

        inline void finish()
        {
            Timer_t endTick = _timer->tick();
            if (_time) *_time += _timer->delta_s(_startTick, endTick);
            _startTick = endTick;
        }

    protected:

        inline void init(osg::Timer* timer)
        {
            if (timer) _timer = timer;
            else _timer = osg::Timer::instance();

            _startTick = _timer->tick();
        }

        double* _time;
        Timer*  _timer;
        Timer_t _startTick;

};

}
#endif

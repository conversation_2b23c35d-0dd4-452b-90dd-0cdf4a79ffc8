/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/
//osgParticle - Copyright (C) 2002 <PERSON>

#ifndef OSGPARTICLE_ACCELOPERATOR
#define OSGPARTICLE_ACCELOPERATOR 1

#include <osgParticle/ModularProgram>
#include <osgParticle/Operator>
#include <osgParticle/Particle>

#include <osg/CopyOp>
#include <osg/Object>
#include <osg/Vec3>

namespace osgParticle
{

    /**    An operator class that applies a constant acceleration to the particles.
    */
    class AccelOperator: public Operator {
    public:
        inline AccelOperator();
        inline AccelOperator(const AccelOperator& copy, const osg::CopyOp& copyop = osg::CopyOp::SHALLOW_COPY);

        META_Object(osgParticle, AccelOperator);

        /// Get the acceleration vector.
        inline const osg::Vec3& getAcceleration() const;

        /// Set the acceleration vector.
        inline void setAcceleration(const osg::Vec3& v);

        /** Quickly set the acceleration vector to the gravity on earth (0, 0, -9.81).
            The acceleration will be multiplied by the <CODE>scale</CODE> parameter.
        */
        inline void setToGravity(float scale = 1);

        /// Apply the acceleration to a particle. Do not call this method manually.
        inline void operate(Particle* P, double dt);

        /// Perform some initializations. Do not call this method manually.
        inline void beginOperate(Program *prg);

    protected:
        virtual ~AccelOperator() {}
        AccelOperator &operator=(const AccelOperator &) { return *this; }

    private:
        osg::Vec3 _accel;
        osg::Vec3 _xf_accel;
    };

    // INLINE FUNCTIONS

    inline AccelOperator::AccelOperator()
    : Operator(), _accel(0, 0, 0)
    {
    }

    inline AccelOperator::AccelOperator(const AccelOperator& copy, const osg::CopyOp& copyop)
    : Operator(copy, copyop), _accel(copy._accel)
    {
    }

    inline const osg::Vec3& AccelOperator::getAcceleration() const
    {
        return _accel;
    }

    inline void AccelOperator::setAcceleration(const osg::Vec3& v)
    {
        _accel = v;
    }

    inline void AccelOperator::setToGravity(float scale)
    {
        _accel.set(0, 0, -9.80665f * scale);
    }

    inline void AccelOperator::operate(Particle* P, double dt)
    {
        P->addVelocity(_xf_accel * dt);
    }

    inline void AccelOperator::beginOperate(Program *prg)
    {
        if (prg->getReferenceFrame() == ModularProgram::RELATIVE_RF) {
            _xf_accel = prg->rotateLocalToWorld(_accel);
        } else {
            _xf_accel = _accel;
        }
    }

}


#endif

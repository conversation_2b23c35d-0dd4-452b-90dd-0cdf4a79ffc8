/*  -*-c++-*-
 *  Copyright (C) 2009 <PERSON><PERSON> <<EMAIL>>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
 */


#ifndef OSGANIMATION_STACKED_TRANSLATE_ELEMENT
#define OSGANIMATION_STACKED_TRANSLATE_ELEMENT 1

#include <osgAnimation/Export>
#include <osgAnimation/StackedTransformElement>
#include <osgAnimation/Target>

namespace osgAnimation
{

    class OSGANIMATION_EXPORT StackedTranslateElement : public StackedTransformElement
    {
    public:
        META_Object(osgAnimation, StackedTranslateElement);

        StackedTranslateElement();
        StackedTranslateElement(const StackedTranslateElement&, const osg::CopyOp&);
        StackedTranslateElement(const std::string&, const osg::Vec3& translate = osg::Vec3(0,0,0));
        StackedTranslateElement(const osg::Vec3& translate);

        void applyToMatrix(osg::Matrix& matrix) const;
        osg::Matrix getAsMatrix() const;
        bool isIdentity() const;
        void update(float t = 0.0);

        const osg::Vec3& getTranslate() const;
        void setTranslate(const osg::Vec3& );
        virtual Target* getOrCreateTarget();
        virtual Target* getTarget();
        virtual const Target* getTarget() const;

    protected:
        osg::Vec3 _translate;
        osg::ref_ptr<Vec3Target> _target;
    };

}

#endif

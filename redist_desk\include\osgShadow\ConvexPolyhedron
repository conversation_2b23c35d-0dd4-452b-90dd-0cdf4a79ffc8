/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
 *
 * ViewDependentShadow codes Copyright (C) 2008 W<PERSON><PERSON><PERSON><PERSON>
 * Thanks to to my company http://www.ai.com.pl for allowing me free this work.
*/

#ifndef OSGSHADOW_CONVEXPOLYHEDRON
#define OSGSHADOW_CONVEXPOLYHEDRON 1

#include <osg/Geometry>
#include <osg/Polytope>
#include <osgShadow/Export>

////////////////////////////////////////////////////////////////////////////////
// Class based on CustomPolytope defined and used in osgSim::OverlayNode.cpp.
// Honors should go to Robert Osfield for writing such useful piece of code.
// First incarnations of my ConvexPolyhedron were derived from CustomPolytope.
// Later I made a number of modifications aimed at improving convex hull
// precision of intersection & extrusion operations and ended up with code
// so mixed that I decided to rewrite it as separate class.
////////////////////////////////////////////////////////////////////////////////

namespace osgShadow {

class OSGSHADOW_EXPORT ConvexPolyhedron
{
    public:
        typedef std::vector<osg::Vec3d> Vertices;

        static const osg::Matrix & defaultMatrix;

        struct Face
        {
            std::string name;
            osg::Plane  plane;
            Vertices    vertices;
        };

        typedef std::list<Face> Faces;
        Faces _faces;

        ConvexPolyhedron( void ) { }

        ConvexPolyhedron( const osg::Matrix& matrix, const osg::Matrix& inverse,
            const osg::BoundingBox& bb = osg::BoundingBox(-1,-1,-1,1,1,1));

        Face& createFace() { _faces.push_back(Face()); return _faces.back(); }
        void clear()       { _faces.clear(); }


        void setToUnitFrustum(bool withNear=true, bool withFar=true);
        void setToBoundingBox(const osg::BoundingBox& bb);
        void transform(const osg::Matrix& matrix, const osg::Matrix& inverse);
        void transformClip(const osg::Matrix& matrix, const osg::Matrix& inverse);


        bool mergeFaces
            ( const Face & face0, const Face & face1, Face & face );

        void mergeCoplanarFaces( const double & plane_normal_dot_tolerance = 0.0,
            const double & plane_distance_tolerance = 0.0 );

        void removeDuplicateVertices( void );


        static int pointsColinear
            ( const osg::Vec3d & va, const osg::Vec3d & vb, const osg::Vec3d & vc,
            const double & edge_normal_dot_tolerance = 0.0,
            const double & null_edge_length_tolerance = 0.0 );

        static int isFacePolygonConvex( Face & face, bool ignoreCollinearVertices = true );

        bool checkCoherency
            ( bool checkForNonConvexPolys = false, const char * errorPrefix = NULL );


        void cut(const osg::Polytope& polytope);

        void cut(const ConvexPolyhedron& polytope);

        void cut(const osg::Plane& plane, const std::string& name=std::string());

        void extrude( const osg::Vec3d & offset );

        void translate( const osg::Vec3d & offset );


        void getPolytope(osg::Polytope& polytope) const;
        void getPoints(Vertices& vertices) const;
        osg::BoundingBox computeBoundingBox( const osg::Matrix & m = osgShadow::ConvexPolyhedron::defaultMatrix ) const;

        osg::Geometry* buildGeometry( const osg::Vec4d& colorOutline,
            const osg::Vec4d& colorInside,
            osg::Geometry* useGeometry = NULL ) const;


        bool dumpGeometry( const Face * face = NULL,
            const osg::Plane * plane = NULL,
            ConvexPolyhedron * basehull = NULL,
            const char * filename = "convexpolyhedron.osg",
            const osg::Vec4d& colorOutline = osg::Vec4( 0,1,0,0.5 ),
            const osg::Vec4d& colorInside  = osg::Vec4( 0,1,0,0.25 ),
            const osg::Vec4d& faceColorOutline = osg::Vec4( 0,0,1,0.5 ),
            const osg::Vec4d& faceColorInside  = osg::Vec4( 0,0,1,0.25 ),
            const osg::Vec4d& planeColorOutline = osg::Vec4( 1,0,0,0.5 ),
            const osg::Vec4d& planeColorInside  = osg::Vec4( 1,0,0,0.25 ),
            const osg::Vec4d& baseColorOutline = osg::Vec4( 0,0,0,0.5 ),
            const osg::Vec4d& baseColorInside  = osg::Vec4( 0,0,0,0.25 ) ) const;
};

} // namespace osgShadow

#endif

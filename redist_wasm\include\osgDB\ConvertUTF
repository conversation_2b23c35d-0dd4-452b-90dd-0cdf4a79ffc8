/* -*-c++-*- OpenSceneGraph - Copyright (C) 2008 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSGDB_CONVERTUTF
#define OSGDB_CONVERTUTF 1

#include <osg/Config>
#include <osgDB/Export>

#include <string>

#if defined(__CYGWIN__) || defined(__ANDROID__)
namespace std
{
typedef basic_string<wchar_t> wstring;
}
#endif

namespace osgDB
{

extern OSGDB_EXPORT std::string convertUTF16toUTF8(const wchar_t* source, unsigned sourceLength);
extern OSGDB_EXPORT std::wstring convertUTF8toUTF16(const char* source, unsigned sourceLength);

extern OSGDB_EXPORT std::string convertUTF16toUTF8(const std::wstring& s);
extern OSGDB_EXPORT std::string convertUTF16toUTF8(const wchar_t* s);

extern OSGDB_EXPORT std::wstring convertUTF8toUTF16(const std::string& s);
extern OSGDB_EXPORT std::wstring convertUTF8toUTF16(const char* s);

extern OSGDB_EXPORT std::string convertStringFromCurrentCodePageToUTF8(const char* source, unsigned sourceLength);
extern OSGDB_EXPORT std::string convertStringFromUTF8toCurrentCodePage(const char* source, unsigned sourceLength);

extern OSGDB_EXPORT std::string convertStringFromCurrentCodePageToUTF8(const std::string& s);
extern OSGDB_EXPORT std::string convertStringFromCurrentCodePageToUTF8(const char* s);

extern OSGDB_EXPORT std::string convertStringFromUTF8toCurrentCodePage(const std::string& s);
extern OSGDB_EXPORT std::string convertStringFromUTF8toCurrentCodePage(const char* s);

}

#endif

/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSGTEXT_KERNINGTYPE
#define OSGTEXT_KERNINGTYPE

namespace osgText
{

typedef std::pair< unsigned int, unsigned int > FontResolution;

enum KerningType
{
    KERNING_DEFAULT,    //default locked to integer kerning values
    KERNING_UNFITTED,   //use floating point value for kerning
    KERNING_NONE        //no kerning
};

}
#endif /*OSGTEXT_KERNINGTYPE*/

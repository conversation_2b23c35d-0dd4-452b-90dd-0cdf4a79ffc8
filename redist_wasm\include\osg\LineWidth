/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2004 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSG_LINEWIDTH
#define OSG_LINEWIDTH 1

#include <osg/StateAttribute>

namespace osg {

/** LineWidth - encapsulates the OpenGL glLineWidth for setting the width of lines in pixels. */
class OSG_EXPORT LineWidth : public StateAttribute
{
    public :

        LineWidth(float width=1.0f);

        /** Copy constructor using CopyOp to manage deep vs shallow copy. */
        LineWidth(const LineWidth& lw,const CopyOp& copyop=CopyOp::SHALLOW_COPY) :
            StateAttribute(lw,copyop),
            _width(lw._width) {}

        META_StateAttribute(osg, LineWidth, LINEWIDTH);

        /** return -1 if *this < *rhs, 0 if *this==*rhs, 1 if *this>*rhs. */
        virtual int compare(const StateAttribute& sa) const
        {
            // check if the types are equal and then create the rhs variable
            // used by the COMPARE_StateAttribute_Parameter macros below.
            COMPARE_StateAttribute_Types(LineWidth,sa)

            // compare each parameter in turn against the rhs.
            COMPARE_StateAttribute_Parameter(_width)

            return 0; // passed all the above comparison macros, must be equal.
        }

        void setWidth(float width);

        inline float getWidth() const { return _width; }

        virtual void apply(State& state) const;

    protected :

        virtual ~LineWidth();

        float       _width;

};

}

#endif

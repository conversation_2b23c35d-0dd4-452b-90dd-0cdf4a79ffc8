#include <iostream>
#include <osg/Version>
#include <osg/Node>
#include <osg/Group>
#include <osg/Geode>
#include <osg/Geometry>
#include <osg/Array>
#include <osg/PrimitiveSet>
#include <osg/StateSet>
#include <osg/Material>
#include <osg/Texture2D>
#include <osg/Image>
#include <osg/MatrixTransform>
#include <osg/PositionAttitudeTransform>
#include <osg/Camera>
#include <osg/GraphicsContext>
#include <osgViewer/Viewer>
#include <osgGA/TrackballManipulator>

void testBasicOSGFunctionality() {
    std::cout << "=== OSG Basic Functionality Test ===" << std::endl;
    std::cout << "OSG Version: " << osgGetVersion() << std::endl;
    std::cout << "OSG Library Name: " << osgGetLibraryName() << std::endl;
    
    // Test 1: Create basic scene graph
    std::cout << "\n1. Testing Scene Graph Creation..." << std::endl;
    osg::ref_ptr<osg::Group> root = new osg::Group();
    osg::ref_ptr<osg::Geode> geode = new osg::Geode();
    root->addChild(geode);
    std::cout << "   ✓ Scene graph created successfully" << std::endl;
    
    // Test 2: Create geometry
    std::cout << "\n2. Testing Geometry Creation..." << std::endl;
    osg::ref_ptr<osg::Geometry> geometry = new osg::Geometry();
    
    // Create vertex array
    osg::ref_ptr<osg::Vec3Array> vertices = new osg::Vec3Array();
    vertices->push_back(osg::Vec3(-1.0f, 0.0f, -1.0f));
    vertices->push_back(osg::Vec3( 1.0f, 0.0f, -1.0f));
    vertices->push_back(osg::Vec3( 1.0f, 0.0f,  1.0f));
    vertices->push_back(osg::Vec3(-1.0f, 0.0f,  1.0f));
    geometry->setVertexArray(vertices);
    
    // Create normal array
    osg::ref_ptr<osg::Vec3Array> normals = new osg::Vec3Array();
    normals->push_back(osg::Vec3(0.0f, 1.0f, 0.0f));
    geometry->setNormalArray(normals, osg::Array::BIND_OVERALL);
    
    // Create texture coordinates
    osg::ref_ptr<osg::Vec2Array> texcoords = new osg::Vec2Array();
    texcoords->push_back(osg::Vec2(0.0f, 0.0f));
    texcoords->push_back(osg::Vec2(1.0f, 0.0f));
    texcoords->push_back(osg::Vec2(1.0f, 1.0f));
    texcoords->push_back(osg::Vec2(0.0f, 1.0f));
    geometry->setTexCoordArray(0, texcoords, osg::Array::BIND_PER_VERTEX);
    
    // Create primitive set
    osg::ref_ptr<osg::DrawElementsUInt> indices = new osg::DrawElementsUInt(osg::PrimitiveSet::TRIANGLES, 0);
    indices->push_back(0); indices->push_back(1); indices->push_back(2);
    indices->push_back(2); indices->push_back(3); indices->push_back(0);
    geometry->addPrimitiveSet(indices);
    
    geode->addDrawable(geometry);
    std::cout << "   ✓ Geometry created successfully" << std::endl;
    
    // Test 3: Create materials and state
    std::cout << "\n3. Testing Material and State..." << std::endl;
    osg::ref_ptr<osg::StateSet> stateset = geode->getOrCreateStateSet();
    osg::ref_ptr<osg::Material> material = new osg::Material();
    material->setDiffuse(osg::Material::FRONT_AND_BACK, osg::Vec4(1.0f, 0.0f, 0.0f, 1.0f));
    stateset->setAttributeAndModes(material, osg::StateAttribute::ON);
    std::cout << "   ✓ Material and state created successfully" << std::endl;
    
    // Test 4: Create transformations
    std::cout << "\n4. Testing Transformations..." << std::endl;
    osg::ref_ptr<osg::MatrixTransform> transform = new osg::MatrixTransform();
    osg::Matrix matrix;
    matrix.makeTranslate(0.0f, 0.0f, 0.0f);
    transform->setMatrix(matrix);
    transform->addChild(geode);
    root->addChild(transform);
    std::cout << "   ✓ Transformations created successfully" << std::endl;
    
    // Test 5: Test matrix operations
    std::cout << "\n5. Testing Matrix Operations..." << std::endl;
    osg::Matrixd projMatrix;
    projMatrix.makePerspective(45.0, 1.0, 0.1, 100.0);
    
    osg::Matrixd viewMatrix;
    viewMatrix.makeLookAt(osg::Vec3d(0, 0, 5), osg::Vec3d(0, 0, 0), osg::Vec3d(0, 1, 0));
    std::cout << "   ✓ Matrix operations completed successfully" << std::endl;
    
    std::cout << "\n=== All OSG Desktop Tests Passed! ===" << std::endl;
}

int main() {
    try {
        testBasicOSGFunctionality();
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}

/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSG_CONVEXPLANARPOLYGON
#define OSG_CONVEXPLANARPOLYGON 1

#include <osg/Plane>

#include <vector>

namespace osg {

/** A class for representing components of convex clipping volumes. */
class OSG_EXPORT ConvexPlanarPolygon
{

    public:

        ConvexPlanarPolygon();

        typedef std::vector<osg::Vec3> VertexList;

        void add(const Vec3& v) { _vertexList.push_back(v); }

        void setVertexList(const VertexList& vertexList) { _vertexList=vertexList; }

        VertexList& getVertexList() { return _vertexList; }

        const VertexList& getVertexList() const { return _vertexList; }

    protected:

        VertexList _vertexList;

};

}    // end of namespace

#endif

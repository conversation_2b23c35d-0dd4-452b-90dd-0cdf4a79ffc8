/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/
//osgParticle - Copyright (C) 2002 <PERSON>

#ifndef OSGPARTICLE_PARTICLESYSTEMUPDATER
#define OSGPARTICLE_PARTICLESYSTEMUPDATER 1

#include <osgParticle/Export>
#include <osgParticle/ParticleSystem>

#include <vector>

#include <osg/ref_ptr>
#include <osg/CopyOp>
#include <osg/Object>
#include <osg/Geode>
#include <osg/NodeVisitor>

#include <osgUtil/CullVisitor>

namespace osgParticle
{

    /**    A useful node class for updating particle systems automatically.
        When a ParticleSystemUpdater is traversed by a cull visitor, it calls the
        update() method on the specified particle systems. You should place this updater
        AFTER other nodes like emitters and programs.
    */
    class OSGPARTICLE_EXPORT ParticleSystemUpdater: public osg::Node {
    public:
        ParticleSystemUpdater();
        ParticleSystemUpdater(const ParticleSystemUpdater& copy, const osg::CopyOp& copyop = osg::CopyOp::SHALLOW_COPY);

        META_Node(osgParticle,ParticleSystemUpdater);

        /// Add a particle system to the list.
        virtual bool addParticleSystem(ParticleSystem* ps);

        /// Remove a particle system from the list (by pointer).
        virtual bool removeParticleSystem(ParticleSystem* ps);

        /// Remove a particle system(s) from the list (by index).
        virtual bool removeParticleSystem(unsigned int i, unsigned int numParticleSystemsToRemove=1);

        /// Replace ParticleSystem with another ParticleSystem.
        virtual bool replaceParticleSystem(ParticleSystem* origPS, ParticleSystem* newPS);

        /// set a particle system by index.
        virtual bool setParticleSystem( unsigned int i, ParticleSystem* ps );

        /// Return the number of particle systems on the list.
        inline unsigned int getNumParticleSystems() const;

        /// Get a particle system from the list.
        inline ParticleSystem* getParticleSystem(unsigned int i);

        /// Get a particle system from the list.
        inline const ParticleSystem* getParticleSystem(unsigned int i) const;

        /// return true if ParticleSystem is contained within ParticlsSystemUpdater.
        inline bool containsParticleSystem( const ParticleSystem* ps ) const;

        /// get index number of ParticleSystem.
        inline unsigned int getParticleSystemIndex( const ParticleSystem* ps ) const;

        virtual void traverse(osg::NodeVisitor& nv);

        virtual osg::BoundingSphere computeBound() const;

    protected:
        virtual ~ParticleSystemUpdater() {}
        ParticleSystemUpdater &operator=(const ParticleSystemUpdater &) { return *this; }

    private:
        typedef std::vector<osg::ref_ptr<ParticleSystem> > ParticleSystem_Vector;

        ParticleSystem_Vector _psv;
        double _t0;

        //added 1/17/06- <EMAIL>
        //a var to keep from doing multiple updates per frame
        unsigned int _frameNumber;
    };

    // INLINE FUNCTIONS

    inline unsigned int ParticleSystemUpdater::getNumParticleSystems() const
    {
        return static_cast<int>(_psv.size());
    }

    inline ParticleSystem* ParticleSystemUpdater::getParticleSystem(unsigned int i)
    {
        return _psv[i].get();
    }

    inline const ParticleSystem* ParticleSystemUpdater::getParticleSystem(unsigned int i) const
    {
        return _psv[i].get();
    }

    inline bool ParticleSystemUpdater::containsParticleSystem( const ParticleSystem* ps ) const
    {
       for( ParticleSystem_Vector::const_iterator itr=_psv.begin();
            itr!=_psv.end();
            ++itr )
       {
          if( itr->get() == ps ) return true;
       }
       return false;
    }

    inline unsigned int ParticleSystemUpdater::getParticleSystemIndex( const ParticleSystem* ps ) const
    {
       for( unsigned int particleSystemNum=0; particleSystemNum<_psv.size(); ++particleSystemNum )
       {
          if( _psv[particleSystemNum] == ps ) return particleSystemNum;
       }
       return _psv.size(); // node not found.
    }

}

#endif

/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON> 
 *
 * This library is open source and may be redistributed and/or modified under  
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or 
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 * 
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the 
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSG_VEC3I
#define OSG_VEC3I        1

namespace osg {

/** General purpose integer triple
**/
class Vec3i
{
    public:

        /** Type of Vec class.*/
        typedef int value_type;

        /** Number of vector components. */
        enum { num_components = 3 };
        
        /** Vec member variable. */
        value_type _v[3];

        Vec3i() { _v[0]=0; _v[1]=0; _v[2]=0; }
        
        Vec3i(value_type r, value_type g, value_type b) { _v[0]=r; _v[1]=g; _v[2]=b;  }

        inline bool operator == (const Vec3i& v) const { return _v[0]==v._v[0] && _v[1]==v._v[1] && _v[2]==v._v[2]; }
        inline bool operator != (const Vec3i& v) const { return _v[0]!=v._v[0] || _v[1]!=v._v[1] || _v[2]!=v._v[2]; }
        inline bool operator <  (const Vec3i& v) const
        {
            if (_v[0]<v._v[0])      return true;
            else if (_v[0]>v._v[0]) return false;
            else if (_v[1]<v._v[1]) return true;
            else if (_v[1]>v._v[1]) return false;
            else return (_v[2]<v._v[2]);
        }

        inline value_type* ptr() { return _v; }
        inline const value_type* ptr() const { return _v; }

        inline void set(value_type r, value_type g, value_type b)
        {
            _v[0]=r; _v[1]=g; _v[2]=b;
        }

        inline void set( const Vec3i& rhs)
        {
            _v[0]=rhs._v[0]; _v[1]=rhs._v[1]; _v[2]=rhs._v[2];
        }

        inline value_type& operator [] (unsigned int i) { return _v[i]; }
        inline value_type operator [] (unsigned int i) const { return _v[i]; }

        inline value_type& x() { return _v[0]; }
        inline value_type& y() { return _v[1]; }
        inline value_type& z() { return _v[2]; }

        inline value_type x() const { return _v[0]; }
        inline value_type y() const { return _v[1]; }
        inline value_type z() const { return _v[2]; }

        inline value_type& r() { return _v[0]; }
        inline value_type& g() { return _v[1]; }
        inline value_type& b() { return _v[2]; }

        inline value_type r() const { return _v[0]; }
        inline value_type g() const { return _v[1]; }
        inline value_type b() const { return _v[2]; }

        /** Multiply by scalar. */
        inline Vec3i operator * (value_type rhs) const
        {
            return Vec3i(_v[0]*rhs, _v[1]*rhs, _v[2]*rhs);
        }

        inline Vec3i operator / (value_type rhs) const
        {
            return Vec3i(_v[0]/rhs, _v[1]/rhs, _v[2]/rhs);
        }

        inline Vec3i operator + (value_type rhs) const
        {
            return Vec3i(_v[0]+rhs, _v[1]+rhs, _v[2]+rhs);
        }

        inline Vec3i operator - (value_type rhs) const
        {
            return Vec3i(_v[0]-rhs, _v[1]-rhs, _v[2]-rhs);
        }

        inline Vec3i operator + (const Vec3i& rhs) const
        {
            return Vec3i(_v[0]+rhs._v[0], _v[1]+rhs._v[1], _v[2]+rhs._v[2]);
        }

        inline Vec3i operator - (const Vec3i& rhs) const
        {
            return Vec3i(_v[0]-rhs._v[0], _v[1]-rhs._v[1], _v[2]-rhs._v[2]);
        }

        inline Vec3i operator * (const Vec3i& rhs) const
        {
            return Vec3i(_v[0]*rhs._v[0], _v[1]*rhs._v[1], _v[2]*rhs._v[2]);
        }
};

}

#endif

/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
 *
 * ViewDependentShadow codes Copyright (C) 2008 W<PERSON><PERSON><PERSON><PERSON>
 * Thanks to to my company http://www.ai.com.pl for allowing me free this work.
*/

#ifndef OSGSHADOW_MINIMALCULLBOUNDSSHADOWMAP
#define OSGSHADOW_MINIMALCULLBOUNDSSHADOWMAP 1

#include <osgShadow/MinimalShadowMap>

namespace osgShadow {

class OSGSHADOW_EXPORT MinimalCullBoundsShadowMap
    : public MinimalShadowMap
{
    public :
        /** Convenient typedef used in definition of ViewData struct and methods */
        typedef MinimalCullBoundsShadowMap ThisClass;
        /** Convenient typedef used in definition of ViewData struct and methods */
        typedef MinimalShadowMap           BaseClass;

        /** Classic OSG constructor */
        MinimalCullBoundsShadowMap();

        /** Classic OSG cloning constructor */
        MinimalCullBoundsShadowMap(
            const MinimalCullBoundsShadowMap& mcbsm,
            const osg::CopyOp& copyop=osg::CopyOp::SHALLOW_COPY);

        /** Declaration of standard OSG object methods */
        META_Object( osgShadow, MinimalCullBoundsShadowMap );

    protected:
        /** Classic protected OSG destructor */
        virtual ~MinimalCullBoundsShadowMap(void);

        struct OSGSHADOW_EXPORT ViewData: public MinimalShadowMap::ViewData
        {
            virtual void init( ThisClass * st, osgUtil::CullVisitor * cv );

            virtual void cullShadowReceivingScene(  );

            virtual void aimShadowCastingCamera( const osg::Light *light,
                const osg::Vec4 &worldLightPos,
                const osg::Vec3 &worldLightDir,
                const osg::Vec3 &worldLightUp
                = osg::Vec3(0,1,0) );

            typedef std::vector< osgUtil::RenderLeaf* > RenderLeafList;

            static unsigned RemoveOldRenderLeaves
                ( RenderLeafList &rllNew, RenderLeafList &rllOld );

            static unsigned RemoveIgnoredRenderLeaves( RenderLeafList &rll );

            static osg::BoundingBox ComputeRenderLeavesBounds
                ( RenderLeafList &rll, osg::Matrix & projectionToWorld );

            static osg::BoundingBox ComputeRenderLeavesBounds
                ( RenderLeafList &rll, osg::Matrix & projectionToWorld, osg::Polytope & polytope );

            static void GetRenderLeaves
                ( osgUtil::RenderBin *rb, RenderLeafList &rll );
        };

        META_ViewDependentShadowTechniqueData( ThisClass, ThisClass::ViewData )
};

} // namespace osgShadow

#endif

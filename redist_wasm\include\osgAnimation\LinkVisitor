/*  -*-c++-*-
 *  Copyright (C) 2008 <PERSON><PERSON> <<EMAIL>>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSGANIMATION_NODE_VISITOR_H
#define OSGANIMATION_NODE_VISITOR_H

#include <osg/NodeVisitor>
#include <osg/StateSet>
#include <osgAnimation/Animation>

namespace osgAnimation
{
    class AnimationUpdateCallbackBase;

    /** This class is instancied by the AnimationManagerBase, it will link animation target to updatecallback that have the same name
     */
    class OSGANIMATION_EXPORT LinkVisitor : public osg::NodeVisitor
    {
    public:
        LinkVisitor();

        META_NodeVisitor(osgAnimation, LinkVisitor);

        void apply(osg::Node& node);
        void apply(osg::Geode& node);

        AnimationList& getAnimationList();
        void reset();
        unsigned int getNbLinkedTarget() const { return _nbLinkedTarget; }

    protected:

        void handle_stateset(osg::StateSet* stateset);
        void link(osgAnimation::AnimationUpdateCallbackBase* cb);

        // animation list to link
        AnimationList _animations;

        // number of success link done
        unsigned int _nbLinkedTarget;
    };

}

#endif

#this file is automatically generated 

SET(TARGET_ADDED_LIBRARIES osgdb_ive osgdb_openflight osgdb_osg osgdb_rgb)
SET(TARGET_ADDED_LIBRARIES ${TARGET_ADDED_LIBRARIES}
    osgdb_deprecated_osg osgdb_deprecated_osgparticle osgdb_deprecated_osganimation
    osgdb_deprecated_osgfx osgdb_deprecated_osgsim osgdb_deprecated_osgtext
    osgdb_deprecated_osgviewer osgdb_deprecated_osgshadow osgdb_deprecated_osgterrain
    osgdb_deprecated_osgvolume osgdb_deprecated_osgwidget
)
SET(TARGET_ADDED_LIBRARIES ${TARGET_ADDED_LIBRARIES}
    osgdb_serializers_osg osgdb_serializers_osgparticle osgdb_serializers_osgtext
    osgdb_serializers_osgterrain osgdb_serializers_osganimation osgdb_serializers_osgfx
    osgdb_serializers_osgshadow osgdb_serializers_osgmanipulator osgdb_serializers_osgsim
    osgdb_serializers_osgvolume
)

IF(FREETYPE_FOUND)
    ADD_DEFINITIONS(-DUSE_FREETYPE)
    SET(TARGET_ADDED_LIBRARIES ${TARGET_ADDED_LIBRARIES} osgdb_freetype)
ENDIF(FREETYPE_FOUND)

SET(TARGET_SRC osgstaticviewer.cpp )
#### end var setup  ###
SETUP_EXAMPLE(osgstaticviewer)

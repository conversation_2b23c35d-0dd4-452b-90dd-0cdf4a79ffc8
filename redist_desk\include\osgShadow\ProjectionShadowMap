/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
 *
 * ViewDependentShadow codes Copyright (C) 2008 W<PERSON><PERSON><PERSON><PERSON>
 * Thanks to to my company http://www.ai.com.pl for allowing me free this work.
*/

#ifndef OSGSHADOW_PROJECTIONSHADOWMAP
#define OSGSHADOW_PROJECTIONSHADOWMAP 1

#include <osgShadow/MinimalShadowMap>

namespace osgShadow {

template< typename MinimalBoundsBaseClass, typename ShadowProjectionAlgorithmClass >
class OSGSHADOW_EXPORT ProjectionShadowMap : public MinimalBoundsBaseClass
{
    public :
        /** Convenient typedef used in definition of ViewData struct and methods */
        typedef MinimalBoundsBaseClass                                                     BaseClass;
        /** Convenient typedef used in definition of ViewData struct and methods */
        typedef ProjectionShadowMap<MinimalBoundsBaseClass,ShadowProjectionAlgorithmClass> ThisClass;

        /** Classic OSG constructor */
        ProjectionShadowMap() : BaseClass()
        {
        }

        /** Classic OSG cloning constructor */
        ProjectionShadowMap(
            const ProjectionShadowMap& copy,
            const osg::CopyOp& copyop=osg::CopyOp::SHALLOW_COPY) : BaseClass(copy,copyop)
        {
        }

        /** Declaration of standard OSG object methods */
        META_Object( osgShadow, ProjectionShadowMap );

    protected:

        /** Classic protected OSG destructor */
        virtual ~ProjectionShadowMap(void)
        {
        }

        struct OSGSHADOW_EXPORT ViewData: public BaseClass::ViewData,
                         public ShadowProjectionAlgorithmClass
        {
    #if 0
            virtual void init( ThisClass * st, osgUtil::CullVisitor * cv );
            {
                BaseClass::ViewData::init( st, cv );
            }
    #endif

            virtual void frameShadowCastingCamera
                ( const osg::Camera* cameraMain, osg::Camera* cameraShadow, int pass = 1 )
            {
                if( pass == BaseClass::ViewData::_frameShadowCastingCameraPasses - 1 )
                {
                    // Force dependent name lookup
                    ShadowProjectionAlgorithmClass::operator()
                        ( &this->_sceneReceivingShadowPolytope, cameraMain, cameraShadow );
                }

                // DebugBoundingBox( computeScenePolytopeBounds(), "ProjectionShadowMap" );
                BaseClass::ViewData::frameShadowCastingCamera( cameraMain, cameraShadow, pass );
            }
        };

        META_ViewDependentShadowTechniqueData( ThisClass, typename ThisClass::ViewData )
};

} // namespace osgShadow

#endif

/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/
//Build by <PERSON>

#ifndef OSGPARTICLE_BOX_PLACER
#define OSGPARTICLE_BOX_PLACER 1

#include <osgParticle/CenteredPlacer>
#include <osgParticle/Particle>
#include <osgParticle/range>

#include <osg/CopyOp>
#include <osg/Object>
#include <osg/Vec3>
#include <osg/Math>

namespace osgParticle
{

    /** A box-shaped particle placer.
        This placer sets the initial position of incoming particle by choosing a random position
        within the volume of a box; this placer is defined by four parameters: a <I>center point</I>,
        which is inherited directly from <CODE>osgParticle::CenteredPlacer</CODE>, and three ranges of values
    for the valid <I>X</I>, <I>Y</I>, and <I>Z</I> coordinates.
    */
    class BoxPlacer: public CenteredPlacer {
    public:
        inline BoxPlacer();
        inline BoxPlacer(const BoxPlacer& copy, const osg::CopyOp& copyop = osg::CopyOp::SHALLOW_COPY);

        /// Get the range of possible values along the X axis.
        inline const rangef& getXRange() const;

        /// Set the range of possible values along the X axis.
        inline void setXRange(const rangef& r);

        /// Set the range of possible values along the X axis.
        inline void setXRange(float r1, float r2);

        /// Get the range of possible values along the Y axis.
        inline const rangef& getYRange() const;

        /// Set the range of possible values along the Y axis.
        inline void setYRange(const rangef& r);

        /// Set the range of possible values along the Y axis.
        inline void setYRange(float r1, float r2);

        /// Get the range of possible values along the Z axis.
        inline const rangef& getZRange() const;

        /// Set the range of possible values along the Z axis.
        inline void setZRange(const rangef& r);

        /// Set the range of possible values along the Z axis.
        inline void setZRange(float r1, float r2);

        META_Object(osgParticle, BoxPlacer);

        /// Place a particle. Do not call it manually.
        inline void place(Particle* P) const;

        /// return the volume of the box
        inline float volume() const;

        /// return the control position
        inline osg::Vec3 getControlPosition() const;

    protected:
        virtual ~BoxPlacer() {}
        BoxPlacer& operator=(const BoxPlacer&) { return *this; }

    private:
        rangef _x_range;
        rangef _y_range;
        rangef _z_range;
    };

    // INLINE FUNCTIONS

    inline BoxPlacer::BoxPlacer()
    : CenteredPlacer(), _x_range(-1, 1), _y_range(-1, 1), _z_range(-1, 1)
    {
    }

    inline BoxPlacer::BoxPlacer(const BoxPlacer& copy, const osg::CopyOp& copyop)
    : CenteredPlacer(copy, copyop),
    _x_range(copy._x_range), _y_range(copy._y_range), _z_range(copy._z_range)
    {
    }

    inline const rangef& BoxPlacer::getXRange() const
    {
        return _x_range;
    }

    inline void BoxPlacer::setXRange(const rangef& r)
    {
        _x_range = r;
    }

    inline void BoxPlacer::setXRange(float r1, float r2)
    {
        _x_range.minimum = r1;
        _x_range.maximum = r2;
    }

    inline const rangef& BoxPlacer::getYRange() const
    {
        return _y_range;
    }

    inline void BoxPlacer::setYRange(const rangef& r)
    {
        _y_range = r;
    }

    inline void BoxPlacer::setYRange(float r1, float r2)
    {
        _y_range.minimum = r1;
        _y_range.maximum = r2;
    }

    inline const rangef& BoxPlacer::getZRange() const
    {
        return _z_range;
    }

    inline void BoxPlacer::setZRange(const rangef& r)
    {
        _z_range = r;
    }

    inline void BoxPlacer::setZRange(float r1, float r2)
    {
        _z_range.minimum = r1;
        _z_range.maximum = r2;
    }

    inline void BoxPlacer::place(Particle* P) const
    {
        osg::Vec3 pos(
            getCenter().x() + _x_range.get_random(),
            getCenter().y() + _y_range.get_random(),
            getCenter().z() + _z_range.get_random());

        P->setPosition(pos);
    }

    inline float BoxPlacer::volume() const
    {
        return (_x_range.maximum - _x_range.minimum) *
               (_y_range.maximum - _y_range.minimum) *
               (_z_range.maximum - _z_range.minimum);
    }

    inline osg::Vec3 BoxPlacer::getControlPosition() const
    {
        return getCenter();
    }

}

#endif

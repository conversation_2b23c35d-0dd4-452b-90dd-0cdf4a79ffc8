/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2013 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSG_SCRIPTENGINE
#define OSG_SCRIPTENGINE 1

#include <osg/Object>
#include <osg/Callback>
#include <osg/NodeVisitor>
#include <osg/UserDataContainer>

namespace osg
{

// forward declare
class ScriptEngine;

/* Script class for wrapping a script and the language used in the script.*/
class Script : public osg::Object
{
    public:
        Script():_modifiedCount(0) {}
        Script(const std::string& language, const std::string& str): _language(language), _script(str), _modifiedCount(0)  {}
        Script(const Script& rhs, const osg::CopyOp& copyop=osg::CopyOp::SHALLOW_COPY): osg::Object(rhs,copyop), _language(rhs._language), _script(rhs._script), _modifiedCount(0) {}

        META_Object(osg, Script)

        void setLanguage(const std::string& language) { _language = language; dirty(); }
        const std::string& getLanguage() const{ return _language; }

        void setScript(const std::string& str) { _script = str; dirty(); }
        const std::string& getScript() const { return _script; }

        void dirty() { ++_modifiedCount; }
        unsigned int getModifiedCount() const { return _modifiedCount; }

    protected:

        virtual ~Script() {}

        std::string _language;
        std::string _script;
        unsigned int _modifiedCount;
};


/** NodeCallback for attaching a script to a NodeCallback so that it can be called as an update or event callback.*/
class OSG_EXPORT ScriptNodeCallback : public osg::NodeCallback
{
    public:
        ScriptNodeCallback(Script* script=0, const std::string& entryPoint="") : _script(script), _entryPoint(entryPoint) {}
        ScriptNodeCallback(const ScriptNodeCallback& rhs, const osg::CopyOp& copyop=osg::CopyOp::SHALLOW_COPY):
            osg::Object(rhs,copyop),
            osg::Callback(rhs,copyop),
            osg::NodeCallback(rhs,copyop), _script(rhs._script) {}

        META_Object(osg, ScriptNodeCallback)

        /** Set the script to call.*/
        void setScript(osg::Script* script) { _script  = script; }

        /** Get the script to call.*/
        osg::Script* getScript() { return _script.get(); }

        /** Get the script to call.*/
        const osg::Script* getScript() const { return _script.get(); }

        /** Set the entry point to call.*/
        void setEntryPoint(const std::string& script) { _entryPoint  = script; }

        /** Get the script to call.*/
        const std::string& getEntryPoint() const { return _entryPoint; }

        /** find the ScriptEngine from looking at the UserDataContainers of nodes in scene graph above the ScriptCallback.*/
        osg::ScriptEngine* getScriptEngine(osg::NodePath& nodePath);

        /** NodeCallback method, calls the Script.*/
        virtual void operator()(osg::Node* node, osg::NodeVisitor* nv);

    protected:

        virtual ~ScriptNodeCallback() {}

        osg::ref_ptr<Script>    _script;
        std::string             _entryPoint;
};

/** ScriptEngine base class for integrating different scripting languages.
 *  Concrete ScriptEngine's are provided by osgDB::readFile<ScriptEngine> */
class ScriptEngine : public osg::Object
{
    public:

        /** get the scripting language supported by the ScriptEngine.*/
        inline const std::string& getLanguage() const { return _language; }

        /** run a Script.*/
        bool run(osg::Script* script)
        {
            // assumpt empty input and output parameters lists
            Parameters inputParameters, outputParameters;
            return run(script, "", inputParameters, outputParameters);
        }

        /** run a Script.*/
        virtual bool run(osg::Script* script, const std::string& entryPoint, Parameters& inputParameters, Parameters& outputParameters) = 0;

    protected:

        ScriptEngine(const std::string& language):_language(language) { setName(language); }
        virtual ~ScriptEngine() {}

        std::string _language;
};

}

#endif

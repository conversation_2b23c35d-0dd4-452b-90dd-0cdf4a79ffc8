/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSG_CLAMPCOLOR
#define OSG_CLAMPCOLOR 1

#include <osg/StateAttribute>

#ifndef GL_ARB_color_buffer_float
#define GL_RGBA_FLOAT_MODE_ARB                  0x8820
#define GL_CLAMP_VERTEX_COLOR_ARB               0x891A
#define GL_CLAMP_FRAGMENT_COLOR_ARB             0x891B
#define GL_CLAMP_READ_COLOR_ARB                 0x891C
#define GL_FIXED_ONLY_ARB                       0x891D
#endif

#ifndef GL_FIXED_ONLY
#define GL_FIXED_ONLY                           GL_FIXED_ONLY_ARB
#define GL_CLAMP_VERTEX_COLOR                   GL_CLAMP_VERTEX_COLOR_ARB
#define GL_CLAMP_READ_COLOR                     GL_CLAMP_READ_COLOR_ARB
#define GL_CLAMP_FRAGMENT_COLOR                 GL_CLAMP_FRAGMENT_COLOR_ARB
#endif

#if defined(OSG_GL3_AVAILABLE)
    #define GL_CLAMP_VERTEX_COLOR             0x891A
    #define GL_CLAMP_FRAGMENT_COLOR           0x891B
#endif

namespace osg {

/** Encapsulates OpenGL ClampColor state. */
class OSG_EXPORT ClampColor : public StateAttribute
{
    public :

        ClampColor();

        ClampColor(GLenum vertexMode, GLenum fragmentMode, GLenum readMode);

        /** Copy constructor using CopyOp to manage deep vs shallow copy. */
        ClampColor(const ClampColor& rhs,const CopyOp& copyop=CopyOp::SHALLOW_COPY):
            StateAttribute(rhs,copyop),
            _clampVertexColor(rhs._clampVertexColor),
            _clampFragmentColor(rhs._clampFragmentColor),
            _clampReadColor(rhs._clampReadColor) {}

        META_StateAttribute(osg, ClampColor,CLAMPCOLOR);

        /** Return -1 if *this < *rhs, 0 if *this==*rhs, 1 if *this>*rhs. */
        virtual int compare(const StateAttribute& sa) const
        {
            // Check for equal types, then create the rhs variable
            // used by the COMPARE_StateAttribute_Parameter macros below.
            COMPARE_StateAttribute_Types(ClampColor,sa)

            // Compare each parameter in turn against the rhs.
            COMPARE_StateAttribute_Parameter(_clampVertexColor)
            COMPARE_StateAttribute_Parameter(_clampFragmentColor)
            COMPARE_StateAttribute_Parameter(_clampReadColor)

            return 0; // Passed all the above comparison macros, so must be equal.
        }

        void setClampVertexColor(GLenum mode) { _clampVertexColor = mode; }
        GLenum getClampVertexColor() const { return _clampVertexColor; }

        void setClampFragmentColor(GLenum mode) { _clampFragmentColor = mode; }
        GLenum getClampFragmentColor() const { return _clampFragmentColor; }

        void setClampReadColor(GLenum mode) { _clampReadColor = mode; }
        GLenum getClampReadColor() const { return _clampReadColor; }

        virtual void apply(State& state) const;

    protected :

        virtual ~ClampColor();

        GLenum _clampVertexColor;
        GLenum _clampFragmentColor;
        GLenum _clampReadColor;

};

}

#endif

/*  -*-c++-*-
 *  Copyright (C) 2009 <PERSON><PERSON> <<EMAIL>>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
 */

#ifndef OSGANIMATION_STACKED_SCALE_ELEMENT
#define OSGANIMATION_STACKED_SCALE_ELEMENT 1

#include <osg/Object>
#include <osgAnimation/Export>
#include <osgAnimation/StackedTransformElement>
#include <osgAnimation/Target>

namespace osgAnimation
{

    class OSGANIMATION_EXPORT StackedScaleElement : public StackedTransformElement
    {
    public:
        META_Object(osgAnimation, StackedScaleElement)

        StackedScaleElement();
        StackedScaleElement(const StackedScaleElement&, const osg::CopyOp&);
        StackedScaleElement(const std::string& name, const osg::Vec3& scale = osg::Vec3(1,1,1));
        StackedScaleElement(const osg::Vec3& scale);

        void applyToMatrix(osg::Matrix& matrix) const;
        osg::Matrix getAsMatrix() const;
        bool isIdentity() const;
        void update(float t = 0.0);
        const osg::Vec3& getScale() const;
        void setScale(const osg::Vec3& scale);

        virtual Target* getOrCreateTarget();
        virtual Target* getTarget();
        virtual const Target* getTarget() const;

    protected:
        osg::Vec3 _scale;
        osg::ref_ptr<Vec3Target> _target;
    };


}

#endif


/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSG_CONVEXPLANAROCCLUDER
#define OSG_CONVEXPLANAROCCLUDER 1

#include <osg/ConvexPlanarPolygon>
#include <osg/Object>

namespace osg {

class OccluderVolume;

/** A class for representing convex clipping volumes made up of several ConvexPlanarPolygon. */
class OSG_EXPORT ConvexPlanarOccluder : public Object
{

    public:

        ConvexPlanarOccluder():Object() {}
        ConvexPlanarOccluder(const ConvexPlanarOccluder& cpo,const CopyOp& copyop=CopyOp::SHALLOW_COPY):
            Object(cpo,copyop),
            _occluder(cpo._occluder),
            _holeList(cpo._holeList) {}

        META_Object(osg,ConvexPlanarOccluder);

        void setOccluder(const ConvexPlanarPolygon& cpp) { _occluder = cpp; }

        ConvexPlanarPolygon& getOccluder() { return _occluder; }

        const ConvexPlanarPolygon& getOccluder() const { return _occluder; }



        typedef std::vector<ConvexPlanarPolygon> HoleList;

        void addHole(const ConvexPlanarPolygon& cpp) { _holeList.push_back(cpp); }

        void setHoleList(const HoleList& holeList) { _holeList=holeList; }

        HoleList& getHoleList() { return _holeList; }

        const HoleList& getHoleList() const { return _holeList; }

    protected:

        ~ConvexPlanarOccluder(); // {}

        ConvexPlanarPolygon _occluder;
        HoleList _holeList;

};

}    // end of namespace

#endif

/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSGDB_VERSION
#define OSGDB_VERSION 1

#include <osgDB/Export>

extern "C" {

/**
 * osgDBGetVersion() returns the library version number.
 * Numbering convention : OpenSceneGraph-1.0 will return 1.0 from osgDBGetVersion.
 *
 * This C function can be also used to check for the existence of the OpenSceneGraph
 * library using autoconf and its m4 macro AC_CHECK_LIB.
 *
 * Here is the code to add to your configure.in:
 \verbatim
 #
 # Check for the OpenSceneGraph (OSG) DB library
 #
 AC_CHECK_LIB(osg, osgDBGetVersion, ,
    [AC_MSG_ERROR(OpenSceneGraph DB library not found. See http://www.openscenegraph.org)],)
 \endverbatim
*/
extern OSGDB_EXPORT const char* osgDBGetVersion();

/**
 * getLibraryName() returns the library name in human friendly form.
*/
extern OSGDB_EXPORT const char* osgDBGetLibraryName();

}

#endif

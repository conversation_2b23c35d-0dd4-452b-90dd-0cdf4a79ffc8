/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2008 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

// Code by: <PERSON> (cubicool) 2007-2008

#ifndef OSGWIDGET_UTIL
#define OSGWIDGET_UTIL

#include <ctype.h>
#include <cctype>
#include <algorithm>
#include <sstream>
#include <osg/Camera>
#include <osgViewer/Viewer>
#include <osgViewer/CompositeViewer>

#include <osgWidget/Export>
#include <osgWidget/Types>

namespace osgWidget {

// These are NOT OSGWIDGET_EXPORT'd; these are internal only!

inline std::ostream& _notify(osg::NotifySeverity ns = osg::INFO)
{
    std::ostream& n = osg::notify(ns);

    return n << "osgWidget: ";
}

inline std::ostream& warn()
{
    return _notify(osg::WARN);
}

inline std::ostream& info()
{
    return _notify();
}

inline char lowerCaseChar(const char in)
{
    return (unsigned char)(::tolower((int)((unsigned char)in)));
}

inline std::string lowerCase(const std::string& str)
{
    std::string s = str;

    // TODO: Why can't I specify std::tolower?
    std::transform(s.begin(), s.end(), s.begin(), lowerCaseChar);

    return s;
}

// TODO: Is this totally ghetto or what?
template <typename T>
inline bool hasDecimal(T v)
{
    return (v - static_cast<T>(static_cast<long>(v))) > 0.0f;
}

class WindowManager;

// These ARE OSGWIDGET_EXPORT'd for your convenience. :)

OSGWIDGET_EXPORT std::string  getFilePath        (const std::string&);
OSGWIDGET_EXPORT std::string  generateRandomName (const std::string&);
OSGWIDGET_EXPORT osg::Camera* createOrthoCamera  (matrix_type, matrix_type);

// This function sets up our basic example framework, and optionally sets some root
// scene data.
OSGWIDGET_EXPORT int  createExample          (osgViewer::Viewer&, WindowManager*, osg::Node* = 0);
OSGWIDGET_EXPORT bool writeWindowManagerNode (WindowManager*);

}

#endif

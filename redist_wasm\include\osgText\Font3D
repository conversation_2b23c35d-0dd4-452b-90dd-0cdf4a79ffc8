/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSGTEXT_FONT3D
#define OSGTEXT_FONT3D 1

#include <osgText/Font>

namespace osgText {

typedef Font Font3D;

#ifdef OSG_PROVIDE_READFILE
/** deprecated, use readFontFile() instead.*/
inline Font* readFont3DFile(const std::string& filename, const osgDB::ReaderWriter::Options* userOptions = 0)
{
    return readFontFile(filename,userOptions);
}

/** deprecated, use readFontStream() instead.*/
inline Font* readFont3DStream(std::istream& stream, const osgDB::ReaderWriter::Options* userOptions = 0)
{
    return readFontStream(stream, userOptions);
}
#endif

/** deprecated, use readRefFontFile() instead.*/
inline osg::ref_ptr<Font> readRefFont3DFile(const std::string& filename, const osgDB::ReaderWriter::Options* userOptions = 0)
{
    return readRefFontFile(filename, userOptions);
}

/** deprecated, use readRefFontStream() instead.*/
inline osg::ref_ptr<Font> readRefFont3DStream(std::istream& stream, const osgDB::ReaderWriter::Options* userOptions = 0)
{
    return readRefFontStream(stream, userOptions);
}

/** deprecated, use findFontFile() instead.*/
inline std::string findFont3DFile(const std::string& str)
{
    return findFontFile(str);
}

}

#endif

/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/
//osgFX - Copyright (C) 2003 Marco <PERSON>

#ifndef OSGFX_VALIDATOR_
#define OSGFX_VALIDATOR_

#include <osgFX/Effect>

#include <osg/ref_ptr>
#include <osg/StateAttribute>

#include <vector>

namespace osgFX
{

    /**
     This class is used internally by osgFX::Effect to choose between different
     techniques dynamically. The apply() method will call each technique's
     validate() method and store the results in a buffered array. The Effect
     class will then choose the first technique that could be validated in all
     active rendering contexts.
     */
    class OSGFX_EXPORT Validator: public osg::StateAttribute {
    public:

        Validator();
        Validator(Effect* effect);
        Validator(const Validator& copy, const osg::CopyOp& copyop = osg::CopyOp::SHALLOW_COPY);

        META_StateAttribute(osgFX, Validator, VALIDATOR);

        void apply(osg::State& state) const;
        void compileGLObjects(osg::State& state) const;

        inline int compare(const osg::StateAttribute& sa) const;

        inline void disable() { _effect = 0; }

    protected:
        virtual ~Validator() {}
        Validator& operator=(const Validator&) { return *this; }

    private:
        mutable Effect* _effect;
    };

    // INLINE METHODS

    inline int Validator::compare(const osg::StateAttribute& sa) const
    {
        // check the types are equal and then create the rhs variable
        //used by the COMPARE_StateAttribute_Parameter macros below.
        COMPARE_StateAttribute_Types(Validator,sa)

        // compare parameters
        COMPARE_StateAttribute_Parameter(_effect)

        return 0;
    }

}

#endif

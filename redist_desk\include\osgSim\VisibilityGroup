/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSGSIM_VISIBILITYGROUP
#define OSGSIM_VISIBILITYGROUP 1

#include <osg/Node>
#include <osg/Group>
#include <osg/NodeVisitor>

#include <osgSim/Export>

namespace osgSim {

/** VisibilityGroup renders (traverses) it's children only when the camera is inside a specified visibility volume.
 * The visibility volume is intersected with a line segment that extends from
 * the current camera's eye-point along the view vector for a given segment length.
 * If an intersection is detected then the node's children are traversed.
 */
class OSGSIM_EXPORT VisibilityGroup : public osg::Group
{
    public :

        VisibilityGroup();

        /** Copy constructor using CopyOp to manage deep vs shallow copy.*/
        VisibilityGroup(const VisibilityGroup&,const osg::CopyOp& copyop=osg::CopyOp::SHALLOW_COPY);

        META_Node(osgSim, VisibilityGroup);

        virtual void traverse(osg::NodeVisitor& nv);

        /** Set the subgraph that is intersected for the visibility determination.*/
        void setVisibilityVolume(osg::Node* node) { _visibilityVolume = node; }

        /** Get the subgraph that is intersected for the visibility determination.*/
        osg::Node* getVisibilityVolume() { return _visibilityVolume.get(); }

        /** Get the const subgraph that is intersected for the visibility determination.*/
        const osg::Node* getVisibilityVolume() const { return _visibilityVolume.get(); }

        /** Set the traversal mask for the intersection testing.*/
        void setVolumeIntersectionMask(osg::Node::NodeMask mask) { _volumeIntersectionMask = mask; }

        /** Get the traversal mask for the intersection testing.*/
        osg::Node::NodeMask getVolumeIntersectionMask() const { return _volumeIntersectionMask; }

        /** Set the length of the intersection segment.
         * The segments extends this many database units from the camera eye-point along the look vector.
         * If this is left at zero then the diameter of the bounding sphere of the visibility volume is used.*/
        void setSegmentLength(float length) { _segmentLength = length; }

        /** Get the length of the intersection segment.*/
        float getSegmentLength() const { return _segmentLength; }

    protected :

        virtual ~VisibilityGroup() {}

        osg::ref_ptr<osg::Node> _visibilityVolume;
        osg::Node::NodeMask _volumeIntersectionMask;
        float _segmentLength;
};

}
#endif

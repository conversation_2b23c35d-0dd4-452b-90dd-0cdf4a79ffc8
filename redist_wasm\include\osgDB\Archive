/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSGDB_ARCHIVE
#define OSGDB_ARCHIVE 1

#include <osgDB/ReaderWriter>
#include <osgDB/Registry>
#include <osgDB/FileUtils>

#include <fstream>
#include <list>

namespace osgDB {


/** Base class for implementing database Archives. See src/osgPlugins/osga for an example of a concrete implementation. */
class OSGDB_EXPORT Archive : public ReaderWriter
{
    public:
        Archive();
        virtual ~Archive();

        virtual const char* libraryName() const { return "osgDB"; }

        virtual const char* className() const { return "Archive"; }

        virtual bool acceptsExtension(const std::string& /*extension*/) const { return true; }

        /** close the archive.*/
        virtual void close() = 0;

        /** Get the file name which represents the archived file.*/
        virtual std::string getArchiveFileName() const = 0;

        /** Get the file name which represents the master file recorded in the Archive.*/
        virtual std::string getMasterFileName() const = 0;

        /** return true if file exists in archive.*/
        virtual bool fileExists(const std::string& filename) const = 0;

        /** return type of file. */
        virtual FileType getFileType(const std::string& filename) const = 0;

        typedef osgDB::DirectoryContents FileNameList;

        /** Get the full list of file names available in the archive.*/
        virtual bool getFileNames(FileNameList& fileNames) const = 0;

        /** return the contents of a directory.
          * returns an empty array on any error.*/
        virtual DirectoryContents getDirectoryContents(const std::string& dirName) const;


        virtual ReadResult readObject(const std::string& /*fileName*/,const Options* =NULL) const = 0;
        virtual ReadResult readImage(const std::string& /*fileName*/,const Options* =NULL) const = 0;
        virtual ReadResult readHeightField(const std::string& /*fileName*/,const Options* =NULL) const = 0;
        virtual ReadResult readNode(const std::string& /*fileName*/,const Options* =NULL) const = 0;
        virtual ReadResult readShader(const std::string& /*fileName*/,const Options* =NULL) const = 0;

        virtual WriteResult writeObject(const osg::Object& /*obj*/,const std::string& /*fileName*/,const Options* =NULL) const = 0;
        virtual WriteResult writeImage(const osg::Image& /*image*/,const std::string& /*fileName*/,const Options* =NULL) const = 0;
        virtual WriteResult writeHeightField(const osg::HeightField& /*heightField*/,const std::string& /*fileName*/,const Options* =NULL) const = 0;
        virtual WriteResult writeNode(const osg::Node& /*node*/,const std::string& /*fileName*/,const Options* =NULL) const = 0;
        virtual WriteResult writeShader(const osg::Shader& /*shader*/,const std::string& /*fileName*/,const Options* =NULL) const = 0;

};

/** Open an archive for reading or writing.*/
OSGDB_EXPORT Archive* openArchive(const std::string& filename, ReaderWriter::ArchiveStatus status, unsigned int indexBlockSizeHint=4096);

/** Open an archive for reading or writing.*/
OSGDB_EXPORT Archive* openArchive(const std::string& filename, ReaderWriter::ArchiveStatus status, unsigned int indexBlockSizeHint,Options* options);
}

#endif // OSGDB_ARCHIVE

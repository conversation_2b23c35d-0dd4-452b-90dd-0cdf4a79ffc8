/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSGUTIL_DISPLAYREQUIREMENTSVISITOR
#define OSGUTIL_DISPLAYREQUIREMENTSVISITOR 1

#include <osg/NodeVisitor>
#include <osg/Geode>
#include <osg/DisplaySettings>

#include <osgUtil/Export>

namespace osgUtil {

/** A visitor for traversing a scene graph establishing which OpenGL visuals are
  * required to support rendering of that scene graph.  The results can then be used by
  * applications to set up their windows with the correct visuals.  Have a look at
  * src/osgGLUT/Viewer.cpp's Viewer::open() method for an example of how to use it.
  */
class OSGUTIL_EXPORT DisplayRequirementsVisitor : public osg::NodeVisitor
{
    public:

        /** Default to traversing all children, and requiresDoubleBuffer,
          * requiresRGB and requiresDepthBuffer to true and with
          * alpha and stencil off.*/
        DisplayRequirementsVisitor();

        META_NodeVisitor(osgUtil, DisplayRequirementsVisitor)

        /** Set the DisplaySettings. */
        inline void setDisplaySettings(osg::DisplaySettings* ds) { _ds = ds; }

        /** Get the DisplaySettings */
        inline const osg::DisplaySettings* getDisplaySettings() const { return _ds.get(); }

        virtual void applyStateSet(osg::StateSet& stateset);

        virtual void apply(osg::Node& node);

    protected:

        osg::ref_ptr<osg::DisplaySettings> _ds;

};

}

#endif

#include <iostream>
#include <osg/Version>
#include <osg/Node>
#include <osg/Group>
#include <osg/Geode>
#include <osg/Geometry>
#include <osg/Array>
#include <osg/PrimitiveSet>
#include <osg/StateSet>
#include <osg/Material>
#include <osg/Texture2D>
#include <osg/Image>
#include <osgViewer/Viewer>
#include <osgGA/TrackballManipulator>

int main() {
    std::cout << "OSG Version: " << osgGetVersion() << std::endl;
    std::cout << "OSG Library Name: " << osgGetLibraryName() << std::endl;
    
    // 创建一个简单的几何体
    osg::ref_ptr<osg::Geometry> geometry = new osg::Geometry();
    
    // 创建顶点数组
    osg::ref_ptr<osg::Vec3Array> vertices = new osg::Vec3Array();
    vertices->push_back(osg::Vec3(-1.0f, 0.0f, -1.0f));
    vertices->push_back(osg::Vec3( 1.0f, 0.0f, -1.0f));
    vertices->push_back(osg::Vec3( 1.0f, 0.0f,  1.0f));
    vertices->push_back(osg::Vec3(-1.0f, 0.0f,  1.0f));
    geometry->setVertexArray(vertices);
    
    // 创建法线数组
    osg::ref_ptr<osg::Vec3Array> normals = new osg::Vec3Array();
    normals->push_back(osg::Vec3(0.0f, 1.0f, 0.0f));
    geometry->setNormalArray(normals, osg::Array::BIND_OVERALL);
    
    // 创建纹理坐标数组
    osg::ref_ptr<osg::Vec2Array> texcoords = new osg::Vec2Array();
    texcoords->push_back(osg::Vec2(0.0f, 0.0f));
    texcoords->push_back(osg::Vec2(1.0f, 0.0f));
    texcoords->push_back(osg::Vec2(1.0f, 1.0f));
    texcoords->push_back(osg::Vec2(0.0f, 1.0f));
    geometry->setTexCoordArray(0, texcoords, osg::Array::BIND_PER_VERTEX);
    
    // 创建图元集合
    osg::ref_ptr<osg::DrawElementsUInt> indices = new osg::DrawElementsUInt(osg::PrimitiveSet::TRIANGLES, 0);
    indices->push_back(0); indices->push_back(1); indices->push_back(2);
    indices->push_back(2); indices->push_back(3); indices->push_back(0);
    geometry->addPrimitiveSet(indices);
    
    // 创建Geode并添加几何体
    osg::ref_ptr<osg::Geode> geode = new osg::Geode();
    geode->addDrawable(geometry);
    
    // 创建根节点
    osg::ref_ptr<osg::Group> root = new osg::Group();
    root->addChild(geode);
    
    std::cout << "Successfully created OSG scene graph!" << std::endl;
    std::cout << "Root node has " << root->getNumChildren() << " children" << std::endl;
    
    return 0;
}

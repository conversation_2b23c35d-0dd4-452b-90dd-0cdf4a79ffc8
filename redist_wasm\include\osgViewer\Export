/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

// The following symbol has a underscore suffix for compatibility.
#ifndef OSGVIEWER_EXPORT_
#define OSGVIEWER_EXPORT_ 1

#include<osg/Config>

#if defined(_MSC_VER) && defined(OSG_DISABLE_MSVC_WARNINGS)
    #pragma warning( disable : 4244 )
    #pragma warning( disable : 4251 )
    #pragma warning( disable : 4267 )
    #pragma warning( disable : 4275 )
    #pragma warning( disable : 4290 )
    #pragma warning( disable : 4786 )
    #pragma warning( disable : 4305 )
    #pragma warning( disable : 4996 )
#endif

#if defined(_MSC_VER) || defined(__CYGWIN__) || defined(__MINGW32__) || defined( __BCPLUSPLUS__) || defined( __MWERKS__)
    #  if defined( OSG_LIBRARY_STATIC )
        #    define OSGVIEWER_EXPORT
        #  elif defined( OSGVIEWER_LIBRARY )
        #    define OSGVIEWER_EXPORT   __declspec(dllexport)
        #  else
        #    define OSGVIEWER_EXPORT   __declspec(dllimport)
        #endif
#else
        #define OSGVIEWER_EXPORT
#endif

#endif


/**

\namespace osgViewer

The osgViewer library provides high level viewer functionality designed to make it easier to write a range of different types of viewers,
from viewers embedded in existing windows via SimpleViewer, through to highly scalable and flexible Viewer and Composite classes.  A
set of event handlers add functionality to these viewers so that you can rapidly compose the viewer functionality tailored to your needs.
Finally the viewer classes can be adapted to work with a range of different window toolkit API's via GraphicsWindow implementations,
with native Win32, X11 and Carbon implementations on Windows, Unices and OSX respectively, and other window toolkits such as WxWidgets, Qt etc.
*/



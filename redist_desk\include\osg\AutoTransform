/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSG_AUTOTRANSFORM
#define OSG_AUTOTRANSFORM 1

#include <osg/Group>
#include <osg/Transform>
#include <osg/Quat>
#include <osg/Viewport>

namespace osg {

/** AutoTransform is a derived form of Transform that automatically
  * scales or rotates to keep its children aligned with screen coordinates.
*/
class OSG_EXPORT AutoTransform : public Transform
{
    public :
        AutoTransform();

        AutoTransform(const AutoTransform& pat,const CopyOp& copyop=CopyOp::SHALLOW_COPY);

        virtual osg::Object* cloneType() const { return new AutoTransform (); }
        virtual osg::Object* clone(const osg::CopyOp& copyop) const { return new AutoTransform (*this,copyop); }
        virtual bool isSameKindAs(const osg::Object* obj) const { return dynamic_cast<const AutoTransform *>(obj)!=NULL; }
        virtual const char* className() const { return "AutoTransform"; }
        virtual const char* libraryName() const { return "osg"; }

        virtual AutoTransform* asAutoTransform() { return this; }
        virtual const AutoTransform* asAutoTransform() const { return this; }

        inline void setPosition(const Vec3d& pos) { _position = pos; dirtyBound(); }
        inline const Vec3d& getPosition() const { return _position; }


        inline void setRotation(const Quat& quat) { _rotation = quat; dirtyBound(); }
        inline const Quat& getRotation() const { return _rotation; }

        inline void setScale(double scale) { setScale(osg::Vec3(scale,scale,scale)); }

        void setScale(const Vec3d& scale);
        inline const Vec3d& getScale() const { return _scale; }

        void setMinimumScale(double minimumScale) { _minimumScale = minimumScale; }
        double getMinimumScale() const { return _minimumScale; }

        void setMaximumScale(double maximumScale) { _maximumScale = maximumScale; }
        double getMaximumScale() const { return _maximumScale; }

        inline void setPivotPoint(const Vec3d& pivot) { _pivotPoint = pivot; dirtyBound(); }
        inline const Vec3d& getPivotPoint() const { return _pivotPoint; }


        void setAutoUpdateEyeMovementTolerance(float tolerance) { _autoUpdateEyeMovementTolerance = tolerance; }
        float getAutoUpdateEyeMovementTolerance() const { return _autoUpdateEyeMovementTolerance; }


        enum AutoRotateMode
        {
            NO_ROTATION,
            ROTATE_TO_SCREEN,
            ROTATE_TO_CAMERA,
            ROTATE_TO_AXIS
        };

        void setAutoRotateMode(AutoRotateMode mode);

        AutoRotateMode getAutoRotateMode() const { return _autoRotateMode; }

        /** Set the rotation axis for the AutoTransform's child nodes.
          * Only utilized when _autoRotateMode==ROTATE_TO_AXIS. */
        void setAxis(const Vec3& axis);
        /** Get the rotation axis. */
        inline const Vec3& getAxis() const { return _axis; }

        /** This normal defines child Nodes' front face direction when unrotated. */
        void setNormal(const Vec3& normal);
        /** Get the front face direction normal. */
        inline const Vec3& getNormal() const { return _normal; }

        void setAutoScaleToScreen(bool autoScaleToScreen);
        bool getAutoScaleToScreen() const { return _autoScaleToScreen; }

        void setAutoScaleTransitionWidthRatio(float ratio) { _autoScaleTransitionWidthRatio = ratio; }
        float getAutoScaleTransitionWidthRatio() const { return _autoScaleTransitionWidthRatio; }


        virtual bool computeLocalToWorldMatrix(Matrix& matrix,NodeVisitor* nv) const;

        virtual bool computeWorldToLocalMatrix(Matrix& matrix,NodeVisitor* nv) const;

    protected :

        virtual ~AutoTransform() {}

        Vec3d                           _position;
        Vec3d                           _pivotPoint;
        double                          _autoUpdateEyeMovementTolerance;

        AutoRotateMode                  _autoRotateMode;

        bool                            _autoScaleToScreen;

        mutable Quat                    _rotation;
        mutable Vec3d                   _scale;

        double                          _minimumScale;
        double                          _maximumScale;
        double                          _autoScaleTransitionWidthRatio;

        osg::Matrixd computeMatrix(const osg::NodeVisitor* nv) const;

        enum AxisAligned
        {
            AXIAL_ROT_X_AXIS=ROTATE_TO_AXIS+1,
            AXIAL_ROT_Y_AXIS,
            AXIAL_ROT_Z_AXIS,
            CACHE_DIRTY
        };

        Vec3                            _axis;
        Vec3                            _normal;

        // used internally as cache of which what _axis is aligned to help
        // decide which method of rotation to use.
        int                             _cachedMode;
        Vec3                            _side;
        void updateCache();

};

}

#endif

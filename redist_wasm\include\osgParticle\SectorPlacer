/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/
//osgParticle - Copyright (C) 2002 <PERSON>

#ifndef OSGPARTICLE_SECTOR_PLACER
#define OSGPARTICLE_SECTOR_PLACER 1

#include <osgParticle/CenteredPlacer>
#include <osgParticle/Particle>
#include <osgParticle/range>

#include <osg/CopyOp>
#include <osg/Object>
#include <osg/Vec3>
#include <osg/Math>

namespace osgParticle
{

    /**     A sector-shaped particle placer.
        This placer sets the initial position of incoming particle by choosing a random position
        within a circular sector; this sector is defined by three parameters: a <I>center point</I>,
        which is inherited directly from <CODE>osgParticle::CenteredPlacer</CODE>, a range of values
        for <I>radius</I>, and a range of values for the <I>central angle</I> (sometimes called    <B>phi</B>).
    */
    class SectorPlacer: public CenteredPlacer {
    public:
        inline SectorPlacer();
        inline SectorPlacer(const SectorPlacer& copy, const osg::CopyOp& copyop = osg::CopyOp::SHALLOW_COPY);

        /// Get the range of possible values for radius.
        inline const rangef& getRadiusRange() const;

        /// Set the range of possible values for radius.
        inline void setRadiusRange(const rangef& r);

        /// Set the range of possible values for radius.
        inline void setRadiusRange(float r1, float r2);

        /// Get the range of possible values for the central angle.
        inline const rangef& getPhiRange() const;

        /// Set the range of possible values for the central angle.
        inline void setPhiRange(const rangef& r);

        /// Set the range of possible values for the central angle.
        inline void setPhiRange(float r1, float r2);

        META_Object(osgParticle, SectorPlacer);

        /// Place a particle. Do not call it manually.
        inline void place(Particle* P) const;

        /// return the area of the sector
        inline float volume() const;

        /// return the control position
        inline osg::Vec3 getControlPosition() const;

    protected:
        virtual ~SectorPlacer() {}
        SectorPlacer& operator=(const SectorPlacer&) { return *this; }

    private:
        rangef _rad_range;
        rangef _phi_range;
    };

    // INLINE FUNCTIONS

    inline SectorPlacer::SectorPlacer()
    : CenteredPlacer(), _rad_range(0, 1), _phi_range(0, osg::PI*2)
    {
    }

    inline SectorPlacer::SectorPlacer(const SectorPlacer& copy, const osg::CopyOp& copyop)
    : CenteredPlacer(copy, copyop), _rad_range(copy._rad_range), _phi_range(copy._phi_range)
    {
    }

    inline const rangef& SectorPlacer::getRadiusRange() const
    {
        return _rad_range;
    }

    inline const rangef& SectorPlacer::getPhiRange() const
    {
        return _phi_range;
    }

    inline void SectorPlacer::setRadiusRange(const rangef& r)
    {
        _rad_range = r;
    }

    inline void SectorPlacer::setRadiusRange(float r1, float r2)
    {
        _rad_range.minimum = r1;
        _rad_range.maximum = r2;
    }

    inline void SectorPlacer::setPhiRange(const rangef& r)
    {
        _phi_range = r;
    }

    inline void SectorPlacer::setPhiRange(float r1, float r2)
    {
        _phi_range.minimum = r1;
        _phi_range.maximum = r2;
    }

    inline void SectorPlacer::place(Particle* P) const
    {
        float rad = _rad_range.get_random_sqrtf();
        float phi = _phi_range.get_random();

        osg::Vec3 pos(
            getCenter().x() + rad * cosf(phi),
            getCenter().y() + rad * sinf(phi),
            getCenter().z());

        P->setPosition(pos);
    }

    inline float SectorPlacer::volume() const
    {
        return 0.5f * (_phi_range.maximum - _phi_range.minimum) *
               (_rad_range.maximum*_rad_range.maximum - _rad_range.minimum*_rad_range.minimum);
    }

    inline osg::Vec3 SectorPlacer::getControlPosition() const
    {
        return getCenter();
    }

}

#endif

/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSGVIEWER_AcrossAllScreens
#define OSGVIEWER_AcrossAllScreens 1

#include <osgViewer/View>

namespace osgViewer {

class OSGVIEWER_EXPORT AcrossAllScreens : public ViewConfig
{
    public:
        
        AcrossAllScreens() {}
        AcrossAllScreens(const AcrossAllScreens& rhs, const osg::CopyOp& copyop=osg::CopyOp::SHALLOW_COPY): ViewConfig(rhs,copyop) {}
        
        META_Object(osgViewer, AcrossAllScreens);
        
        virtual void configure(osgViewer::View& view) const;
};

}


#endif

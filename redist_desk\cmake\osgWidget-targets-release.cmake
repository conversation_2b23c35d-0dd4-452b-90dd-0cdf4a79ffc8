#----------------------------------------------------------------
# Generated CMake target import file for configuration "Release".
#----------------------------------------------------------------

# Commands may need to know the format version.
set(CMAKE_IMPORT_FILE_VERSION 1)

# Import target "osg3::osgWidget" for configuration "Release"
set_property(TARGET osg3::osgWidget APPEND PROPERTY IMPORTED_CONFIGURATIONS RELEASE)
set_target_properties(osg3::osgWidget PROPERTIES
  IMPORTED_LINK_INTERFACE_LANGUAGES_RELEASE "CXX;RC"
  IMPORTED_LINK_INTERFACE_LIBRARIES_RELEASE "osg3::osg<PERSON><PERSON>wer;osg3::osgGA;osg3::osgText;osg3::osgDB;osg3::osg;osg3::OpenThreads;opengl32"
  IMPORTED_LOCATION_RELEASE "${_IMPORT_PREFIX}/lib/osg202-osgWidget.lib"
  )

list(APPEND _cmake_import_check_targets osg3::osgWidget )
list(APPEND _cmake_import_check_files_for_osg3::osgWidget "${_IMPORT_PREFIX}/lib/osg202-osgWidget.lib" )

# Commands beyond this point should not need to know the version.
set(CMAKE_IMPORT_FILE_VERSION)

/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSG_STENCIL
#define OSG_STENCIL 1

#include <osg/StateAttribute>

namespace osg {

#ifndef GL_INCR_WRAP
#define GL_INCR_WRAP 0x8507
#define GL_DECR_WRAP 0x8508
#endif


/** Encapsulate OpenGL glStencilFunc/Op/Mask functions.
*
*   All functionality except INCR_WRAP and DECR_WRAP is supported by OpenGL 1.1.
*   INCR_WRAP an DECR_WRAP are available since OpenGL 1.4 or when
*   GL_EXT_stencil_wrap extension is present.
*
*   If INCR_WRAP or DECR_WRAP values are used while they are detected to be not supported,
*   the INCR or DECR values are sent to OpenGL instead. 
*
*   OpenGL 2.0 introduced two side stenciling that is available through
*   osg::StencilTwoSided class.
*/
class OSG_EXPORT Stencil : public StateAttribute
{
    public :


        Stencil();

        /** Copy constructor using CopyOp to manage deep vs shallow copy.*/
        Stencil(const Stencil& stencil,const CopyOp& copyop=CopyOp::SHALLOW_COPY):
            StateAttribute(stencil,copyop),
            _func(stencil._func),
            _funcRef(stencil._funcRef),
            _funcMask(stencil._funcMask),
            _sfail(stencil._sfail),
            _zfail(stencil._zfail),
            _zpass(stencil._zpass),
            _writeMask(stencil._writeMask) {}


        META_StateAttribute(osg, Stencil, STENCIL);

        /** return -1 if *this < *rhs, 0 if *this==*rhs, 1 if *this>*rhs.*/
        virtual int compare(const StateAttribute& sa) const
        {
            // check the types are equal and then create the rhs variable
            // used by the COMPARE_StateAttribute_Parameter macros below.
            COMPARE_StateAttribute_Types(Stencil,sa)

            // compare each parameter in turn against the rhs.
            COMPARE_StateAttribute_Parameter(_func)
            COMPARE_StateAttribute_Parameter(_funcRef)
            COMPARE_StateAttribute_Parameter(_funcMask)
            COMPARE_StateAttribute_Parameter(_sfail)
            COMPARE_StateAttribute_Parameter(_zfail)
            COMPARE_StateAttribute_Parameter(_zpass)
            COMPARE_StateAttribute_Parameter(_writeMask)

            return 0; // passed all the above comparison macros, must be equal.
        }

        virtual bool getModeUsage(StateAttribute::ModeUsage& usage) const
        {
            usage.usesMode(GL_STENCIL_TEST);
            return true;
        }

        enum Function
        {
            NEVER = GL_NEVER,
            LESS = GL_LESS,
            EQUAL = GL_EQUAL,
            LEQUAL = GL_LEQUAL,
            GREATER = GL_GREATER,
            NOTEQUAL = GL_NOTEQUAL,
            GEQUAL = GL_GEQUAL,
            ALWAYS = GL_ALWAYS
        };

        inline void setFunction(Function func,int ref,unsigned int mask)
        {
            _func = func;
            _funcRef = ref;
            _funcMask = mask;
        }

        inline void setFunction(Function func) { _func = func; }
        inline Function getFunction() const { return _func; }

        inline void setFunctionRef(int ref) { _funcRef=ref; }
        inline int getFunctionRef() const { return _funcRef; }

        inline void setFunctionMask(unsigned int mask) { _funcMask=mask; }
        inline unsigned int getFunctionMask() const { return _funcMask; }


        enum Operation
        {
            KEEP = GL_KEEP,
            ZERO = GL_ZERO,
            REPLACE = GL_REPLACE,
            INCR = GL_INCR,
            DECR = GL_DECR,
            INVERT = GL_INVERT,
            INCR_WRAP = GL_INCR_WRAP,
            DECR_WRAP = GL_DECR_WRAP
        };

        /** set the operations to apply when the various stencil and depth
          * tests fail or pass.  First parameter is to control the operation
          * when the stencil test fails.  The second parameter is to control the
          * operation when the stencil test passes, but depth test fails. The
          * third parameter controls the operation when both the stencil test
          * and depth pass.  Ordering of parameter is the same as if using
          * glStencilOp(,,).*/
        inline void setOperation(Operation sfail, Operation zfail, Operation zpass)
        {
            _sfail = sfail;
            _zfail = zfail;
            _zpass = zpass;
        }

        /** set the operation when the stencil test fails.*/
        inline void setStencilFailOperation(Operation sfail) { _sfail = sfail; }

        /** get the operation when the stencil test fails.*/
        inline Operation getStencilFailOperation() const { return _sfail; }

        /** set the operation when the stencil test passes but the depth test fails.*/
        inline void setStencilPassAndDepthFailOperation(Operation zfail) { _zfail=zfail; }

        /** get the operation when the stencil test passes but the depth test fails.*/
        inline Operation getStencilPassAndDepthFailOperation() const { return _zfail; }

        /** set the operation when both the stencil test and the depth test pass.*/
        inline void setStencilPassAndDepthPassOperation(Operation zpass) { _zpass=zpass; }

        /** get the operation when both the stencil test and the depth test pass.*/
        inline Operation getStencilPassAndDepthPassOperation() const { return _zpass; }


        inline void setWriteMask(unsigned int mask) { _writeMask = mask; }

        inline unsigned int getWriteMask() const { return _writeMask; }


        virtual void apply(State& state) const;


    protected:

        virtual ~Stencil();

        Function            _func;
        int                 _funcRef;
        unsigned int        _funcMask;

        Operation           _sfail;
        Operation           _zfail;
        Operation           _zpass;

        unsigned int        _writeMask;

};

}

#endif

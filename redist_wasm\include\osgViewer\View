/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSGVIEWER_VIEW
#define OSGVIEWER_VIEW 1

#include <osg/View>

#include <osgUtil/PolytopeIntersector>
#include <osgUtil/LineSegmentIntersector>
#include <osgUtil/UpdateVisitor>
#include <osgUtil/SceneView>

#include <osgGA/CameraManipulator>
#include <osgGA/EventVisitor>
#include <osgGA/EventQueue>
#include <osgGA/Device>

#include <osgViewer/Scene>
#include <osgViewer/ViewerBase>
#include <osgViewer/Keystone>

namespace osgViewer {


/** Base class for View configurations for setting up Camera and Windowing.*/
class OSGVIEWER_EXPORT ViewConfig : public osg::Object
{
    public:

        ViewConfig() {}

        ViewConfig(const ViewConfig& rhs, const osg::CopyOp& copyop=osg::CopyOp::SHALLOW_COPY) : osg::Object(rhs,copyop) {}

        META_Object(osgViewer,ViewConfig);

        /** configure method that is overridden by Config subclasses.*/
        virtual void configure(osgViewer::View& /*view*/) const {}

        /** convenience method for getting the relevant display settings to use.*/
        virtual osg::DisplaySettings* getActiveDisplaySetting(osgViewer::View& view) const;
};


struct OSGVIEWER_EXPORT DepthPartitionSettings : public osg::Referenced
{
    enum DepthMode
    {
        FIXED_RANGE,
        BOUNDING_VOLUME
    };

    DepthPartitionSettings(DepthMode mode=BOUNDING_VOLUME);

    virtual bool getDepthRange(osg::View& view, unsigned int partition, double& zNear, double& zFar);

    DepthMode _mode;
    double _zNear;
    double _zMid;
    double _zFar;
};


/** View holds a single view on a scene, this view may be composed of one or more slave cameras.*/
class OSGVIEWER_EXPORT View : public osg::View, public osgGA::GUIActionAdapter
{
    public:

        View();

        View(const osgViewer::View& view, const osg::CopyOp& copyop=osg::CopyOp::SHALLOW_COPY);

        META_Object(osgViewer,View);

        /** Provide a mechanism for getting the osg::View associated from the GUIActionAdapter.
          * One would use this to case view to osgViewer::View(er) if supported by the subclass.*/
        virtual osg::View* asView() { return this; }

        /** Provide a mechanism for getting the viewer object from this osgViewer::View.
          * In the case of a osgViewer::Viewer the ViewerBase will effectively point to this object as Viewer subclasses from View.
          * In the case of a osgViewer::CompsoiteViewer the ViewerBase will point to the CompositeViewer that owns this View. */
        ViewerBase* getViewerBase() { return _viewerBase.get(); }

        /** Take all the settings, Camera and Slaves from the passed in view, leaving it empty. */
        virtual void take(osg::View& rhs);

        virtual void setStartTick(osg::Timer_t tick);
        osg::Timer_t getStartTick() const { return _startTick; }

        Scene* getScene() { return _scene.get(); }
        const Scene* getScene() const { return _scene.get(); }

        /** Set the scene graph that the View will use.*/
        virtual void setSceneData(osg::Node* node);

        template<class T> void setSceneData(const osg::ref_ptr<T>& node) { setSceneData(node.get()); }

        /** Get the View's scene graph.*/
        osg::Node* getSceneData() { return _scene.valid() ? _scene->getSceneData() : 0; }

        /** Get the const View's scene graph.*/
        const osg::Node* getSceneData() const { return _scene.valid() ? _scene->getSceneData() : 0; }


        /** Set the View's database pager.*/
        void setDatabasePager(osgDB::DatabasePager* dp);

        template<class T> void setDatabasePager(const osg::ref_ptr<T>& dp) { setDatabasePager(dp.get()); }

        /** Get the View's database pager.*/
        osgDB::DatabasePager* getDatabasePager();

        /** Get the const View's database pager.*/
        const osgDB::DatabasePager* getDatabasePager() const;


        /** Set the View's image pager.*/
        void setImagePager(osgDB::ImagePager* ip);

        template<class T> void setImagePager(const osg::ref_ptr<T>& ip) { setImagePager(ip.get()); }

        /** Get the View's image pager.*/
        osgDB::ImagePager* getImagePager();

        /** Get the const View's image pager.*/
        const osgDB::ImagePager* getImagePager() const;


        /** Add a Device.
         * The Device is polled on each new frame via it's Device::checkEvents() method and any events generated then collected via Device::getEventQueue()*/
        void addDevice(osgGA::Device* eventSource);

        template<class T> void addDevice(const osg::ref_ptr<T>& eventSource) { addDevice(eventSource.get()); }

        /** Remove a Device. */
        void removeDevice(osgGA::Device* eventSource);

        template<class T> void removeDevice(const osg::ref_ptr<T>& eventSource) { removeDevice(eventSource.get()); }

        typedef std::vector< osg::ref_ptr<osgGA::Device> > Devices;

        Devices& getDevices() { return _eventSources; }
        const Devices& getDevices() const { return _eventSources; }


        /* Set the EventQueue that the View uses to integrate external non window related events.*/
        void setEventQueue(osgGA::EventQueue* eventQueue) { _eventQueue = eventQueue; }

        template<class T> void setEventQueue(const osg::ref_ptr<T>& eventQueue) { setEventQueue(eventQueue.get()); }

        /* Get the View's EventQueue.*/
        osgGA::EventQueue* getEventQueue() { return _eventQueue.get(); }

        /* Get the const View's EventQueue.*/
        const osgGA::EventQueue* getEventQueue() const { return _eventQueue.get(); }

        /** Set the CameraManipulator that moves the View's master Camera position in response to events.
          * The parameter resetPosition determines whether manipulator is set to its home position.*/
        void setCameraManipulator(osgGA::CameraManipulator* manipulator, bool resetPosition = true);

        template<class T> void setCameraManipulator(const osg::ref_ptr<T>& manipulator, bool resetPosition = true) { setCameraManipulator(manipulator.get(), resetPosition); }

        /** Get the View's CameraManipulator.*/
        osgGA::CameraManipulator* getCameraManipulator() { return _cameraManipulator.get(); }

        /** Get the const View's CameraManipulator.*/
        const osgGA::CameraManipulator* getCameraManipulator() const { return _cameraManipulator.get(); }

        /** Set the view to the CameraManipulator's home position, if none is attached home() it does nothing.
          * Note, to set the home position use getCamaraManipulator()->setHomePosition(...). */
        void home();


        typedef std::list< osg::ref_ptr<osgGA::EventHandler> > EventHandlers;

        /** Add an EventHandler that adds handling of events to the View.*/
        void addEventHandler(osgGA::EventHandler* eventHandler);

        template<class T> void addEventHandler(const osg::ref_ptr<T>& eventHandler) { addEventHandler(eventHandler.get()); }

        /** Remove an EventHandler from View.*/
        void removeEventHandler(osgGA::EventHandler* eventHandler);

        template<class T> void removeEventHandler(const osg::ref_ptr<T>& eventHandler) { removeEventHandler(eventHandler.get()); }

        /** Get the View's list of EventHandlers.*/
        EventHandlers& getEventHandlers() { return _eventHandlers; }

        /** Get the const View's list of EventHandlers.*/
        const EventHandlers& getEventHandlers() const { return _eventHandlers; }


        /** Set the NodePath to any active CoordinateSystemNode present in the Scene.
          * The CoordinateSystemNode path is used to help applications and CamaraManipulators handle geocentric coordinates systems,
          * so that the local up direction is known at any position on the whole earth. */
        void setCoordinateSystemNodePath(const osg::NodePath& nodePath);

        /** Get the NodePath to any active CoordinateSystemNode present in the Scene.*/
        osg::NodePath getCoordinateSystemNodePath() const;

        /** Compute the NodePath to any active CoordinateSystemNode present in the Scene.*/
        void computeActiveCoordinateSystemNodePath();


        /** Set the DisplaySettings object associated with this view.*/
        void setDisplaySettings(osg::DisplaySettings* ds) { _displaySettings = ds; }

        template<class T> void setDisplaySettings(const osg::ref_ptr<T>& ds) { setDisplaySettings(ds.get()); }

        /** Set the DisplaySettings object associated with this view.*/
        osg::DisplaySettings* getDisplaySettings() { return _displaySettings.get(); }

        /** Set the DisplaySettings object associated with this view.*/
        const osg::DisplaySettings* getDisplaySettings() const { return _displaySettings.get(); }

        /** Set the FusionDistanceMode and Value. Note, only used when working in stereo.*/
        void setFusionDistance(osgUtil::SceneView::FusionDistanceMode mode,float value=1.0f)
        {
            _fusionDistanceMode = mode;
            _fusionDistanceValue = value;
        }

        /** Get the FusionDistanceMode.*/
        osgUtil::SceneView::FusionDistanceMode getFusionDistanceMode() const { return _fusionDistanceMode; }

        /** Get the FusionDistanceValue. Note, only used for USE_FUSION_DISTANCE_VALUE & PROPORTIONAL_TO_SCREEN_DISTANCE modes.*/
        float getFusionDistanceValue() const { return _fusionDistanceValue; }


        /** Apply a viewer configuration to set up Cameras and Windowing. */
        void apply(ViewConfig* config);

        template<class T> void apply(const osg::ref_ptr<T>& config) { apply(config.get()); }

        ViewConfig* getLastAppliedViewConfig() { return _lastAppliedViewConfig.get(); }
        const ViewConfig* getLastAppliedViewConfig() const { return _lastAppliedViewConfig.get(); }


        /** deprecated, use view.apply(new osgViewer::AcrossAllScreens()). */
        void setUpViewAcrossAllScreens();

        /** deprecated, use view.apply(new osgViewer::SingleWindow(x,y,width,screenNum)). */
        void setUpViewInWindow(int x, int y, int width, int height, unsigned int screenNum=0);

        /** deprecated, use view.apply(new osgViewer::SingleScreen(screenNum)). */
        void setUpViewOnSingleScreen(unsigned int screenNum=0);

        /** deprecated, use view.apply(new osgViewer::SphericalDisplay(radius, collar, screenNum, intensityMap, projectorMatrix)). */
        void setUpViewFor3DSphericalDisplay(double radius=1.0, double collar=0.45, unsigned int screenNum=0, osg::Image* intensityMap=0, const osg::Matrixd& projectorMatrix = osg::Matrixd());

        /** deprecated, use view.apply(new osgViewer::PanoramicSphericalDisplay(radius, collar, screenNum, intensityMap, projectorMatrix)). */
        void setUpViewForPanoramicSphericalDisplay(double radius=1.0, double collar=0.45, unsigned int screenNum=0, osg::Image* intensityMap=0, const osg::Matrixd& projectorMatrix = osg::Matrixd());

        /** deprecated. use view.apply(new osgViewer::WoWVxDisplay(type (20 to 42), screenNum). */
        void setUpViewForWoWVxDisplay(unsigned int screenNum, unsigned char wow_content, unsigned char wow_factor, unsigned char wow_offset, float wow_disparity_Zd, float wow_disparity_vz, float wow_disparity_M, float wow_disparity_C);



        /** Convenience method for setting up depth partitioning on the specified camera.*/
        bool setUpDepthPartitionForCamera(osg::Camera* cameraToPartition, DepthPartitionSettings* dps=0);

        /** Convenience method for setting up multiple slave cameras with depth partitioning on each of the view's active cameras.*/
        bool setUpDepthPartition(DepthPartitionSettings* dsp=0);


        /** Return true if this view contains a specified camera.*/
        bool containsCamera(const osg::Camera* camera) const;

        template<class T> bool containsCamera(const osg::ref_ptr<T>& camera) const { return containsCamera(camera.get()); }

        /** deprecated. */
        const osg::Camera* getCameraContainingPosition(float x, float y, float& local_x, float& local_y) const;

        /** deprecated. */
        bool computeIntersections(float x,float y, osgUtil::LineSegmentIntersector::Intersections& intersections,osg::Node::NodeMask traversalMask = 0xffffffff);

        /** deprecated. */
        bool computeIntersections(float x,float y, const osg::NodePath& nodePath, osgUtil::LineSegmentIntersector::Intersections& intersections,osg::Node::NodeMask traversalMask = 0xffffffff);


        /** Compute intersections of a ray, starting the current mouse position, through the specified camera. */
        bool computeIntersections(const osgGA::GUIEventAdapter& ea, osgUtil::LineSegmentIntersector::Intersections& intersections,osg::Node::NodeMask traversalMask = 0xffffffff);

        /** Compute intersections of a ray, starting the current mouse position, through the specified master camera's window/eye coordinates and a specified nodePath's subgraph. */
        bool computeIntersections(const osgGA::GUIEventAdapter& ea, const osg::NodePath& nodePath, osgUtil::LineSegmentIntersector::Intersections& intersections,osg::Node::NodeMask traversalMask = 0xffffffff);


        /** Compute intersections of a ray through the specified camera. */
        bool computeIntersections(const osg::Camera* camera, osgUtil::Intersector::CoordinateFrame cf, float x,float y, osgUtil::LineSegmentIntersector::Intersections& intersections,osg::Node::NodeMask traversalMask = 0xffffffff);

        /** Compute intersections of a ray through the specified camera and a specified nodePath's subgraph. */
        bool computeIntersections(const osg::Camera* camera, osgUtil::Intersector::CoordinateFrame cf, float x,float y, const osg::NodePath& nodePath, osgUtil::LineSegmentIntersector::Intersections& intersections,osg::Node::NodeMask traversalMask = 0xffffffff);

        virtual void requestRedraw();
        virtual void requestContinuousUpdate(bool needed=true);
        virtual void requestWarpPointer(float x,float y);

        /** Return true if there are pending updates to the scene graph that require an update. */
        virtual bool requiresUpdateSceneGraph() const;

        /** Return true if there are graphics operations that require a draw of the grpahics context. */
        virtual bool requiresRedraw() const;

public:

        osg::Texture* createDistortionTexture(int width, int height);
        osg::Camera* assignRenderToTextureCamera(osg::GraphicsContext* gc, int width, int height, osg::Texture* texture);
        osg::Camera* assignKeystoneDistortionCamera(osg::DisplaySettings* ds, osg::GraphicsContext* gc, int x, int y, int width, int height, GLenum buffer, osg::Texture* texture, Keystone* keystone);
        osg::Camera* assignStereoCamera(osg::DisplaySettings* ds, osg::GraphicsContext* gc, int x, int y, int width, int height, GLenum buffer, double eyeScale);
        void assignStereoOrKeystoneToCamera(osg::Camera* camera, osg::DisplaySettings* ds);

        struct StereoSlaveCallback : public osg::View::Slave::UpdateSlaveCallback
        {
            StereoSlaveCallback(osg::DisplaySettings* ds, double eyeScale):_ds(ds), _eyeScale(eyeScale) {}

            virtual void updateSlave(osg::View& view, osg::View::Slave& slave);

            osg::ref_ptr<osg::DisplaySettings> _ds;
            double _eyeScale;
        };


    public:

        void assignSceneDataToCameras();
        void init();

    protected:

        friend class CompositeViewer;

        virtual ~View();

        virtual osg::GraphicsOperation* createRenderer(osg::Camera* camera);

        osg::observer_ptr<ViewerBase>          _viewerBase;


        osg::Timer_t                            _startTick;

        Devices                                 _eventSources;

        osg::ref_ptr<osgViewer::Scene>          _scene;
        osg::ref_ptr<osgGA::EventQueue>         _eventQueue;
        osg::ref_ptr<osgGA::CameraManipulator>  _cameraManipulator;
        EventHandlers                           _eventHandlers;

        osg::ObserverNodePath                   _coordinateSystemNodePath;

        osg::ref_ptr<osg::DisplaySettings>      _displaySettings;
        osgUtil::SceneView::FusionDistanceMode  _fusionDistanceMode;
        float                                   _fusionDistanceValue;

        osg::ref_ptr<ViewConfig>                _lastAppliedViewConfig;

};

}

#endif

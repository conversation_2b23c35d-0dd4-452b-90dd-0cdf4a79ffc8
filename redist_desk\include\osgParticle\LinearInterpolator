/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/
//osgParticle - Copyright (C) 2002 <PERSON>

#ifndef OSGPARTICLE_LINEARINTERPOLATOR
#define OSGPARTICLE_LINEARINTERPOLATOR

#include <osgParticle/Interpolator>

#include <osg/CopyOp>
#include <osg/Object>
#include <osg/Vec3>
#include <osg/Vec4>

namespace osgParticle
{

    /// A linear interpolator.
    class LinearInterpolator: public Interpolator
    {
    public:
        LinearInterpolator()
        : Interpolator() {}

        LinearInterpolator(const LinearInterpolator& copy, const osg::CopyOp& copyop = osg::CopyOp::SHALLOW_COPY)
        : Interpolator(copy, copyop) {}

        META_Object(osgParticle, LinearInterpolator);

        virtual float interpolate(float t, float y1, float y2) const
        {
            return y1 + (y2 - y1) * t;
        }

        virtual osg::Vec2 interpolate(float t, const osg::Vec2& y1, const osg::Vec2& y2) const
        {
            return y1 + (y2 - y1) * t;
        }

        virtual osg::Vec3 interpolate(float t, const osg::Vec3& y1, const osg::Vec3& y2) const
        {
            return y1 + (y2 - y1) * t;
        }

        virtual osg::Vec4 interpolate(float t, const osg::Vec4& y1, const osg::Vec4& y2) const
        {
            return y1 + (y2 - y1) * t;
        }

    protected:
        virtual ~LinearInterpolator() {}
    };

}

#endif

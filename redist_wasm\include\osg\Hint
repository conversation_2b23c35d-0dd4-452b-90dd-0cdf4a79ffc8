/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSG_HINT
#define OSG_HINT 1

#include <osg/StateAttribute>

namespace osg
{

class OSG_EXPORT Hint : public StateAttribute
{
public:

        Hint():
            _target(GL_NONE),
            _mode(GL_DONT_CARE) {}

        Hint(GLenum target, GLenum mode):
            _target(target),
            _mode(mode) {}

        /** Copy constructor using CopyOp to manage deep vs shallow copy.*/
        Hint(const Hint& hint,const CopyOp& copyop=CopyOp::SHALLOW_COPY):
            StateAttribute(hint,copyop),
            _target(hint._target),
            _mode(hint._mode) {}

        virtual osg::Object* cloneType() const { return new Hint( _target, GL_DONT_CARE ); }
        virtual osg::Object* clone(const osg::CopyOp& copyop) const { return new Hint(*this,copyop); }
        virtual bool isSameKindAs(const osg::Object* obj) const { return dynamic_cast<const Hint *>(obj)!=NULL; }
        virtual const char* libraryName() const { return "osg"; }
        virtual const char* className() const { return "Hint"; }
        virtual Type getType() const { return HINT; }

        virtual int compare(const StateAttribute& sa) const
        {
        // check the types are equal and then create the rhs variable
        // used by the COMPARE_StateAttribute_Parameter macros below.
        COMPARE_StateAttribute_Types(Hint,sa)

            // compare each parameter in turn against the rhs.
            COMPARE_StateAttribute_Parameter(_target)
            COMPARE_StateAttribute_Parameter(_mode)

            return 0;
        }

        /** Return the member identifier within the attribute's class type. Used for light number/clip plane number etc.*/
        virtual unsigned int getMember() const { return static_cast<unsigned int>(_target); }

        void setTarget(GLenum target);
        inline GLenum getTarget() const { return _target;  }

        inline void setMode(GLenum mode) { _mode = mode; }
        inline GLenum getMode() const { return _mode; }

        virtual void apply(State& state) const;

protected:


        GLenum _target;
        GLenum _mode;
};


}

#endif

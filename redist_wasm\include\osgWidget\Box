/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2008 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

// Code by: <PERSON> (cubicool) 2007-2008

#ifndef OSGWIDGET_BOX
#define OSGWIDGET_BOX

#include <osgWidget/Window>

namespace osgWidget {

//! The Box object is a Window subclass that can be configured to uniformly (or
//! non-uniformly) position it's children either vertically or horizontally. It
//! is the most basic Window implementation, though there is some difficulty when
//! positioning children such that each child object ends up pixel-aligned.
class OSGWIDGET_EXPORT Box: public Window
{
    public:

        //! An enum corresponding to the type of Box alignment.
        enum BoxType {
            VERTICAL,
            HORIZONTAL
        };

        META_Object(osgWidget, Box);

        //! The main constructor; takes the string name, the BoxType orientation, and a
        //! boolean indicating whether or not all of the Box regions should be uniformly
        //! sized.
        Box (const std::string& = "", BoxType = HORIZONTAL, bool = false);
        Box (const Box&, const osg::CopyOp&);

    protected:

        virtual void _resizeImplementation(point_type, point_type);

        virtual Sizes _getWidthImplementation  () const;
        virtual Sizes _getHeightImplementation () const;

    private:

        BoxType      _boxType;
        bool         _uniform;
        unsigned int _lastAdd;
};

}

#endif

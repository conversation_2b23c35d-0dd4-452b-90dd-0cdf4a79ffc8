/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/
#ifndef OSGUTIL_HIGHLIGHTMAPGENERATOR_
#define OSGUTIL_HIGHLIGHTMAPGENERATOR_

#include <osgUtil/Export>

#include <osgUtil/CubeMapGenerator>

namespace osgUtil
{

    /** This cube map generator produces a specular highlight map.
      * The vector-color association is: C = (R dot (-L)) ^ n, where C is the
      * resulting color, R is the reflection vector, L is the light direction
      * and n is the specular exponent.
      */
    class OSGUTIL_EXPORT HighlightMapGenerator: public CubeMapGenerator {
    public:
        HighlightMapGenerator(
            const osg::Vec3 &light_direction,
            const osg::Vec4 &light_color,
            float specular_exponent,
            int texture_size = 64);

        HighlightMapGenerator(const HighlightMapGenerator &copy, const osg::CopyOp &copyop = osg::CopyOp::SHALLOW_COPY);

    protected:
        virtual ~HighlightMapGenerator() {}
        HighlightMapGenerator &operator=(const HighlightMapGenerator &) { return *this; }

        inline virtual osg::Vec4 compute_color(const osg::Vec3 &R) const;

    private:
        osg::Vec3 ldir_;
        osg::Vec4 lcol_;
        float sexp_;
    };

    // INLINE METHODS

    inline osg::Vec4 HighlightMapGenerator::compute_color(const osg::Vec3 &R) const
    {
        float v = -ldir_ * (R / R.length());
        if (v < 0) v = 0;
        osg::Vec4 color(lcol_ * powf(v, sexp_));
        color.w() = 1;
        return color;
    }

}

#endif

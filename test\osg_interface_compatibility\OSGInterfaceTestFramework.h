/* OSG Interface Compatibility Test Framework
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#pragma once

#include <osg/Drawable>
#include <osg/State>
#include <osg/RenderInfo>
#include <osg/GraphicsContext>
#include <osg/Geometry>
#include <osg/Texture2D>
#include <osg/Program>
#include <osg/Shader>
#include <osgViewer/Viewer>

#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <chrono>

namespace OSGCompatibilityTest
{

    /**
     * 测试结果枚举
     */
    enum class TestResult
    {
        PASS,         // 测试通过
        FAIL,         // 测试失败
        SKIP,         // 测试跳过
        NOT_SUPPORTED // 功能不支持
    };

    /**
     * 测试用例基类
     */
    class TestCase
    {
    public:
        TestCase(const std::string &name, const std::string &description)
            : _name(name), _description(description) {}

        virtual ~TestCase() = default;

        // 纯虚函数，子类必须实现
        virtual TestResult runTest(osg::RenderInfo &renderInfo) = 0;

        // 可选的设置和清理函数
        virtual bool setup() { return true; }
        virtual void cleanup() {}

        const std::string &getName() const { return _name; }
        const std::string &getDescription() const { return _description; }

    protected:
        std::string _name;
        std::string _description;
    };

    /**
     * 测试套件 - 管理一组相关的测试用例
     */
    class TestSuite
    {
    public:
        TestSuite(const std::string &name) : _name(name) {}

        void addTest(std::unique_ptr<TestCase> test)
        {
            _tests.push_back(std::move(test));
        }

        struct TestResult_Detail
        {
            std::string testName;
            TestResult result;
            std::string errorMessage;
            double executionTimeMs;
        };

        std::vector<TestResult_Detail> runAllTests(osg::RenderInfo &renderInfo);

        const std::string &getName() const { return _name; }
        size_t getTestCount() const { return _tests.size(); }

    private:
        std::string _name;
        std::vector<std::unique_ptr<TestCase>> _tests;
    };

    /**
     * 测试框架主类
     */
    class OSGInterfaceTestFramework
    {
    public:
        OSGInterfaceTestFramework();
        ~OSGInterfaceTestFramework();

        // 初始化测试环境
        bool initialize();

        // 添加测试套件
        void addTestSuite(std::unique_ptr<TestSuite> suite);

        // 运行所有测试
        void runAllTests();

        // 运行指定的测试套件
        void runTestSuite(const std::string &suiteName);

        // 生成测试报告
        void generateReport(const std::string &filename = "osg_compatibility_report.html");

        // 获取测试统计
        struct TestStatistics
        {
            int totalTests = 0;
            int passedTests = 0;
            int failedTests = 0;
            int skippedTests = 0;
            int notSupportedTests = 0;
            double totalExecutionTimeMs = 0.0;
        };

        TestStatistics getStatistics() const;

    private:
        bool setupGraphicsContext();
        bool setupGraphicsContextSimple(); // WebAssembly简化版本
        void cleanupGraphicsContext();

        osg::ref_ptr<osgViewer::Viewer> _viewer;
        osg::ref_ptr<osg::GraphicsContext> _gc;
        std::vector<std::unique_ptr<TestSuite>> _testSuites;
        std::vector<TestSuite::TestResult_Detail> _allResults;
        bool _initialized = false;
    };

/**
 * 辅助宏定义
 */
#define DECLARE_OSG_TEST(className, testName, description)        \
    class className : public OSGCompatibilityTest::TestCase       \
    {                                                             \
    public:                                                       \
        className() : TestCase(testName, description) {}          \
        TestResult runTest(osg::RenderInfo &renderInfo) override; \
    };

#define IMPLEMENT_OSG_TEST(className) \
    OSGCompatibilityTest::TestResult className::runTest(osg::RenderInfo &renderInfo)

/**
 * 测试断言宏
 */
#define OSG_TEST_ASSERT(condition, message) \
    do                                      \
    {                                       \
        if (!(condition))                   \
        {                                   \
            return TestResult::FAIL;        \
        }                                   \
    } while (0)

#define OSG_TEST_ASSERT_GL_NO_ERROR(message) \
    do                                       \
    {                                        \
        GLenum error = glGetError();         \
        if (error != GL_NO_ERROR)            \
        {                                    \
            return TestResult::FAIL;         \
        }                                    \
    } while (0)

    /**
     * 平台检测辅助函数
     */
    class PlatformInfo
    {
    public:
        static bool isWebAssembly();
        static bool isDesktop();
        static std::string getPlatformName();
        static std::string getOpenGLVersion();
        static std::string getRenderer();
        static std::vector<std::string> getSupportedExtensions();
    };

    /**
     * OpenGL状态检查辅助类
     */
    class GLStateChecker
    {
    public:
        static bool checkGLError(const std::string &operation);
        static bool isExtensionSupported(const std::string &extension);
        static bool checkFramebufferStatus();
        static void logGLInfo();
    };

    /**
     * 性能测量辅助类
     */
    class PerformanceMeasure
    {
    public:
        PerformanceMeasure();
        void start();
        void stop();
        double getElapsedMs() const;

    private:
        std::chrono::high_resolution_clock::time_point _startTime;
        std::chrono::high_resolution_clock::time_point _endTime;
        bool _running = false;
    };

} // namespace OSGCompatibilityTest

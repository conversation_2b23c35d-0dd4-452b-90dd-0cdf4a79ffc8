/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/
//osgFX - Copyright (C) 2003 Marco <PERSON>

#ifndef OSGFX_CARTOON_
#define OSGFX_CARTOON_

#include <osgFX/Export>
#include <osgFX/Effect>

#include <osg/Material>
#include <osg/LineWidth>

namespace osgFX
{

     /**
     This effect implements a technique called 'Cel-Shading' to produce a
     cartoon-style (non photorealistic) rendering. Two passes are required:
     the first one draws solid surfaces, the second one draws the outlines.
     A vertex program is used to setup texture coordinates for a sharp lighting
     texture on unit 0 which is generated on-the-fly.
     This effect requires the ARB_vertex_program extension.
     */
    class OSGFX_EXPORT Cartoon: public Effect {
    public:
        Cartoon();
        Cartoon(const Cartoon& copy, const osg::CopyOp& copyop = osg::CopyOp::SHALLOW_COPY);

        // effect class information
        META_Effect(
            osgFX,
            Cartoon,

            "Cartoon",

            "This effect implements a technique called 'Cel-Shading' to produce a "
            "cartoon-style (non photorealistic) rendering. Two passes are required: "
            "the first one draws solid surfaces, the second one draws the outlines. "
            "A vertex program is used to setup texture coordinates for a sharp lighting "
            "texture on unit 0 which is generated on-the-fly.\n"
            "This effect requires the ARB_vertex_program extension "
            "or OpenGL Shading Language.",

            "Marco Jez; OGLSL port by Mike Weiblen");

        /** get the outline color */
        inline const osg::Vec4& getOutlineColor() const;

        /** set the outline color */
        inline void setOutlineColor(const osg::Vec4& color);

        /** get the outline line width */
        inline float getOutlineLineWidth() const;

        /** set the outline line width */
        inline void setOutlineLineWidth(float w);

        /** get the OpenGL light number */
        inline int getLightNumber() const;

        /** set the OpenGL light number that will be used in lighting computations */
        inline void setLightNumber(int n);

    protected:
        virtual ~Cartoon() {}
        Cartoon& operator=(const Cartoon&) { return *this; }

        bool define_techniques();

    private:
        osg::ref_ptr<osg::Material> _wf_mat;
        osg::ref_ptr<osg::LineWidth> _wf_lw;
        int _lightnum;
    };

    // INLINE METHODS

    inline const osg::Vec4& Cartoon::getOutlineColor() const
    {
        return _wf_mat->getEmission(osg::Material::FRONT_AND_BACK);
    }

    inline void Cartoon::setOutlineColor(const osg::Vec4& color)
    {
        _wf_mat->setEmission(osg::Material::FRONT_AND_BACK, color);
    }

    inline float Cartoon::getOutlineLineWidth() const
    {
        return _wf_lw->getWidth();
    }

    inline void Cartoon::setOutlineLineWidth(float w)
    {
        _wf_lw->setWidth(w);
    }

    inline int Cartoon::getLightNumber() const
    {
        return _lightnum;
    }

    inline void Cartoon::setLightNumber(int n)
    {
        _lightnum = n;
        dirtyTechniques();
    }

}

#endif

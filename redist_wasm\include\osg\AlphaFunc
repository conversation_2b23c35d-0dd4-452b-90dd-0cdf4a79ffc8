/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSG_ALPHAFUNC
#define OSG_ALPHAFUNC 1

#include <osg/StateAttribute>

namespace osg {

/** Encapsulates OpenGL glAlphaFunc.
*/
class OSG_EXPORT AlphaFunc : public StateAttribute
{
    public :

        enum ComparisonFunction {
            NEVER = GL_NEVER,
            LESS = GL_LESS,
            EQUAL = GL_EQUAL,
            LEQUAL = GL_LEQUAL,
            GREATER = GL_GREATER,
            NOTEQUAL = GL_NOTEQUAL,
            GEQUAL = GL_GEQUAL,
            ALWAYS = GL_ALWAYS
        };


        AlphaFunc();

        AlphaFunc(ComparisonFunction func,float ref):
            _comparisonFunc(func),
            _referenceValue(ref) {}

        /** Copy constructor using CopyOp to manage deep vs shallow copy. */
        AlphaFunc(const AlphaFunc& af,const CopyOp& copyop=CopyOp::SHALLOW_COPY):
            StateAttribute(af,copyop),
            _comparisonFunc(af._comparisonFunc),
            _referenceValue(af._referenceValue) {}

        META_StateAttribute(osg, AlphaFunc,ALPHAFUNC);

        /** Return -1 if *this < *rhs, 0 if *this==*rhs, 1 if *this>*rhs. */
        virtual int compare(const StateAttribute& sa) const
        {
            // Check for equal types, then create the rhs variable
            // used by the COMPARE_StateAttribute_Parameter macros below.
            COMPARE_StateAttribute_Types(AlphaFunc,sa)

            // Compare each parameter in turn against the rhs.
            COMPARE_StateAttribute_Parameter(_comparisonFunc)
            COMPARE_StateAttribute_Parameter(_referenceValue)

            return 0; // Passed all the above comparison macros, so must be equal.
        }

        virtual bool getModeUsage(StateAttribute::ModeUsage& usage) const
        {
            usage.usesMode(GL_ALPHA_TEST);
            return true;
        }

        inline void setFunction(ComparisonFunction func,float ref)
        {
            _comparisonFunc = func;
            _referenceValue = ref;
        }

        inline void setFunction(ComparisonFunction func) { _comparisonFunc=func; }
        inline ComparisonFunction getFunction() const { return _comparisonFunc; }

        inline void setReferenceValue(float value) { _referenceValue=value; }
        inline float getReferenceValue() const { return _referenceValue; }

        virtual void apply(State& state) const;

    protected:

        virtual ~AlphaFunc();

        ComparisonFunction _comparisonFunc;
        float _referenceValue;

};

}

#endif

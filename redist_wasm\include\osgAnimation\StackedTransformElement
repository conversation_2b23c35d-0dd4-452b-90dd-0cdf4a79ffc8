/*  -*-c++-*-
 *  Copyright (C) 2009 <PERSON><PERSON> <<EMAIL>>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
 */


#ifndef OSGANIMATION_STACKED_TRANSFORM_ELEMENT
#define OSGANIMATION_STACKED_TRANSFORM_ELEMENT 1

#include <osgAnimation/Export>
#include <osg/Object>
#include <osg/Matrix>

namespace osgAnimation
{
    class Target;
    class OSGANIMATION_EXPORT StackedTransformElement : public osg::Object
    {
    public:
        StackedTransformElement() {}
        StackedTransformElement(const StackedTransformElement& rhs, const osg::CopyOp& c) : osg::Object(rhs, c) {}
        virtual void applyToMatrix(osg::Matrix& matrix) const = 0;
        virtual osg::Matrix getAsMatrix() const = 0;
        virtual bool isIdentity() const = 0;
        virtual void update(float t) = 0;
        virtual Target* getOrCreateTarget() {return 0;}
        virtual Target* getTarget() {return 0;}
        virtual const Target* getTarget() const {return 0;}
    };

}

#endif

/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSG_LOGICOP
#define OSG_LOGICOP 1

#include <osg/StateAttribute>

#ifndef OSG_GL_FIXED_FUNCTION_AVAILABLE
    #define GL_CLEAR            0x1500
    #define GL_SET              0x150F
    #define GL_COPY             0x1503
    #define GL_COPY_INVERTED    0x150C
    #define GL_NOOP             0x1505
    #define GL_AND              0x1501
    #define GL_NAND             0x150E
    #define GL_OR               0x1507
    #define GL_NOR              0x1508
    #define GL_XOR              0x1506
    #define GL_EQUIV            0x1509
    #define GL_AND_REVERSE      0x1502
    #define GL_AND_INVERTED     0x1504
    #define GL_OR_REVERSE       0x150B
    #define GL_OR_INVERTED      0x150D
    #define GL_COLOR_LOGIC_OP   0x0BF2
#endif

namespace osg {

/** Encapsulates OpenGL LogicOp state. */
class OSG_EXPORT LogicOp : public StateAttribute
{
    public :

        enum Opcode {
            CLEAR               = GL_CLEAR,
            SET                 = GL_SET,
            COPY                = GL_COPY,
            COPY_INVERTED       = GL_COPY_INVERTED,
            NOOP                = GL_NOOP,
            INVERT              = GL_INVERT,
            AND                 = GL_AND,
            NAND                = GL_NAND,
            OR                  = GL_OR,
            NOR                 = GL_NOR,
            XOR                 = GL_XOR,
            EQUIV               = GL_EQUIV,
            AND_REVERSE         = GL_AND_REVERSE,
            AND_INVERTED        = GL_AND_INVERTED,
            OR_REVERSE          = GL_OR_REVERSE,
            OR_INVERTED         = GL_OR_INVERTED
        };

        LogicOp();

        LogicOp(Opcode opcode);

        /** Copy constructor using CopyOp to manage deep vs shallow copy. */
        LogicOp(const LogicOp& trans,const CopyOp& copyop=CopyOp::SHALLOW_COPY):
            StateAttribute(trans,copyop),
            _opcode(trans._opcode){}

        META_StateAttribute(osg, LogicOp,LOGICOP);

        /** Return -1 if *this < *rhs, 0 if *this==*rhs, 1 if *this>*rhs. */
        virtual int compare(const StateAttribute& sa) const
        {
            // Check for equal types, then create the rhs variable
            // used by the COMPARE_StateAttribute_Parameter macros below.
            COMPARE_StateAttribute_Types(LogicOp,sa)

            // Compare each parameter in turn against the rhs.
            COMPARE_StateAttribute_Parameter(_opcode)

            return 0; // Passed all the above comparison macros, so must be equal.
        }

        virtual bool getModeUsage(StateAttribute::ModeUsage& usage) const
        {
            usage.usesMode(GL_COLOR_LOGIC_OP);
            return true;
        }


        inline void setOpcode(Opcode opcode)
        {
            _opcode = opcode;
        }

        inline Opcode getOpcode() const { return _opcode; }

        virtual void apply(State& state) const;

    protected :

        virtual ~LogicOp();

        Opcode _opcode;
};

}

#endif

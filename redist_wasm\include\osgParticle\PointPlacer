/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/
//osgParticle - Copyright (C) 2002 <PERSON>

#ifndef OSGPARTICLE_POINT_PLACER
#define OSGPARTICLE_POINT_PLACER 1

#include <osgParticle/CenteredPlacer>
#include <osgParticle/Particle>

#include <osg/CopyOp>
#include <osg/Object>

namespace osgParticle
{

    /**    A point-shaped particle placer.
        This placer class uses the center point defined in its base class <CODE>CenteredPlacer</CODE>
        to place there all incoming particles.
    */
    class PointPlacer: public CenteredPlacer {
    public:
        inline PointPlacer();
        inline PointPlacer(const PointPlacer& copy, const osg::CopyOp& copyop = osg::CopyOp::SHALLOW_COPY);

        META_Object(osgParticle, PointPlacer);

        /** Place a particle.
            This method is called automatically by <CODE>ModularEmitter</CODE> and should not be called
            manually.
        */
        inline void place(Particle* P) const;

        /// return the control position
        inline osg::Vec3 getControlPosition() const;

    protected:
        virtual ~PointPlacer() {}
        PointPlacer& operator=(const PointPlacer&) { return *this; }
    };

    // INLINE FUNCTIONS

    inline PointPlacer::PointPlacer()
    : CenteredPlacer()
    {
    }

    inline PointPlacer::PointPlacer(const PointPlacer& copy, const osg::CopyOp& copyop)
    : CenteredPlacer(copy, copyop)
    {
    }

    inline void PointPlacer::place(Particle* P) const
    {
        P->setPosition(getCenter());
    }


    inline osg::Vec3 PointPlacer::getControlPosition() const
    {
        return getCenter();
    }

}


#endif

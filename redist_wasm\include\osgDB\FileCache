/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSGDB_FILECACHE
#define OSGDB_FILECACHE 1

#include <osg/Node>

#include <osgDB/ReaderWriter>
#include <osgDB/DatabaseRevisions>

#include <set>

namespace osgDB {

class OSGDB_EXPORT FileCache : public osg::Referenced
{
    public:

        FileCache(const std::string& path);

        const std::string& getFileCachePath() const { return _fileCachePath; }

        virtual bool isFileAppropriateForFileCache(const std::string& originalFileName) const;

        virtual std::string createCacheFileName(const std::string& originalFileName) const;

        virtual bool existsInCache(const std::string& originalFileName) const;

        virtual ReaderWriter::ReadResult readImage(const std::string& originalFileName, const osgDB::Options* options) const;
        virtual ReaderWriter::WriteResult writeImage(const osg::Image& image, const std::string& originalFileName, const osgDB::Options* options) const;

        virtual ReaderWriter::ReadResult readObject(const std::string& originalFileName, const osgDB::Options* options) const;
        virtual ReaderWriter::WriteResult writeObject(const osg::Object& object, const std::string& originalFileName, const osgDB::Options* options) const;

        virtual ReaderWriter::ReadResult readHeightField(const std::string& originalFileName, const osgDB::Options* options) const;
        virtual ReaderWriter::WriteResult writeHeightField(const osg::HeightField& hf, const std::string& originalFileName, const osgDB::Options* options) const;

        virtual ReaderWriter::ReadResult readNode(const std::string& originalFileName, const osgDB::Options* options, bool buildKdTreeIfRequired=true) const;
        virtual ReaderWriter::WriteResult writeNode(const osg::Node& node, const std::string& originalFileName, const osgDB::Options* options) const;

        virtual ReaderWriter::ReadResult readShader(const std::string& originalFileName, const osgDB::Options* options) const;
        virtual ReaderWriter::WriteResult writeShader(const osg::Shader& shader, const std::string& originalFileName, const osgDB::Options* options) const;

        bool loadDatabaseRevisionsForFile(const std::string& originanlFileName);

        typedef std::list< osg::ref_ptr<DatabaseRevisions> > DatabaseRevisionsList;
        DatabaseRevisionsList& getDatabaseRevisionsList() { return _databaseRevisionsList; }

        bool isCachedFileBlackListed(const std::string& originalFileName) const;

    protected:

        virtual ~FileCache();

        std::string _fileCachePath;

        DatabaseRevisionsList _databaseRevisionsList;

        FileList* readFileList(const std::string& originalFileName) const;
        bool removeFileFromBlackListed(const std::string& originalFileName) const;

};

}

#endif

/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
*
* This library is open source and may be redistributed and/or modified under
* the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
* (at your option) any later version.  The full license is in LICENSE file
* included with this distribution, and on the openscenegraph.org website.
*
* This library is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* OpenSceneGraph Public License for more details.
*/

#ifndef OSGVIEWER_VIEWEREVENTHANDLERS
#define OSGVIEWER_VIEWEREVENTHANDLERS 1

#include <osg/AnimationPath>
#include <osgText/Text>
#include <osgGA/GUIEventHandler>
#include <osgGA/AnimationPathManipulator>

#include <osgViewer/GraphicsWindow>
#include <osgViewer/Viewer>

#include <osgDB/fstream>

namespace osgViewer {

/** Event handler for adding on screen help to Viewers.*/
class OSGVIEWER_EXPORT HelpHandler : public osgGA::GUIEventHandler
{
    public:

        HelpHandler(osg::ApplicationUsage* au=0);

        void setApplicationUsage(osg::ApplicationUsage* au) { _applicationUsage = au; }
        osg::ApplicationUsage* getApplicationUsage() { return _applicationUsage.get(); }
        const osg::ApplicationUsage* getApplicationUsage() const { return _applicationUsage.get(); }

        void setKeyEventTogglesOnScreenHelp(int key) { _keyEventTogglesOnScreenHelp = key; }
        int getKeyEventTogglesOnScreenHelp() const { return _keyEventTogglesOnScreenHelp; }

        void reset();

        osg::Camera* getCamera() { return _camera.get(); }
        const osg::Camera* getCamera() const { return _camera.get(); }

        bool handle(const osgGA::GUIEventAdapter& ea, osgGA::GUIActionAdapter& aa);

        /** Get the keyboard and mouse usage of this manipulator.*/
        virtual void getUsage(osg::ApplicationUsage& usage) const;

    protected:

        void setUpHUDCamera(osgViewer::ViewerBase* viewer);

        void setUpScene(osgViewer::ViewerBase* viewer);

        osg::ref_ptr<osg::ApplicationUsage> _applicationUsage;

        int                                 _keyEventTogglesOnScreenHelp;

        bool                                _helpEnabled;

        bool                                _initialized;
        osg::ref_ptr<osg::Camera>           _camera;
        osg::ref_ptr<osg::Switch>           _switch;

};

/**
 * Event handler for adding on screen stats reporting to Viewers.
 */
class OSGVIEWER_EXPORT StatsHandler : public osgGA::GUIEventHandler
{
    public:

        StatsHandler();

        enum StatsType
        {
            NO_STATS = 0,
            FRAME_RATE = 1,
            VIEWER_STATS = 2,
            CAMERA_SCENE_STATS = 3,
            VIEWER_SCENE_STATS = 4,
            LAST = 5
        };

        void setKeyEventTogglesOnScreenStats(int key) { _keyEventTogglesOnScreenStats = key; }
        int getKeyEventTogglesOnScreenStats() const { return _keyEventTogglesOnScreenStats; }

        void setKeyEventPrintsOutStats(int key) { _keyEventPrintsOutStats = key; }
        int getKeyEventPrintsOutStats() const { return _keyEventPrintsOutStats; }

        double getBlockMultiplier() const { return _blockMultiplier; }

        void reset();

        osg::Camera* getCamera() { return _camera.get(); }
        const osg::Camera* getCamera() const { return _camera.get(); }

        virtual void collectWhichCamerasToRenderStatsFor(osgViewer::ViewerBase* viewer, osgViewer::ViewerBase::Cameras& cameras);

        virtual bool handle(const osgGA::GUIEventAdapter& ea, osgGA::GUIActionAdapter& aa);

        /** Get the keyboard and mouse usage of this manipulator.*/
        virtual void getUsage(osg::ApplicationUsage& usage) const;

        /** This allows the user to register additional stats lines that will
            be added to the graph. The stats for these will be gotten from the
            viewer (viewer->getViewerStats()). The stats can be displayed in
            either or all of the following ways:

            - A numeric time beside the stat name
              Requires that an elapsed time be recorded in the viewer's stats
              for the "timeTakenName".
            - A bar in the top bar graph
              Requires that two times (relative to the viewer's start tick) be
              recorded in the viewer's stats for the "beginTimeName" and
              "endTimeName".
            - A line in the bottom graph
              Requires that an elapsed time be recorded in the viewer's stats
              for the "timeTakenName" and that the value be used as an average.

            If you don't want a numeric value and a line in the bottom line
            graph for your value, pass the empty string for timeTakenName. If
            you don't want a bar in the graph, pass the empty string for
            beginTimeName and endTimeName.

            @param label                 The label to be displayed to identify the line in the stats.
            @param textColor             Will be used for the text label, the numeric time and the bottom line graph.
            @param barColor              Will be used for the bar in the top bar graph.
            @param timeTakenName         The name to be queried in the viewer stats for the numeric value (also used for the bottom line graph).
            @param multiplier            The multiplier to apply to the numeric value before displaying it in the stats.
            @param average               Whether to use the average value of the numeric value.
            @param averageInInverseSpace Whether to average in inverse space (used for frame rate).
            @param beginTimeName         The name to be queried in the viewer stats for the begin time for the top bar graph.
            @param endTimeName           The name to be queried in the viewer stats for the end time for the top bar graph.
            @param maxValue              The value to use as maximum in the bottom line graph. Stats will be clamped between 0 and that value, and it will be the highest visible value in the graph.
          */
        void addUserStatsLine(const std::string& label, const osg::Vec4& textColor, const osg::Vec4& barColor,
                              const std::string& timeTakenName, float multiplier, bool average, bool averageInInverseSpace,
                              const std::string& beginTimeName, const std::string& endTimeName, float maxValue);

        void removeUserStatsLine(const std::string& label);

protected:

        void setUpHUDCamera(osgViewer::ViewerBase* viewer);
        void setWindowSize(int width, int height);

        osg::Geometry* createBackgroundRectangle(const osg::Vec3& pos, const float width, const float height, osg::Vec4& color);

        osg::Geometry* createGeometry(const osg::Vec3& pos, float height, const osg::Vec4& colour, unsigned int numBlocks);

        osg::Geometry* createFrameMarkers(const osg::Vec3& pos, float height, const osg::Vec4& colour, unsigned int numBlocks);

        osg::Geometry* createTick(const osg::Vec3& pos, float height, const osg::Vec4& colour, unsigned int numTicks);

        void createTimeStatsLine(const std::string& lineLabel, osg::Vec3 pos,
                                 const osg::Vec4& textColor, const osg::Vec4& barColor, osg::Stats* viewerStats, osg::Stats* stats,
                                 const std::string& timeTakenName, float multiplier, bool average, bool averageInInverseSpace,
                                 const std::string& beginTimeName, const std::string& endTimeName);

        void createCameraTimeStats(osg::Vec3& pos, bool acquireGPUStats, osg::Stats* viewerStats, osg::Camera* camera);

        void setUpScene(osgViewer::ViewerBase* viewer);

        void updateThreadingModelText();

        int                                 _keyEventTogglesOnScreenStats;
        int                                 _keyEventPrintsOutStats;

        int                                 _statsType;

        bool                                _initialized;
        osg::ref_ptr<osg::Camera>           _camera;

        osg::ref_ptr<osg::Switch>           _switch;

        osg::ref_ptr<osg::Geode>            _statsGeode;

        ViewerBase::ThreadingModel          _threadingModel;
        osg::ref_ptr<osgText::Text>         _threadingModelText;

        unsigned int                        _frameRateChildNum;
        unsigned int                        _viewerChildNum;
        unsigned int                        _cameraSceneChildNum;
        unsigned int                        _viewerSceneChildNum;
        unsigned int                        _numBlocks;
        double                              _blockMultiplier;

        float                               _statsWidth;
        float                               _statsHeight;

        std::string                         _font;
        float                               _startBlocks;
        float                               _leftPos;
        float                               _characterSize;
        float                               _lineHeight;

        struct UserStatsLine
        {
            std::string label;
            osg::Vec4 textColor;
            osg::Vec4 barColor;
            std::string timeTakenName;
            float multiplier;
            bool average;
            bool averageInInverseSpace;
            std::string beginTimeName;
            std::string endTimeName;
            float maxValue;

            UserStatsLine(const std::string& label_, const osg::Vec4& textColor_, const osg::Vec4& barColor_,
                          const std::string& timeTakenName_, float multiplier_, bool average_, bool averageInInverseSpace_,
                          const std::string& beginTimeName_, const std::string& endTimeName_, float maxValue_)
                : label(label_), textColor(textColor_), barColor(barColor_),
                  timeTakenName(timeTakenName_), multiplier(multiplier_), average(average_), averageInInverseSpace(averageInInverseSpace_),
                  beginTimeName(beginTimeName_), endTimeName(endTimeName_), maxValue(maxValue_)
            {
            }
        };

        typedef std::vector<UserStatsLine>  UserStatsLines;
        UserStatsLines                      _userStatsLines;

};

/** Event handler allowing to change the screen resolution (in windowed mode) and toggle between fullscreen and windowed mode. */
class OSGVIEWER_EXPORT WindowSizeHandler : public osgGA::GUIEventHandler
{
public:

        WindowSizeHandler();

        /** Get the keyboard and mouse usage of this manipulator.*/
        virtual void getUsage(osg::ApplicationUsage &usage) const;

        void setKeyEventToggleFullscreen(int key) { _keyEventToggleFullscreen = key; }
        int getKeyEventToggleFullscreen() const { return _keyEventToggleFullscreen; }

        void setToggleFullscreen(bool flag) { _toggleFullscreen = flag; }
        bool getToggleFullscreen() const { return _toggleFullscreen; }

        void setKeyEventWindowedResolutionUp(int key) { _keyEventWindowedResolutionUp = key; }
        int getKeyEventWindowedResolutionUp() const { return _keyEventWindowedResolutionUp; }
        void setKeyEventWindowedResolutionDown(int key) { _keyEventWindowedResolutionDown = key; }
        int getKeyEventWindowedResolutionDown() const { return _keyEventWindowedResolutionDown; }

        void setChangeWindowedResolution(bool flag) { _changeWindowedResolution = flag; }
        bool getChangeWindowedResolution() const { return _changeWindowedResolution; }

        virtual bool handle(const osgGA::GUIEventAdapter &ea, osgGA::GUIActionAdapter &aa);

protected:

        void toggleFullscreen(osgViewer::GraphicsWindow *window);
        void changeWindowedResolution(osgViewer::GraphicsWindow *window, bool increase);

        unsigned int getNearestResolution(int screenWidth, int screenHeight, int width, int height) const;

        int                     _keyEventToggleFullscreen;
        bool                    _toggleFullscreen;

        int                     _keyEventWindowedResolutionUp;
        int                     _keyEventWindowedResolutionDown;
        bool                    _changeWindowedResolution;
        std::vector<osg::Vec2>  _resolutionList;
        int                     _currentResolutionIndex;
};

/** Event handler allowing to change the viewer threading model */
class OSGVIEWER_EXPORT ThreadingHandler : public osgGA::GUIEventHandler
{
public:

        ThreadingHandler();

        /** Get the keyboard and mouse usage of this manipulator.*/
        virtual void getUsage(osg::ApplicationUsage &usage) const;

        void setKeyEventChangeThreadingModel(int key) { _keyEventChangeThreadingModel = key; }
        int getKeyEventChangeThreadingModel() const { return _keyEventChangeThreadingModel; }

        void setChangeThreadingModel(bool flag) { _changeThreadingModel = flag; }
        bool getChangeThreadingModel() const { return _changeThreadingModel; }

        void setKeyEventChangeEndBarrierPosition(int key) { _keyEventChangeEndBarrierPosition = key; }
        int getKeyEventChangeEndBarrierPosition() const { return _keyEventChangeEndBarrierPosition; }

        void setChangeEndBarrierPosition(bool flag) { _changeEndBarrierPosition = flag; }
        bool getChangeEndBarrierPosition() const { return _changeEndBarrierPosition; }

        bool handle(const osgGA::GUIEventAdapter &ea, osgGA::GUIActionAdapter &aa);

protected:

        int             _keyEventChangeThreadingModel;
        bool            _changeThreadingModel;

        int             _keyEventChangeEndBarrierPosition;
        bool            _changeEndBarrierPosition;

        osg::Timer_t    _tickOrLastKeyPress;
};

/**
 * Event handler allowing the user to record the animation "path" of a camera. In it's current
 * implementation, this handler cannot guarantee the final view matrix is correct; it is
 * conceivable that the matrix may be one frame off. Eh--not a big deal! :)
 * TODO: Write the file as we go, not when it's all done.
 * TODO: Create an osgviewer on-screen indication that animation is taking place.
*/
class OSGVIEWER_EXPORT RecordCameraPathHandler : public osgGA::GUIEventHandler
{
public:

        RecordCameraPathHandler(const std::string &filename = "saved_animation.path", float fps = 25.0f);

        void setKeyEventToggleRecord(int key) { _keyEventToggleRecord = key; }
        int getKeyEventToggleRecord() const { return _keyEventToggleRecord; }

        void setKeyEventTogglePlayback(int key) { _keyEventTogglePlayback = key; }
        int getKeyEventTogglePlayback() const { return _keyEventTogglePlayback; }

        void setAutoIncrementFilename( bool autoinc = true ) { _autoinc = autoinc?0:-1; }

        virtual void getUsage(osg::ApplicationUsage &usage) const;

        bool handle(const osgGA::GUIEventAdapter &ea, osgGA::GUIActionAdapter &aa);

protected:

        std::string                                     _filename;
        int                                             _autoinc;
        osgDB::ofstream                                 _fout;

        int                                             _keyEventToggleRecord;
        int                                             _keyEventTogglePlayback;


        bool                                            _currentlyRecording;
        bool                                            _currentlyPlaying;
        double                                          _interval;
        double                                          _delta;
        osg::Timer_t                                    _animStartTime;
        osg::Timer_t                                    _lastFrameTime;
        osg::ref_ptr<osg::AnimationPath>                _animPath;
        osg::ref_ptr<osgGA::AnimationPathManipulator>   _animPathManipulator;
        osg::ref_ptr<osgGA::CameraManipulator>          _oldManipulator;
};

/** Event handler for increase/decreasing LODScale.*/
class OSGVIEWER_EXPORT LODScaleHandler : public osgGA::GUIEventHandler
{
    public:

        LODScaleHandler();

        void setKeyEventIncreaseLODScale(int key) { _keyEventIncreaseLODScale = key; }
        int getKeyEventIncreaseLODScale() const { return _keyEventIncreaseLODScale; }

        void setKeyEventDecreaseLODScale(int key) { _keyEventDecreaseLODScale = key; }
        int getKeyEventDecreaseLODScale() const { return _keyEventDecreaseLODScale; }

        bool handle(const osgGA::GUIEventAdapter& ea, osgGA::GUIActionAdapter& aa);

        /** Get the keyboard and mouse usage of this manipulator.*/
        virtual void getUsage(osg::ApplicationUsage& usage) const;

    protected:


        int _keyEventIncreaseLODScale;
        int _keyEventDecreaseLODScale;


};

/** Event handler for toggling SyncToVBlank.*/
class OSGVIEWER_EXPORT ToggleSyncToVBlankHandler : public osgGA::GUIEventHandler
{
    public:

        ToggleSyncToVBlankHandler();

        void setKeyEventToggleSyncToVBlankHandler(int key) { _keyEventToggleSyncToVBlank = key; }
        int getKeyEventToggleSyncToVBlankHandler() const { return _keyEventToggleSyncToVBlank; }

        bool handle(const osgGA::GUIEventAdapter& ea, osgGA::GUIActionAdapter& aa);

        /** Get the keyboard and mouse usage of this manipulator.*/
        virtual void getUsage(osg::ApplicationUsage& usage) const;

    protected:


        int _keyEventToggleSyncToVBlank;


};


/** Event handler that will capture the screen on key press. */
class OSGVIEWER_EXPORT ScreenCaptureHandler : public osgGA::GUIEventHandler
{
    public:

        /** Abstract base class for what to do when a screen capture happens. */
        class CaptureOperation : public osg::Referenced
        {
            public:
                virtual void operator()(const osg::Image& image, const unsigned int context_id) = 0;
        };

        /** Concrete implementation of a CaptureOperation that writes the screen capture to a file. */
        class OSGVIEWER_EXPORT WriteToFile : public CaptureOperation
        {
            public:
                enum SavePolicy
                {
                    OVERWRITE,
                    SEQUENTIAL_NUMBER
                    // ... any others?
                };

                WriteToFile(const std::string& filename, const std::string& extension, SavePolicy savePolicy = SEQUENTIAL_NUMBER);

                virtual void operator()(const osg::Image& image, const unsigned int context_id);

                void setSavePolicy(SavePolicy savePolicy) { _savePolicy = savePolicy; }
                SavePolicy getSavePolicy() const { return _savePolicy; }

            protected:

                WriteToFile& operator = (const WriteToFile&) { return *this; }

                const std::string _filename;
                const std::string _extension;

                SavePolicy _savePolicy;

                std::vector<unsigned int> _contextSaveCounter;
        };

        /** @param defaultOperation : operation to do when screen capture happens. */
        /** @param numFrames >0: capture that number of frames. <0: capture all frames, call stopCapture() to stop it. */
        ScreenCaptureHandler(CaptureOperation* defaultOperation = 0, int numFrames = 1);

        void setKeyEventTakeScreenShot(int key) { _keyEventTakeScreenShot = key; }
        int getKeyEventTakeScreenShot() const { return _keyEventTakeScreenShot; }

        void setKeyEventToggleContinuousCapture(int key) { _keyEventToggleContinuousCapture = key; }
        int getKeyEventToggleContinuousCapture() const { return _keyEventToggleContinuousCapture; }

        void setCaptureOperation(CaptureOperation* operation);
        CaptureOperation* getCaptureOperation() const;

        // aa will point to an osgViewer::View, so we will take a screenshot
        // of that view's graphics contexts.
        virtual bool handle(const osgGA::GUIEventAdapter& ea, osgGA::GUIActionAdapter& aa);

        /** Capture the given viewer's views on the next frame. */
        virtual void captureNextFrame(osgViewer::ViewerBase& viewer);

        /** Set the number of frames to capture.
            @param numFrames >0: capture that number of frames. <0: capture all frames, call stopCapture() to stop it. */
        void setFramesToCapture(int numFrames);

        /** Get the number of frames to capture. */
        int getFramesToCapture() const;

        /** Start capturing any viewer(s) the handler is attached to at the
            end of the next frame. */
        void startCapture();

        /** Stop capturing. */
        void stopCapture();

        /** Get the keyboard and mouse usage of this manipulator.*/
        virtual void getUsage(osg::ApplicationUsage& usage) const;

    protected:
        bool _startCapture;
        bool _stopCapture;

        int _keyEventTakeScreenShot;
        int _keyEventToggleContinuousCapture;
        // there could be a key to start taking screenshots every new frame

        osg::ref_ptr<CaptureOperation>          _operation;
        osg::ref_ptr<osg::Camera::DrawCallback> _callback;

        void addCallbackToViewer(osgViewer::ViewerBase& viewer);
        void removeCallbackFromViewer(osgViewer::ViewerBase& viewer);
        osg::Camera* findAppropriateCameraForCallback(osgViewer::ViewerBase& viewer);
};

/** InteractiveImage is an event handler that computes the mouse coordinates in an images coordinate frame
  * and then passes keyboard and mouse events to it.  This event handler is useful for vnc or browser
  * surfaces in the 3D scene.*/
class OSGVIEWER_EXPORT InteractiveImageHandler : public osgGA::GUIEventHandler, public osg::DrawableCullCallback
{
public:

    /// Constructor to use when the InteractiveImage is in the 3D scene (i.e. not in a fullscreen HUD overlay).
    InteractiveImageHandler(osg::Image* image);
    /// Constructor to use when the InteractiveImage is in a fullscreen HUD overlay.
    InteractiveImageHandler(osg::Image* image, osg::Texture2D* texture, osg::Camera* camera);

    META_Object(osgViewer, InteractiveImageHandler);

    virtual NodeCallback* asNodeCallback() { return osg::NodeCallback::asNodeCallback(); }
    virtual const NodeCallback* asNodeCallback() const { return osg::NodeCallback::asNodeCallback(); }

    virtual DrawableEventCallback* asDrawableEventCallback() { return osg::DrawableEventCallback::asDrawableEventCallback(); }
    virtual const DrawableEventCallback* asDrawableEventCallback() const { return osg::DrawableEventCallback::asDrawableEventCallback(); }

    virtual DrawableCullCallback* asDrawableCullCallback() { return osg::DrawableCullCallback::asDrawableCullCallback(); }
    virtual const DrawableCullCallback* asDrawableCullCallback() const { return osg::DrawableCullCallback::asDrawableCullCallback(); }

    virtual osgGA::EventHandler* asEventHandler() { return osgGA::EventHandler::asEventHandler(); }
    virtual const osgGA::EventHandler* asEventHandler() const { return osgGA::EventHandler::asEventHandler(); }

	// use the osgGA::GUIEventHandler implementation of run.
    virtual bool run(osg::Object* object, osg::Object* data) { return osgGA::GUIEventHandler::run(object, data); }

    virtual bool handle(const osgGA::GUIEventAdapter& ea,osgGA::GUIActionAdapter& aa, osg::Object*, osg::NodeVisitor* nv);

    virtual bool cull(osg::NodeVisitor* nv, osg::Drawable* drawable, osg::RenderInfo* renderInfo) const;

protected:

    virtual ~InteractiveImageHandler() {}

    InteractiveImageHandler():
        _fullscreen(false) {}

    InteractiveImageHandler(const InteractiveImageHandler&,const osg::CopyOp& = osg::CopyOp::SHALLOW_COPY):
        osg::Object(), osg::Callback(), osgGA::GUIEventHandler(), osg::DrawableCullCallback(), _fullscreen(false) {}

    bool mousePosition(osgViewer::View* view, osg::NodeVisitor* nv, const osgGA::GUIEventAdapter& ea, int& x, int &y) const;

    void resize(int width, int height);

    osg::observer_ptr<osg::Image>   _image;
    osg::observer_ptr<osg::Texture2D> _texture;

    bool                            _fullscreen;
    osg::observer_ptr<osg::Camera>  _camera;

};

}

#endif

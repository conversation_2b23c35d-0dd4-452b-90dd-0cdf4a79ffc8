/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSGTERRAIN_VALIDDATAOPERATOR
#define OSGTERRAIN_VALIDDATAOPERATOR 1

#include <osg/Referenced>
#include <osg/Vec2>
#include <osg/Vec3>
#include <osg/Vec4>
#include <osgTerrain/Export>

namespace osgTerrain {

struct ValidDataOperator : public osg::Referenced
{
    virtual bool operator() (float /*value*/) const { return true; }
    virtual bool operator() (const osg::Vec2& value) const { return operator()(value.x()) && operator()(value.y()) ; }
    virtual bool operator() (const osg::Vec3& value) const { return operator()(value.x()) && operator()(value.y()) && operator()(value.z());  }
    virtual bool operator() (const osg::Vec4& value) const { return operator()(value.x()) && operator()(value.y()) && operator()(value.z()) && operator()(value.w()); }
};

struct ValidRange : public ValidDataOperator
{
    ValidRange(float minValue, float maxValue):
        _minValue(minValue),
        _maxValue(maxValue) {}

    void setRange(float minValue, float maxValue)
    {
        _minValue = minValue;
        _maxValue = maxValue;
    }

    void setMinValue(float minValue) { _minValue = minValue; }
    float getMinValue() const { return _minValue; }

    void setMaxValue(float maxValue) { _maxValue = maxValue; }
    float getMaxValue() const { return _maxValue; }

    virtual bool operator() (float value) const { return value>=_minValue && value<=_maxValue; }

    float _minValue, _maxValue;
};


struct NoDataValue : public ValidDataOperator
{
    NoDataValue(float value):
        _value(value) {}

    void setNoDataValue(float value) { _value = value; }
    float getValue() const { return _value; }

    virtual bool operator() (float value) const { return value!=_value; }

    float _value;
};

}

#endif



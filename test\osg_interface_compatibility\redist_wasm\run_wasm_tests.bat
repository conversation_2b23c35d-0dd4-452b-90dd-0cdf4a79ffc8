@echo off
echo ========================================
echo OSG接口兼容性测试 - WebAssembly版
echo ========================================

:: 检查Python是否可用
python --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo [错误] Python未安装或不在PATH中
    echo 请安装Python 3.x并确保python命令可用
    pause
    exit /b 1
)

:: 检查测试文件是否存在
if not exist "osg_compatibility_test.html" (
    echo [错误] 找不到测试文件 osg_compatibility_test.html
    echo 请确认您在正确的目录中运行此脚本
    pause
    exit /b 1
)

echo 检查测试文件...
if exist "osg_compatibility_test.html" echo   ✓ osg_compatibility_test.html
if exist "osg_compatibility_test.js" echo   ✓ osg_compatibility_test.js
if exist "osg_compatibility_test.wasm" echo   ✓ osg_compatibility_test.wasm

echo.
echo ========================================
echo 重要提醒：配置浏览器代理
echo ========================================
echo.
echo 在运行测试前，请确保浏览器已配置代理：
echo   HTTP代理:  127.0.0.1:10809
echo   HTTPS代理: 127.0.0.1:10809
echo.
echo Chrome配置方法：
echo   设置 → 高级 → 系统 → 打开代理设置
echo.
echo Firefox配置方法：
echo   设置 → 网络设置 → 手动代理配置
echo.

:: 创建代理配置说明文件
echo ^{> proxy_config.json
echo   "proxy_settings": {>> proxy_config.json
echo     "http": "127.0.0.1:10809",>> proxy_config.json
echo     "https": "127.0.0.1:10809">> proxy_config.json
echo   },>> proxy_config.json
echo   "instructions": {>> proxy_config.json
echo     "chrome": "设置 → 高级 → 系统 → 打开代理设置",>> proxy_config.json
echo     "firefox": "设置 → 网络设置 → 手动代理配置">> proxy_config.json
echo   }>> proxy_config.json
echo ^}>> proxy_config.json

echo 代理配置说明已保存到 proxy_config.json
echo.

echo ========================================
echo 启动HTTP服务器...
echo ========================================
echo.
echo 服务器地址: http://localhost:8080
echo 测试页面: http://localhost:8080/osg_compatibility_test.html
echo.
echo 服务器启动后：
echo 1. 在浏览器中打开: http://localhost:8080/osg_compatibility_test.html
echo 2. 按F12打开开发者工具查看控制台输出
echo 3. 点击页面上的测试按钮开始测试
echo 4. 使用Ctrl+C停止服务器
echo.

:: 创建测试说明HTML文件
echo ^<!DOCTYPE html^>> test_instructions.html
echo ^<html^>>> test_instructions.html
echo ^<head^>>> test_instructions.html
echo     ^<title^>OSG WebAssembly测试说明^</title^>>> test_instructions.html
echo     ^<meta charset="utf-8"^>>> test_instructions.html
echo     ^<style^>>> test_instructions.html
echo         body { font-family: Arial, sans-serif; margin: 40px; }>> test_instructions.html
echo         .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; }>> test_instructions.html
echo         .info { background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; margin: 20px 0; }>> test_instructions.html
echo         .step { background: #f8f9fa; border-left: 4px solid #007bff; padding: 15px; margin: 10px 0; }>> test_instructions.html
echo         code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }>> test_instructions.html
echo     ^</style^>>> test_instructions.html
echo ^</head^>>> test_instructions.html
echo ^<body^>>> test_instructions.html
echo     ^<h1^>OSG接口兼容性测试 - WebAssembly版^</h1^>>> test_instructions.html
echo     ^<div class="warning"^>>> test_instructions.html
echo         ^<h3^>⚠️ 重要：配置浏览器代理^</h3^>>> test_instructions.html
echo         ^<p^>测试需要访问谷歌地图瓦片，请配置浏览器代理：^</p^>>> test_instructions.html
echo         ^<ul^>>> test_instructions.html
echo             ^<li^>HTTP代理: ^<code^>127.0.0.1:10809^</code^>^</li^>>> test_instructions.html
echo             ^<li^>HTTPS代理: ^<code^>127.0.0.1:10809^</code^>^</li^>>> test_instructions.html
echo         ^</ul^>>> test_instructions.html
echo     ^</div^>>> test_instructions.html
echo     ^<div class="info"^>>> test_instructions.html
echo         ^<h3^>📋 测试步骤^</h3^>>> test_instructions.html
echo         ^<div class="step"^>1. 确认代理已配置^</div^>>> test_instructions.html
echo         ^<div class="step"^>2. 打开 ^<a href="osg_compatibility_test.html"^>OSG兼容性测试页面^</a^>^</div^>>> test_instructions.html
echo         ^<div class="step"^>3. 按F12打开开发者工具^</div^>>> test_instructions.html
echo         ^<div class="step"^>4. 切换到Console标签页^</div^>>> test_instructions.html
echo         ^<div class="step"^>5. 点击测试按钮开始测试^</div^>>> test_instructions.html
echo     ^</div^>>> test_instructions.html
echo ^</body^>>> test_instructions.html
echo ^</html^>>> test_instructions.html

echo 测试说明页面已创建: test_instructions.html
echo.

echo 按任意键启动HTTP服务器...
pause >nul

echo 正在启动Python HTTP服务器...
echo 使用Ctrl+C停止服务器
echo.

:: 启动HTTP服务器
python -m http.server 8080

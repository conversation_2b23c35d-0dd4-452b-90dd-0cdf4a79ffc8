/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2010 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/
// Written by <PERSON>, (C) 2010

#ifndef OSGPARTICLE_BOUNCEOPERATOR
#define OSGPARTICLE_BOUNCEOPERATOR

#include <osgParticle/Particle>
#include <osgParticle/DomainOperator>

namespace osgParticle
{


/** A bounce operator can affect the particle's velocity to make it rebound.
    Refer to <PERSON>'s Particle System API (http://www.particlesystems.org)
*/
class OSGPARTICLE_EXPORT BounceOperator : public DomainOperator
{
public:
    BounceOperator()
    : DomainOperator(), _friction(1.0f), _resilience(0.0f), _cutoff(0.0f)
    {}

    BounceOperator( const BounceOperator& copy, const osg::CopyOp& copyop = osg::CopyOp::SHALLOW_COPY )
    :   DomainOperator(copy, copyop),
        _friction(copy._friction), _resilience(copy._resilience), _cutoff(copy._cutoff)
    {}

    META_Object( osgParticle, BounceOperator );

    /// Set the friction
    void setFriction( float f ) { _friction = f; }

    /// Get the friction
    float getFriction() const { return _friction; }

    /// Set the resilience
    void setResilience( float r ) { _resilience = r; }

    /// Get the velocity cutoff factor
    float getResilience() const { return _resilience; }

    /// Set the velocity cutoff factor
    void setCutoff( float v ) { _cutoff = v; }

    /// Get the velocity cutoff factor
    float getCutoff() const { return _cutoff; }

protected:
    virtual ~BounceOperator() {}
    BounceOperator& operator=( const BounceOperator& ) { return *this; }

    virtual void handleTriangle( const Domain& domain, Particle* P, double dt );
    virtual void handleRectangle( const Domain& domain, Particle* P, double dt );
    virtual void handlePlane( const Domain& domain, Particle* P, double dt );
    virtual void handleSphere( const Domain& domain, Particle* P, double dt );
    virtual void handleDisk( const Domain& domain, Particle* P, double dt );

    float _friction;
    float _resilience;
    float _cutoff;
};


}

#endif

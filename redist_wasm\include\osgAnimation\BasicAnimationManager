/*  -*-c++-*-
 *  Copyright (C) 2008 <PERSON><PERSON> <<EMAIL>>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSGANIMATION_BASIC_ANIMATION_MANAGER_H
#define OSGANIMATION_BASIC_ANIMATION_MANAGER_H

#include <osg/Group>
#include <osgAnimation/AnimationManagerBase>
#include <osgAnimation/Export>
#include <osg/FrameStamp>

namespace osgAnimation
{
    class OSGANIMATION_EXPORT BasicAnimationManager : public AnimationManagerBase
    {
    public:

        META_Object(osgAnimation, BasicAnimationManager);

        BasicAnimationManager();
        BasicAnimationManager(const BasicAnimationManager& b, const osg::CopyOp& copyop = osg::CopyOp::SHALLOW_COPY);
        BasicAnimationManager(const AnimationManagerBase& b, const osg::CopyOp& copyop = osg::CopyOp::SHALLOW_COPY);
        virtual ~BasicAnimationManager();

        void update (double time);

        void playAnimation (Animation* pAnimation, int priority = 0, float weight = 1.0);
        bool stopAnimation (Animation* pAnimation);

        bool findAnimation (Animation* pAnimation);
        bool isPlaying (Animation* pAnimation);
        bool isPlaying (const std::string& animationName);

        void stopAll();

    protected:
        typedef std::map<int, AnimationList > AnimationLayers;
        AnimationLayers _animationsPlaying;
        double _lastUpdate;
    };

}
#endif

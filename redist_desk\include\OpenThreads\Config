/* -*-c++-*- OpenSceneGraph - Copyright (C) 2008 <PERSON> 
 *
 * This library is open source and may be redistributed and/or modified under  
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or 
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 * 
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the 
 * OpenSceneGraph Public License for more details.
*/

/****************************************************************************
 * THIS FILE IS AUTOGENERATED BY CMAKE. DO NOT EDIT!
 ****************************************************************************/

/* Changes to the configuration reflected here can be made with ccmake on
 * unix or with cmake-gui on windows. Alternatively you can use cmake's -D
 * or -P switches to set some configuration values at cmake configuration time.
 */

#ifndef _OPENTHREADS_CONFIG
#define _OPENTHREADS_CONFIG

/* #undef _OPENTHREADS_ATOMIC_USE_GCC_BUILTINS */
/* #undef _OPENTHREADS_ATOMIC_USE_MIPOSPRO_BUILTINS */
/* #undef _OPENTHREADS_ATOMIC_USE_SUN */
#define _OPENTHREADS_ATOMIC_USE_WIN32_INTERLOCKED
/* #undef _OPENTHREADS_ATOMIC_USE_BSD_ATOMIC */
/* #undef _OPENTHREADS_ATOMIC_USE_MUTEX */
/* #undef OT_LIBRARY_STATIC */

#endif

/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/
//osgParticle - Copyright (C) 2002 <PERSON>

#ifndef OSGPARTICLE_ANGULARACCELOPERATOR
#define OSGPARTICLE_ANGULARACCELOPERATOR 1

#include <osgParticle/ModularProgram>
#include <osgParticle/Operator>
#include <osgParticle/Particle>

#include <osg/CopyOp>
#include <osg/Object>
#include <osg/Vec3>

namespace osgParticle
{

    /**    An operator class that applies a constant angular acceleration to
     *     the particles.
     */
    class AngularAccelOperator: public Operator {
    public:
        inline AngularAccelOperator();
        inline AngularAccelOperator(const AngularAccelOperator& copy, const osg::CopyOp& copyop = osg::CopyOp::SHALLOW_COPY);

        META_Object(osgParticle, AngularAccelOperator);

        /// Get the angular acceleration vector.
        inline const osg::Vec3& getAngularAcceleration() const;

        /// Set the angular acceleration vector.
        inline void setAngularAcceleration(const osg::Vec3& v);

        /// Apply the angular acceleration to a particle. Do not call this method manually.
        inline void operate(Particle* P, double dt);

        /// Perform some initializations. Do not call this method manually.
        inline void beginOperate(Program *prg);

    protected:
        virtual ~AngularAccelOperator() {}
        AngularAccelOperator& operator=(const AngularAccelOperator& ) { return *this; }

    private:
        osg::Vec3 _angul_araccel;
        osg::Vec3 _xf_angul_araccel;
    };

    // INLINE FUNCTIONS

    inline AngularAccelOperator::AngularAccelOperator()
    : Operator(), _angul_araccel(0, 0, 0)
    {
    }

    inline AngularAccelOperator::AngularAccelOperator(const AngularAccelOperator& copy, const osg::CopyOp& copyop)
    : Operator(copy, copyop), _angul_araccel(copy._angul_araccel)
    {
    }

    inline const osg::Vec3& AngularAccelOperator::getAngularAcceleration() const
    {
        return _angul_araccel;
    }

    inline void AngularAccelOperator::setAngularAcceleration(const osg::Vec3& v)
    {
        _angul_araccel = v;
    }

    inline void AngularAccelOperator::operate(Particle* P, double dt)
    {
        P->addAngularVelocity(_xf_angul_araccel * dt);
    }

    inline void AngularAccelOperator::beginOperate(Program *prg)
    {
        if (prg->getReferenceFrame() == ModularProgram::RELATIVE_RF) {
            _xf_angul_araccel = prg->rotateLocalToWorld(_angul_araccel);
        } else {
            _xf_angul_araccel = _angul_araccel;
        }
    }

}


#endif

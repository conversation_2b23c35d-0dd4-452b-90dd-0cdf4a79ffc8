1 VERSIONINFO
 FILEVERSION 3, 3, 1, 21
 PRODUCTVERSION 3, 3, 1, 21
 FILEFLAGSMASK 0x17L
#ifdef _DEBUG
 FILEFLAGS 0x1L
#else
 FILEFLAGS 0x0L
#endif
 FILEOS 0x4L
 FILETYPE 0x0L
 FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904b0"
        BEGIN
            VALUE "FileDescription", "OPENTHREADS Binary"
            VALUE "FileVersion", "3, 3, 1, 21"
            VALUE "InternalName", "OSG"
            VALUE "LegalCopyright", "Copyright (C) 2009"
            VALUE "OriginalFilename", ""
            VALUE "ProductName", "OPENTHREADS"
            VALUE "ProductVersion", "3, 3, 1, 21"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1200
    END
END

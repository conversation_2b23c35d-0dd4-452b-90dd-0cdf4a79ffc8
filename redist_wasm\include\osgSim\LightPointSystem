/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2004 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSGSIM_LIGHTPOINTSYSTEM
#define OSGSIM_LIGHTPOINTSYSTEM 1

#include <osg/Object>


namespace osgSim {


/*
 * LightPointSYSTEM encapsulates animation and intensity state in a single object
 *   that can be shared by several osgSim::LightPointNodes, thereby allowing an
 *   application to efficiently control the animation/intensity state of
 *   several LightPointNodes.
 */
class LightPointSystem : public osg::Object
{
    public :
        LightPointSystem() : _intensity( 1.f ), _animationState( ANIMATION_ON )
            { }

        /** Copy constructor using CopyOp to manage deep vs shallow copy.*/
        LightPointSystem( const LightPointSystem& lps, const osg::CopyOp& copyop=osg::CopyOp::SHALLOW_COPY ) :
            osg::Object( lps, copyop ), _intensity( lps._intensity ), _animationState( lps._animationState )
            { }

        META_Object( osgSim, LightPointSystem );

        typedef enum {
            ANIMATION_ON,
            ANIMATION_OFF,
            ANIMATION_RANDOM
        } AnimationState;

        void setIntensity( float intensity ) { _intensity = intensity; }
        float getIntensity() const { return _intensity; }

        void setAnimationState( LightPointSystem::AnimationState state ) { _animationState = state; }
        LightPointSystem::AnimationState getAnimationState() const { return _animationState; }

    protected:
        ~LightPointSystem() {}

        float _intensity;
        AnimationState _animationState;
};

}

#endif

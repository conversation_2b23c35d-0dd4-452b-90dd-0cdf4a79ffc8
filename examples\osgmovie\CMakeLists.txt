# INCLUDE_DIRECTORIES( ${OPENAL_INCLUDE_DIR} )
# SET(TARGET_EXTERNAL_LIBRARIES ${OPENAL_LIBRARY} alut)

IF   (SDL2_FOUND)

    SET(TARGET_EXTERNAL_LIBRARIES ${SDL2_LIBRARY} )
    INCLUDE_DIRECTORIES(${SDL2_INCLUDE_DIR} )
    ADD_DEFINITIONS(-DUSE_SDL2)
    IF (MINGW)
        SET(TARGET_EXTERNAL_LIBRARIES ${TARGET_EXTERNAL_LIBRARIES} winmm dinput ddraw dxguid)
    ENDIF()

ELSEIF (SDL_FOUND)

    SET(TARGET_EXTERNAL_LIBRARIES ${SDL_LIBRARY} )
    INCLUDE_DIRECTORIES(${SDL_INCLUDE_DIR} )
    ADD_DEFINITIONS(-DUSE_SDL)
    IF (MINGW)
        SET(TARGET_EXTERNAL_LIBRARIES ${TARGET_EXTERNAL_LIBRARIES} winmm dinput ddraw dxguid)
    ENDIF()

ENDIF()

SET(TARGET_SRC osgmovie.cpp )
SET(TARGET_ADDED_LIBRARIES osgGA )



#### end var setup  ###
SETUP_EXAMPLE(osgmovie)

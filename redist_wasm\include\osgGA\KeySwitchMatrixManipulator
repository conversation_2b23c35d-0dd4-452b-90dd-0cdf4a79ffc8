/* -*-c++-*- OpenSceneGraph - Copyright (C) 1998-2006 <PERSON>
 *
 * This library is open source and may be redistributed and/or modified under
 * the terms of the OpenSceneGraph Public License (OSGPL) version 0.0 or
 * (at your option) any later version.  The full license is in LICENSE file
 * included with this distribution, and on the openscenegraph.org website.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * OpenSceneGraph Public License for more details.
*/

#ifndef OSGUTIL_KEYSWITCMATRIXMANIPULATOR
#define OSGUTIL_KEYSWITCMATRIXMANIPULATOR 1

#include <osgGA/Export>
#include <osgGA/CameraManipulator>
#include <osgGA/GUIEventHandler>

namespace osgGA{

class GUIActionAdapter;

/**
KeySwitchMatrixManipulator is a decorator which allows the type of camera manipulator
being used to be switched by pressing a key. E.g. '1' for a TrackballManipulator,
'2' for a DriveManipulator, '3' for a FlightManipulator. The manipulators available,
and the associated switch keys, can be configured.
*/
class OSGGA_EXPORT KeySwitchMatrixManipulator : public CameraManipulator
{
    public:

        typedef std::pair<std::string, osg::ref_ptr<CameraManipulator> > NamedManipulator;
        typedef std::map<int, NamedManipulator> KeyManipMap;

        virtual const char* className() const { return "KeySwitchMatrixManipulator"; }

        /**
        Add a camera manipulator with an associated name, and a key to
        trigger the switch,
        */
        void addMatrixManipulator(int key, std::string name, CameraManipulator *cm);

        /**
        Add a camera manipulator with an autogenerated keybinding which is '1' + previous number of camera's registered.
        */
        void addNumberedMatrixManipulator(CameraManipulator *cm);

        unsigned int getNumMatrixManipulators() const { return _manips.size(); }

        void selectMatrixManipulator(unsigned int num);

        /** Get the complete list of manipulators attached to this keyswitch manipulator.*/
        KeyManipMap& getKeyManipMap() { return _manips; }

        /** Get the const complete list of manipulators attached to this keyswitch manipulator.*/
        const KeyManipMap& getKeyManipMap() const { return _manips; }


        /** Get the current active manipulators.*/
        CameraManipulator* getCurrentMatrixManipulator() { return _current.get(); }

        /** Get the const current active manipulators.*/
        const CameraManipulator* getCurrentMatrixManipulator() const { return _current.get(); }


        /** Get manipulator assigned to a specified index.*/
        CameraManipulator* getMatrixManipulatorWithIndex(unsigned int key);

        /** Get const manipulator assigned to a specified index.*/
        const CameraManipulator* getMatrixManipulatorWithIndex(unsigned int key) const;

        /** Get manipulator assigned to a specified key.*/
        CameraManipulator* getMatrixManipulatorWithKey(unsigned int key);

        /** Get const manipulator assigned to a specified key.*/
        const CameraManipulator* getMatrixManipulatorWithKey(unsigned int key) const;


        // Overrides from CameraManipulator...

        /** set the coordinate frame which callback tells the manipulator which way is up, east and north.*/
        virtual void setCoordinateFrameCallback(CoordinateFrameCallback* cb);

        /** Set the position of the matrix manipulator using a 4x4 Matrix.*/
        virtual void setByMatrix(const osg::Matrixd& matrix) { _current->setByMatrix(matrix); }

        /** set the position of the matrix manipulator using a 4x4 Matrix.*/
        virtual void setByInverseMatrix(const osg::Matrixd& matrix) { _current->setByInverseMatrix(matrix); }

        /** get the position of the manipulator as 4x4 Matrix.*/
        virtual osg::Matrixd getMatrix() const { return _current->getMatrix(); }

        /** get the position of the manipulator as a inverse matrix of the manipulator, typically used as a model view matrix.*/
        virtual osg::Matrixd getInverseMatrix() const { return _current->getInverseMatrix(); }

        /** Get the FusionDistanceMode. Used by SceneView for setting up stereo convergence.*/
        virtual osgUtil::SceneView::FusionDistanceMode getFusionDistanceMode() const { return _current->getFusionDistanceMode(); }

        /** Get the FusionDistanceValue. Used by SceneView for setting up stereo convergence.*/
        virtual float getFusionDistanceValue() const { return _current->getFusionDistanceValue(); }


        virtual void setNode(osg::Node* n);

        virtual const osg::Node* getNode() const        { return _current->getNode(); }

        virtual osg::Node* getNode()                    { return _current->getNode(); }

        virtual void setHomePosition(const osg::Vec3d& eye, const osg::Vec3d& center, const osg::Vec3d& up, bool autoComputeHomePosition=false);

        virtual void setAutoComputeHomePosition(bool flag);

        virtual void computeHomePosition();

        virtual void finishAnimation();

        virtual void home(const GUIEventAdapter& ee,GUIActionAdapter& aa);

        virtual void init(const GUIEventAdapter& ee,GUIActionAdapter& aa) { if (_current.valid()) _current->init(ee,aa); }

        virtual bool handle(const GUIEventAdapter& ea,GUIActionAdapter& us);

        /** Get the keyboard and mouse usage of this manipulator.*/
        virtual void getUsage(osg::ApplicationUsage& usage) const;

    private:

        KeyManipMap _manips;

        osg::ref_ptr<CameraManipulator> _current;
};

}

#endif
